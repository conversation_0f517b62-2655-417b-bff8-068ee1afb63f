
import { CheckCircle } from "lucide-react";

const Features = () => {
  const features = [
    {
      title: "Progressive Curriculum",
      description: "Age-appropriate activities designed to develop cognitive, social, and emotional skills.",
      color: "bg-promise-100 text-promise-600"
    },
    {
      title: "Qualified Educators",
      description: "Experienced teachers dedicated to nurturing each child's unique potential.",
      color: "bg-meadow-100 text-meadow-600"
    },
    {
      title: "Safe Environment",
      description: "Secure facilities with modern safety protocols to give parents peace of mind.",
      color: "bg-sunlight-100 text-sunlight-700"
    },
    {
      title: "Creative Spaces",
      description: "Purpose-built areas that encourage exploration, imagination, and hands-on learning.",
      color: "bg-peach-100 text-peach-600"
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800">Why Choose Promise Academy?</h2>
          <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
            Our approach to early childhood education combines the best practices with innovative methods.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div 
              key={index}
              className="bg-white p-6 rounded-xl shadow-soft card-hover"
            >
              <div className={`inline-flex items-center justify-center p-3 rounded-lg ${feature.color} mb-5`}>
                <CheckCircle className="h-6 w-6" />
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-3">{feature.title}</h3>
              <p className="text-gray-600">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;
