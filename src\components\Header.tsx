
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Menu } from "lucide-react";
import { useState } from "react";

interface HeaderProps {
  onAboutClick?: () => void;
  onProgramsClick?: () => void;
  onContactClick?: () => void;
}

const Header = ({ onAboutClick, onProgramsClick, onContactClick }: HeaderProps) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const isHomePage = window.location.pathname === "/";

  return (
    <header className="bg-white shadow-soft sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          <Link to="/" className="flex items-center space-x-2">
            <div className="h-10 w-10 bg-promise-500 rounded-lg flex items-center justify-center">
              <span className="text-xl font-bold text-white">P</span>
            </div>
            <span className="text-xl font-quicksand font-bold text-promise-800">Promise Academy</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {isHomePage ? (
              <>
                <button 
                  onClick={onAboutClick} 
                  className="font-medium text-gray-600 hover:text-promise-600 transition-colors"
                >
                  About
                </button>
                <button 
                  onClick={onProgramsClick} 
                  className="font-medium text-gray-600 hover:text-promise-600 transition-colors"
                >
                  Programs
                </button>
                <button 
                  onClick={onContactClick} 
                  className="font-medium text-gray-600 hover:text-promise-600 transition-colors"
                >
                  Contact
                </button>
              </>
            ) : (
              <>
                <Link to="/" className="font-medium text-gray-600 hover:text-promise-600 transition-colors">Home</Link>
              </>
            )}
            <Button asChild className="bg-promise-500 hover:bg-promise-600 button-hover">
              <Link to="/login">Log In</Link>
            </Button>
          </nav>

          {/* Mobile Menu Button */}
          <button 
            className="md:hidden rounded-md p-2 text-gray-600 hover:bg-gray-100 hover:text-gray-900" 
            onClick={toggleMenu}
          >
            <Menu className="h-6 w-6" />
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t">
            <nav className="flex flex-col space-y-4">
              {isHomePage ? (
                <>
                  <button 
                    onClick={() => {
                      onAboutClick?.();
                      setIsMenuOpen(false);
                    }} 
                    className="font-medium text-gray-600 hover:text-promise-600 transition-colors px-2 py-1"
                  >
                    About
                  </button>
                  <button 
                    onClick={() => {
                      onProgramsClick?.();
                      setIsMenuOpen(false);
                    }} 
                    className="font-medium text-gray-600 hover:text-promise-600 transition-colors px-2 py-1"
                  >
                    Programs
                  </button>
                  <button 
                    onClick={() => {
                      onContactClick?.();
                      setIsMenuOpen(false);
                    }} 
                    className="font-medium text-gray-600 hover:text-promise-600 transition-colors px-2 py-1"
                  >
                    Contact
                  </button>
                </>
              ) : (
                <Link 
                  to="/" 
                  className="font-medium text-gray-600 hover:text-promise-600 transition-colors px-2 py-1"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Home
                </Link>
              )}
              <Button 
                asChild 
                className="bg-promise-500 hover:bg-promise-600 button-hover w-full justify-center"
                onClick={() => setIsMenuOpen(false)}
              >
                <Link to="/login">Log In</Link>
              </Button>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
