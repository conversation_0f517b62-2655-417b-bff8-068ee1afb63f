
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON><PERSON> } from "lucide-react";

const Hero = () => {
  return (
    <div className="relative overflow-hidden bg-gradient-to-b from-blue-50 to-white py-12 md:py-20">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 gap-12 lg:grid-cols-2 lg:gap-8 items-center">
          <div className="flex flex-col space-y-8">
            <div>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-quicksand font-bold text-promise-800 leading-tight tracking-tight">
                <span className="text-promise-500">Promise</span> Academy
              </h1>
              <p className="mt-4 text-xl md:text-2xl text-gray-600 leading-relaxed">
                Nurturing bright minds and cultivating tomorrow's leaders through compassion, play, and discovery.
              </p>
            </div>
            
            <div className="flex flex-wrap gap-4 mt-8">
              <Button 
                asChild
                className="bg-promise-500 hover:bg-promise-600 text-white text-lg px-8 py-6 h-auto button-hover"
              >
                <Link to="/login">
                  Parent Portal
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              
              <Button 
                asChild
                variant="outline"
                className="border-promise-500 text-promise-700 hover:text-promise-800 text-lg px-8 py-6 h-auto button-hover"
              >
                <Link to="/login">
                  Staff Login
                </Link>
              </Button>
            </div>
            
            <div className="flex flex-wrap gap-4 items-center mt-8">
              <div className="flex -space-x-4">
                <div className="w-10 h-10 rounded-full bg-meadow-300 flex items-center justify-center text-meadow-800 font-bold text-xs">98%</div>
                <div className="w-10 h-10 rounded-full bg-sunlight-300 flex items-center justify-center text-sunlight-900 font-bold text-xs">A+</div>
                <div className="w-10 h-10 rounded-full bg-peach-300 flex items-center justify-center text-peach-800 font-bold text-xs">5★</div>
              </div>
              <span className="text-gray-600">Trusted by hundreds of families</span>
            </div>
          </div>
          
          <div className="relative">
            <div className="absolute -top-16 -right-16 w-64 h-64 bg-sunlight-200 rounded-full opacity-50 blur-3xl" />
            <div className="absolute -bottom-20 -left-20 w-80 h-80 bg-meadow-200 rounded-full opacity-50 blur-3xl" />
            
            <div className="relative z-10 illustration-container bg-white shadow-card p-6 rounded-2xl">
              <img 
                src="https://images.unsplash.com/photo-1518495973542-4542c06a5843" 
                alt="Happy children learning together" 
                className="w-full h-auto rounded-xl shadow-soft"
              />
              
              <div className="absolute -right-5 -top-5 bg-white p-3 rounded-xl shadow-soft animate-float">
                <div className="bg-meadow-100 p-3 rounded-lg">
                  <span className="font-quicksand font-bold text-meadow-600">
                    Nurturing Environment
                  </span>
                </div>
              </div>
              
              <div className="absolute -left-5 -bottom-5 bg-white p-3 rounded-xl shadow-soft animate-bounce-subtle">
                <div className="bg-promise-100 p-3 rounded-lg">
                  <span className="font-quicksand font-bold text-promise-600">
                    Creative Learning
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;
