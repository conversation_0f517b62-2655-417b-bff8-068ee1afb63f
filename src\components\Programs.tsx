
import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";

const Programs = () => {
  const programs = [
    {
      title: "Infant Care",
      ageRange: "6-18 months",
      description: "Nurturing care in a safe, stimulating environment tailored to support critical early development milestones.",
      features: ["Small group sizes (4:1 ratio)", "Personalized care routines", "Sensory-rich play experiences"],
      color: "bg-promise-500"
    },
    {
      title: "Toddlers",
      ageRange: "18-36 months",
      description: "Activity-based learning that encourages exploration, language development, and early social skills.",
      features: ["Language development focus", "Toilet training support", "Structured play activities"],
      color: "bg-meadow-500"
    },
    {
      title: "Preschool",
      ageRange: "3-4 years",
      description: "Comprehensive curriculum introducing early literacy, numeracy, science, and creative arts.",
      features: ["Introduction to structured learning", "Regular outdoor activities", "Weekly themed projects"],
      color: "bg-sunlight-600"
    },
    {
      title: "Pre-Kindergarten",
      ageRange: "4-5 years",
      description: "School readiness program developing academic and social skills needed for a successful kindergarten transition.",
      features: ["Advanced literacy and math skills", "Social-emotional development", "Independence and responsibility"],
      color: "bg-peach-500"
    }
  ];

  return (
    <section id="programs" className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800">Our Programs</h2>
          <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
            Age-appropriate educational experiences designed to nurture your child's development.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {programs.map((program, index) => (
            <Card key={index} className="overflow-hidden card-hover">
              <div className={`h-2 ${program.color}`}></div>
              <CardHeader>
                <CardTitle>{program.title}</CardTitle>
                <CardDescription>{program.ageRange}</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">{program.description}</p>
                <ul className="space-y-2">
                  {program.features.map((feature, idx) => (
                    <li key={idx} className="flex items-start text-sm">
                      <span className={`h-2 w-2 ${program.color} rounded-full mt-1.5 mr-2 flex-shrink-0`}></span>
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">Learn More</Button>
              </CardFooter>
            </Card>
          ))}
        </div>
        
        <div className="mt-12 text-center">
          <Button className="bg-promise-500 hover:bg-promise-600">
            View Full Curriculum
          </Button>
        </div>
      </div>
    </section>
  );
};

export default Programs;
