
import { Star } from "lucide-react";

const Testimonials = () => {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "<PERSON><PERSON>",
      image: "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&h=200&q=80",
      quote: "Promise Academy has been a blessing for our family. The teachers truly care about each child and their development. My son has flourished beyond my expectations.",
    },
    {
      name: "<PERSON>",
      role: "Parent",
      image: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&h=200&q=80",
      quote: "The communication between teachers and parents is outstanding. I always know what my daughter is learning and how I can support her growth at home.",
    },
    {
      name: "<PERSON>",
      role: "Education Specialist",
      image: "https://images.unsplash.com/photo-1534751516642-a1af1ef26a56?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&h=200&q=80",
      quote: "As someone who works in education, I'm impressed by the thoughtful curriculum and attention to child development principles at Promise Academy.",
    }
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800">What Families Say</h2>
          <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
            Don't just take our word for it—hear from the families who trust us with their children every day.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div 
              key={index}
              className="bg-white p-6 rounded-xl shadow-soft card-hover flex flex-col"
            >
              <div className="flex gap-2 mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                ))}
              </div>
              
              <blockquote className="flex-grow">
                <p className="text-gray-600 italic mb-6">"{testimonial.quote}"</p>
              </blockquote>
              
              <div className="flex items-center space-x-4">
                <div className="h-12 w-12 rounded-full overflow-hidden">
                  <img 
                    src={testimonial.image} 
                    alt={testimonial.name}
                    className="h-full w-full object-cover" 
                  />
                </div>
                <div>
                  <p className="font-bold text-gray-800">{testimonial.name}</p>
                  <p className="text-gray-500 text-sm">{testimonial.role}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
