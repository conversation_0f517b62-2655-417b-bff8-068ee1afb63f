
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { signIn, getUserProfile } from "@/lib/auth";
import type { UserRole } from "@/lib/auth";
import { Loader2, Crown } from "lucide-react";



const LoginForm = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [role, setRole] = useState<UserRole>("parent");
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Sign in with Firebase
      const { user, error } = await signIn(email, password);

      if (error) {
        throw new Error(error);
      }

      if (user) {
        // Fetch user profile to get role and other details
        const profile = await getUserProfile(user.uid);

        if (!profile) {
          throw new Error("User profile not found");
        }

        // Store user info in local storage for quick access
        localStorage.setItem("user", JSON.stringify({
          email: user.email,
          role: profile.role,
          name: `${profile.firstName} ${profile.lastName}`,
          id: user.uid
        }));

        toast({
          title: "Login successful",
          description: `Welcome back, ${profile.firstName}!`,
        });

        navigate(`/dashboard/${profile.role}`);
      }
    } catch (error: any) {
      toast({
        title: "Login failed",
        description: error.message || "Failed to login. Please check your credentials.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full max-w-sm sm:max-w-md lg:max-w-lg mx-auto px-4 sm:px-0">
      {/* Responsive Luxury Glass-morphism Card */}
      <div className="bg-gradient-to-br from-luxury-purple/20 to-luxury-purple/10 backdrop-blur-md border border-luxury-gold/30 rounded-2xl sm:rounded-3xl shadow-luxury-gold p-6 sm:p-8">
        {/* Responsive Header with Cormorant Garamond */}
        <div className="text-center mb-6 sm:mb-8">
          <h1 className="text-3xl sm:text-4xl font-cormorant font-semibold mb-3 sm:mb-4">
            <span className="bg-gradient-to-r from-luxury-gold via-white to-luxury-red bg-clip-text text-transparent">
              Promise Academy
            </span>
          </h1>
          <p className="text-white/90 font-montserrat text-base sm:text-lg">
            Enter your credentials to access your VIP portal
          </p>
        </div>
        {/* Responsive Luxury Form */}
        <form onSubmit={handleSubmit} className="space-y-5 sm:space-y-6">
          <div className="space-y-2 sm:space-y-3">
            <label htmlFor="role" className="block text-luxury-purple font-montserrat font-medium text-sm">
              I am a
            </label>
            <Select
              value={role}
              onValueChange={(value) => setRole(value as UserRole)}
            >
              <SelectTrigger id="role" className="w-full bg-white/10 border-luxury-gold/30 text-white rounded-lg sm:rounded-xl py-3 px-4 focus:border-luxury-gold focus:ring-2 focus:ring-luxury-gold/20 transition-all duration-300 text-sm sm:text-base">
                <SelectValue placeholder="Select your role" />
              </SelectTrigger>
              <SelectContent className="bg-luxury-purple/90 backdrop-blur-md border-luxury-gold/30 rounded-lg sm:rounded-xl">
                <SelectItem value="parent" className="text-white hover:bg-luxury-gold/20 text-sm sm:text-base">Parent</SelectItem>
                <SelectItem value="teacher" className="text-white hover:bg-luxury-gold/20 text-sm sm:text-base">Teacher</SelectItem>
                <SelectItem value="admin" className="text-white hover:bg-luxury-gold/20 text-sm sm:text-base">Administrator</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-3">
            <label htmlFor="email" className="block text-luxury-purple font-montserrat font-medium text-sm">
              Email Address
            </label>
            <input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="w-full bg-white/10 border border-luxury-gold/30 text-white rounded-xl py-3 px-4 placeholder-white/50 focus:border-luxury-gold focus:ring-2 focus:ring-luxury-gold/20 focus:outline-none transition-all duration-300"
            />
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label htmlFor="password" className="block text-luxury-purple font-montserrat font-medium text-sm">
                Password
              </label>
              <a
                href="#"
                className="text-sm text-luxury-gold hover:text-luxury-red font-montserrat transition-colors duration-300"
              >
                Forgot password?
              </a>
            </div>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className="w-full bg-white/10 border border-luxury-gold/30 text-white rounded-xl py-3 px-4 placeholder-white/50 focus:border-luxury-gold focus:ring-2 focus:ring-luxury-gold/20 focus:outline-none transition-all duration-300"
            />
          </div>
          
          {/* Luxury Login Button with Red Accents */}
          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-luxury-gold via-luxury-purple to-luxury-red text-white font-montserrat font-semibold text-lg py-4 rounded-xl shadow-luxury-red hover:scale-105 hover:shadow-luxury-gold transition-all duration-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-3"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-5 w-5 animate-spin" />
                Signing in...
              </>
            ) : (
              <>
                <Crown className="h-5 w-5" />
                Enter VIP Portal
              </>
            )}
          </button>
        </form>

        {/* Elegant Demo Credentials */}
        <div className="mt-8 p-4 bg-white/5 rounded-xl border border-luxury-gold/20">
          <p className="text-luxury-gold font-montserrat font-medium text-center mb-3">
            Demo Credentials
          </p>
          <div className="space-y-2 text-sm">
            <p className="text-white/80 font-montserrat">
              <span className="text-luxury-purple font-medium">Parent:</span> <EMAIL> / parent123
            </p>
            <p className="text-white/80 font-montserrat">
              <span className="text-luxury-purple font-medium">Teacher:</span> <EMAIL> / teacher123
            </p>
            <p className="text-white/80 font-montserrat">
              <span className="text-luxury-purple font-medium">Admin:</span> <EMAIL> / admin123
            </p>
          </div>
        </div>

        {/* Luxury Footer */}
        <div className="mt-6 text-center">
          <p className="text-xs text-white/70 font-montserrat">
            By logging in, you agree to our{" "}
            <a href="#" className="text-luxury-gold hover:text-luxury-red underline transition-colors duration-300">Terms of Service</a>{" "}
            and{" "}
            <a href="#" className="text-luxury-gold hover:text-luxury-red underline transition-colors duration-300">Privacy Policy</a>.
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;
