
import { ReactNode } from "react";
import Sidebar from "./Sidebar";
import Header from "./Header";
import { useIsMobile } from "@/hooks/use-mobile";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import type { UserRole } from "@/lib/auth";

interface DashboardLayoutProps {
  children: ReactNode;
  role: UserRole;
  title: string;
}

const DashboardLayout = ({ children, role, title }: DashboardLayoutProps) => {
  const isMobile = useIsMobile();

  return (
    <ProtectedRoute requiredRole={role}>
      <div className="flex h-screen bg-gray-50">
        <Sidebar role={role} />
        <div className={`flex-1 flex flex-col transition-all duration-300 ${isMobile ? "" : "md:ml-64"}`}>
          <Header title={title} />
          <main className="flex-1 overflow-y-auto p-4 md:p-6">
            {children}
          </main>
        </div>
      </div>
    </ProtectedRoute>
  );
};

export default DashboardLayout;
