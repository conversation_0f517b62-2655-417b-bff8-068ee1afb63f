
import { Bell, Search, LogOut } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { signOut as firebaseSignOut } from "@/lib/auth";

interface HeaderProps {
  title: string;
}

interface User {
  name: string;
  role: string;
  email: string;
  id?: string;
}

const Header = ({ title }: HeaderProps) => {
  const { profile } = useAuth();
  const { toast } = useToast();

  // Create user object from profile for compatibility with existing UI
  const user = profile ? {
    name: `${profile.firstName} ${profile.lastName}`,
    role: profile.role,
    email: profile.email,
    id: profile.id
  } : null;


  const handleLogout = async () => {
    try {
      // Sign out with Firebase
      const { error } = await firebaseSignOut();

      if (error) {
        throw new Error(error);
      }

      toast({
        title: "Logged out",
        description: "You have been successfully logged out.",
      });

      // Redirect to login page
      window.location.href = "/login";
    } catch (error) {
      console.error("Logout error:", error);
      toast({
        title: "Logout failed",
        description: "An error occurred during logout. Please try again.",
        variant: "destructive",
      });
    }
  };


  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  return (
    <header className="bg-white border-b py-4 px-4 md:px-6 flex justify-between items-center">
      <h1 className="text-2xl font-quicksand font-bold text-gray-800">{title}</h1>
      
      <div className="hidden md:flex bg-gray-100 rounded-lg w-1/3">
        <div className="flex items-center pl-3">
          <Search className="h-4 w-4 text-gray-500" />
        </div>
        <input
          type="text"
          placeholder="Search..."
          className="bg-transparent border-0 py-2 px-3 w-full focus:outline-none focus:ring-0"
        />
      </div>
      
      <div className="flex items-center space-x-4">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="ghost" 
              size="icon"
              className="relative"
            >
              <Bell className="h-5 w-5" />
              <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-80">
            <DropdownMenuLabel>Notifications</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <div className="max-h-80 overflow-y-auto">
              <DropdownMenuItem className="flex flex-col items-start">
                <p className="font-medium">New message from Lisa P.</p>
                <p className="text-sm text-muted-foreground">Question about tomorrow's event</p>
                <p className="text-xs text-muted-foreground mt-1">2 minutes ago</p>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="flex flex-col items-start">
                <p className="font-medium">Attendance updated</p>
                <p className="text-sm text-muted-foreground">Daily attendance report is ready</p>
                <p className="text-xs text-muted-foreground mt-1">1 hour ago</p>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="flex flex-col items-start">
                <p className="font-medium">System update</p>
                <p className="text-sm text-muted-foreground">The system will be updated tonight</p>
                <p className="text-xs text-muted-foreground mt-1">5 hours ago</p>
              </DropdownMenuItem>
            </div>
          </DropdownMenuContent>
        </DropdownMenu>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="ghost" 
              size="sm" 
              className="relative h-8 w-8 rounded-full"
            >
              <div className="h-8 w-8 rounded-full bg-promise-500 flex items-center justify-center text-white text-sm font-medium">
                {user ? getInitials(user.name) : ""}
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>My Account</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="flex flex-col items-start cursor-default">
              <span className="font-medium">{user?.name}</span>
              <span className="text-xs text-muted-foreground">{user?.email}</span>
              <span className="text-xs text-muted-foreground capitalize">{user?.role}</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>Profile</DropdownMenuItem>
            <DropdownMenuItem>Settings</DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleLogout} className="text-red-600 focus:text-red-600">
              <LogOut className="h-4 w-4 mr-2" />
              Logout
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
};

export default Header;
