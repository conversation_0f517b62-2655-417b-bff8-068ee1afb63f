
import { useState } from "react";
import { NavLink, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  Home,
  Users,
  Calendar,
  FileText,
  Book,
  Settings,
  BarChart2,
  LogOut,
  ChevronRight,
  Menu,
  X,
  Coins,
  Archive,
  Heart,
  GraduationCap,
  ClipboardCheck
} from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";

interface SidebarProps {
  role: "admin" | "teacher" | "parent";
}

interface NavItem {
  icon: React.ElementType;
  label: string;
  href: string;
  roles: Array<"admin" | "teacher" | "parent">;
}

const navItems: NavItem[] = [
  {
    icon: Home,
    label: "Dashboard",
    href: "", // Will be constructed dynamically
    roles: ["admin", "teacher", "parent"]
  },
  {
    icon: Users,
    label: "Students",
    href: "/students",
    roles: ["admin"]
  },
  {
    icon: GraduationCap,
    label: "Classroom",
    href: "/classroom",
    roles: ["admin", "teacher"]
  },
  {
    icon: ClipboardCheck,
    label: "Grading",
    href: "/grading",
    roles: ["admin", "teacher"]
  },
  {
    icon: Calendar,
    label: "Schedule",
    href: "/timetable",
    roles: ["admin", "teacher", "parent"]
  },
  {
    icon: Heart,
    label: "Health",
    href: "/health",
    roles: ["parent", "admin"]
  },
  {
    icon: Archive,
    label: "Inventory",
    href: "/inventory",
    roles: ["admin"]
  },
  {
    icon: Coins,
    label: "Financial",
    href: "/financial",
    roles: ["admin"]
  },
  {
    icon: FileText,
    label: "Reports",
    href: "/reports",
    roles: ["admin", "teacher", "parent"]
  },
  {
    icon: Book,
    label: "Curriculum",
    href: "/curriculum",
    roles: ["admin", "teacher"]
  },
  {
    icon: BarChart2,
    label: "Analytics",
    href: "/analytics",
    roles: ["admin"]
  },
  {
    icon: Settings,
    label: "Settings",
    href: "/settings",
    roles: ["admin", "teacher", "parent"]
  }
];

const Sidebar = ({ role }: SidebarProps) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const isMobile = useIsMobile();
  const location = useLocation();

  const toggleSidebar = () => {
    if (isMobile) {
      setIsMobileOpen(!isMobileOpen);
    } else {
      setIsCollapsed(!isCollapsed);
    }
  };

  const closeSidebar = () => {
    if (isMobile) {
      setIsMobileOpen(false);
    }
  };

  const filteredNavItems = navItems.filter(item => item.roles.includes(role));

  const baseUrl = `/dashboard/${role}`;

  const handleLogout = async () => {
    try {
      // Import Firebase signOut function
      const { signOut } = await import("@/lib/auth");

      // Sign out with Firebase
      const { error } = await signOut();

      if (error) {
        console.error("Logout error:", error);
      }

      // Redirect to login page
      window.location.href = "/login";
    } catch (error) {
      console.error("Logout error:", error);
      // Fallback: clear localStorage and redirect
      localStorage.removeItem("user");
      window.location.href = "/login";
    }
  };

  return (
    <>
      {/* Mobile menu button */}
      {isMobile && (
        <Button 
          variant="ghost" 
          size="icon"
          onClick={toggleSidebar}
          className="absolute top-4 left-4 z-50 md:hidden"
        >
          <Menu className="h-6 w-6" />
        </Button>
      )}
      
      {/* Sidebar overlay for mobile */}
      {isMobile && isMobileOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={closeSidebar}
        />
      )}
      
      {/* Sidebar */}
      <aside 
        className={cn(
          "fixed top-0 left-0 h-full z-50 bg-promise-800 text-white transition-all duration-300",
          isMobile 
            ? isMobileOpen
              ? "translate-x-0 w-64" 
              : "-translate-x-full w-64"
            : isCollapsed
              ? "w-16" 
              : "w-64"
        )}
      >
        {/* Logo and collapse button */}
        <div className="flex items-center justify-between h-16 px-4 border-b border-promise-700">
          {!isCollapsed && (
            <div className="flex items-center">
              <div className="h-8 w-8 bg-white rounded-md flex items-center justify-center">
                <span className="text-lg font-bold text-promise-700">P</span>
              </div>
              {!isCollapsed && <span className="ml-2 font-quicksand font-bold">Promise Academy</span>}
            </div>
          )}
          
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={toggleSidebar}
            className="text-white hover:bg-promise-700"
          >
            {isMobile ? <X className="h-4 w-4" /> : <ChevronRight className={cn("h-4 w-4 transition-transform", isCollapsed ? "" : "rotate-180")} />}
          </Button>
        </div>
        
        {/* Navigation */}
        <nav className="p-2">
          <ul className="space-y-1">
            {filteredNavItems.map((item) => (
              <li key={item.label}>
                <NavLink
                  to={`${baseUrl}${item.href}`}
                  className={({ isActive }) => cn(
                    "flex items-center py-2 px-3 rounded-md transition-colors",
                    isActive 
                      ? "bg-promise-600 text-white" 
                      : "text-promise-100 hover:bg-promise-700 hover:text-white",
                    isCollapsed ? "justify-center" : "justify-start"
                  )}
                  onClick={closeSidebar}
                >
                  <item.icon className={cn("h-5 w-5", isCollapsed ? "" : "mr-3")} />
                  {!isCollapsed && <span>{item.label}</span>}
                </NavLink>
              </li>
            ))}
          </ul>
        </nav>
        
        {/* Logout button at bottom */}
        <div className={cn(
          "absolute bottom-4 left-0 right-0 px-2",
          isCollapsed ? "text-center" : ""
        )}>
          <Button
            variant="ghost"
            className={cn(
              "w-full flex items-center py-2 px-3 rounded-md transition-colors text-promise-100 hover:bg-promise-700 hover:text-white",
              isCollapsed ? "justify-center" : "justify-start"
            )}
            onClick={handleLogout}
          >
            <LogOut className={cn("h-5 w-5", isCollapsed ? "" : "mr-3")} />
            {!isCollapsed && <span>Logout</span>}
          </Button>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;
