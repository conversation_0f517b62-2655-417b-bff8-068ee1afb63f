
import { Card, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  description?: string;
  trendValue?: number;
  trendDirection?: "up" | "down" | "neutral";
  className?: string;
}

const StatCard = ({
  title,
  value,
  icon,
  description,
  trendValue,
  trendDirection,
  className,
}: StatCardProps) => {
  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <div className="h-8 w-8 rounded-md bg-muted flex items-center justify-center">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {description && (
          <div className="flex items-center mt-1">
            {trendValue !== undefined && trendDirection && (
              <span
                className={cn(
                  "text-xs font-medium mr-2",
                  trendDirection === "up"
                    ? "text-green-500"
                    : trendDirection === "down"
                    ? "text-red-500"
                    : "text-gray-500"
                )}
              >
                {trendDirection === "up" && "+"}
                {trendValue}%
              </span>
            )}
            <p className="text-xs text-muted-foreground">{description}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default StatCard;
