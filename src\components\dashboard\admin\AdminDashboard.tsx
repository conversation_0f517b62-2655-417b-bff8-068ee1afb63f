import { BarChart2, CheckCircle, Calendar, Clock, Users, FileText, Coins, Archive } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import StatCard from "../StatCard";
import DashboardLayout from "../DashboardLayout";
import { Progress } from "@/components/ui/progress";
import { useLocation, Routes, Route } from "react-router-dom";
import InventoryManagement from "./InventoryManagement";
import FinancialManagement from "./FinancialManagement";
import TimetableManagement from "./TimetableManagement";
import StudentsManagement from "./StudentsManagement";
import ReportsManagement from "./ReportsManagement";
import CurriculumManagement from "./CurriculumManagement";
import AnalyticsManagement from "./AnalyticsManagement";

const AdminDashboardHome = () => {
  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Students"
          value="127"
          icon={<Users className="h-4 w-4 text-muted-foreground" />}
          description="12% increase from last month"
          trendValue={12}
          trendDirection="up"
        />
        <StatCard
          title="Attendance Rate"
          value="93.5%"
          icon={<CheckCircle className="h-4 w-4 text-muted-foreground" />}
          description="2.1% higher than average"
          trendValue={2.1}
          trendDirection="up"
        />
        <StatCard
          title="Active Classes"
          value="8"
          icon={<Calendar className="h-4 w-4 text-muted-foreground" />}
          description="All classes running today"
        />
        <StatCard
          title="Staff Present"
          value="14/15"
          icon={<Clock className="h-4 w-4 text-muted-foreground" />}
          description="One staff member on leave"
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Enrollment by Class</CardTitle>
            <CardDescription>Current enrollment distribution</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-2 rounded-full bg-promise-500" />
                  <span className="text-sm">Infants (6-18 months)</span>
                </div>
                <span className="text-sm font-medium">15/16</span>
              </div>
              <Progress value={(15/16)*100} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-2 rounded-full bg-meadow-500" />
                  <span className="text-sm">Toddlers (18-36 months)</span>
                </div>
                <span className="text-sm font-medium">20/20</span>
              </div>
              <Progress value={100} className="h-2 bg-muted [&>*]:bg-meadow-500" />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-2 rounded-full bg-sunlight-600" />
                  <span className="text-sm">Pre-K (3 years)</span>
                </div>
                <span className="text-sm font-medium">32/35</span>
              </div>
              <Progress value={(32/35)*100} className="h-2 bg-muted [&>*]:bg-sunlight-600" />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-2 rounded-full bg-peach-500" />
                  <span className="text-sm">Pre-K (4 years)</span>
                </div>
                <span className="text-sm font-medium">40/40</span>
              </div>
              <Progress value={100} className="h-2 bg-muted [&>*]:bg-peach-500" />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-2 rounded-full bg-gray-500" />
                  <span className="text-sm">Kindergarten (5 years)</span>
                </div>
                <span className="text-sm font-medium">20/24</span>
              </div>
              <Progress value={(20/24)*100} className="h-2 bg-muted [&>*]:bg-gray-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Recent Activities</CardTitle>
            <CardDescription>Latest notifications and events</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-8">
              <div className="flex">
                <div className="mr-4 flex h-10 w-10 items-center justify-center rounded-lg bg-muted">
                  <Users className="h-5 w-5 text-muted-foreground" />
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium leading-none">New Enrollment</p>
                  <p className="text-sm text-muted-foreground">
                    Emma Thompson has been enrolled in Pre-K (4 years)
                  </p>
                  <p className="text-xs text-muted-foreground">2 hours ago</p>
                </div>
              </div>
              
              <div className="flex">
                <div className="mr-4 flex h-10 w-10 items-center justify-center rounded-lg bg-muted">
                  <FileText className="h-5 w-5 text-muted-foreground" />
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium leading-none">Monthly Report Generated</p>
                  <p className="text-sm text-muted-foreground">
                    April 2025 financial reports are ready for review
                  </p>
                  <p className="text-xs text-muted-foreground">Yesterday</p>
                </div>
              </div>
              
              <div className="flex">
                <div className="mr-4 flex h-10 w-10 items-center justify-center rounded-lg bg-muted">
                  <Calendar className="h-5 w-5 text-muted-foreground" />
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium leading-none">Parent-Teacher Conference</p>
                  <p className="text-sm text-muted-foreground">
                    Scheduled for May 15th-16th, 2025
                  </p>
                  <p className="text-xs text-muted-foreground">2 days ago</p>
                </div>
              </div>
              
              <div className="flex">
                <div className="mr-4 flex h-10 w-10 items-center justify-center rounded-lg bg-muted">
                  <BarChart2 className="h-5 w-5 text-muted-foreground" />
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium leading-none">Budget Update</p>
                  <p className="text-sm text-muted-foreground">
                    Q2 budget has been approved and allocated
                  </p>
                  <p className="text-xs text-muted-foreground">3 days ago</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <div>
        <Card>
          <CardHeader>
            <CardTitle>Upcoming Events</CardTitle>
            <CardDescription>School calendar for the next two weeks</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="bg-muted/50 p-4 rounded-lg border">
                  <div className="flex items-center justify-between mb-2">
                    <div className="bg-promise-100 text-promise-800 px-2 py-1 rounded text-xs font-medium">
                      May 20, 2025
                    </div>
                    <span className="text-xs text-muted-foreground">10:00 AM - 12:00 PM</span>
                  </div>
                  <h4 className="font-medium">Spring Festival</h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    Annual celebration showcasing student performances and art
                  </p>
                </div>
                
                <div className="bg-muted/50 p-4 rounded-lg border">
                  <div className="flex items-center justify-between mb-2">
                    <div className="bg-meadow-100 text-meadow-800 px-2 py-1 rounded text-xs font-medium">
                      May 22, 2025
                    </div>
                    <span className="text-xs text-muted-foreground">1:00 PM - 3:00 PM</span>
                  </div>
                  <h4 className="font-medium">Staff Development</h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    Professional development workshop for all teachers
                  </p>
                </div>
                
                <div className="bg-muted/50 p-4 rounded-lg border">
                  <div className="flex items-center justify-between mb-2">
                    <div className="bg-sunlight-100 text-sunlight-900 px-2 py-1 rounded text-xs font-medium">
                      May 25, 2025
                    </div>
                    <span className="text-xs text-muted-foreground">All Day</span>
                  </div>
                  <h4 className="font-medium">Memorial Day</h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    School closed for Memorial Day holiday
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

const AdminDashboard = () => {
  const location = useLocation();
  const getTitle = () => {
    if (location.pathname.includes("/inventory")) return "Inventory Management";
    if (location.pathname.includes("/financial")) return "Financial Management";
    if (location.pathname.includes("/timetable")) return "Timetable Management";
    if (location.pathname.includes("/students")) return "Students Management";
    if (location.pathname.includes("/reports")) return "Reports Management";
    if (location.pathname.includes("/curriculum")) return "Curriculum Management";
    if (location.pathname.includes("/analytics")) return "Analytics Management";
    if (location.pathname.includes("/health")) return "Health Management";
    return "Admin Dashboard";
  };

  return (
    <DashboardLayout role="admin" title={getTitle()}>
      <Routes>
        <Route path="/" element={<AdminDashboardHome />} />
        <Route path="/inventory" element={<InventoryManagement />} />
        <Route path="/financial" element={<FinancialManagement />} />
        <Route path="/timetable" element={<TimetableManagement />} />
        <Route path="/students" element={<StudentsManagement />} />
        <Route path="/reports" element={<ReportsManagement />} />
        <Route path="/curriculum" element={<CurriculumManagement />} />
        <Route path="/analytics" element={<AnalyticsManagement />} />
      </Routes>
    </DashboardLayout>
  );
};

export default AdminDashboard;
