
import DashboardLayout from "../DashboardLayout";
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Bar<PERSON><PERSON> } from "@/components/ui/charts/BarChart";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { BarChart2, Calendar, Download, ArrowRight } from "lucide-react";

const attendanceData = [
  { month: "Jan", Infants: 92, Toddlers: 91, "Pre-K (3)": 93, "Pre-K (4)": 95, Kindergarten: 94 },
  { month: "Feb", Infants: 91, Todd<PERSON>: 92, "Pre-K (3)": 94, "Pre-K (4)": 96, Kindergarten: 95 },
  { month: "Mar", Infants: 93, Toddlers: 93, "Pre-K (3)": 95, "Pre-K (4)": 94, Kindergarten: 96 },
  { month: "Apr", Infants: 92, Todd<PERSON>: 94, "Pre-K (3)": 96, "Pre-K (4)": 95, Kindergarten: 97 },
  { month: "May", Infants: 94, Toddlers: 95, "Pre-K (3)": 97, "Pre-K (4)": 96, Kindergarten: 98 },
];

const enrollmentData = [
  { month: "Jan", enrolled: 15, capacity: 16, class: "Infants" },
  { month: "Jan", enrolled: 19, capacity: 20, class: "Toddlers" },
  { month: "Jan", enrolled: 30, capacity: 35, class: "Pre-K (3)" },
  { month: "Jan", enrolled: 38, capacity: 40, class: "Pre-K (4)" },
  { month: "Jan", enrolled: 20, capacity: 24, class: "Kindergarten" },
];

const progressData = [
  { 
    class: "Infants",
    "Physical": 85,
    "Cognitive": 80,
    "Social": 82,
    "Language": 78
  },
  { 
    class: "Toddlers",
    "Physical": 87,
    "Cognitive": 84,
    "Social": 86,
    "Language": 83
  },
  { 
    class: "Pre-K (3)",
    "Physical": 90,
    "Cognitive": 88,
    "Social": 91,
    "Language": 89
  },
  { 
    class: "Pre-K (4)",
    "Physical": 92,
    "Cognitive": 91,
    "Social": 94,
    "Language": 93
  },
  { 
    class: "Kindergarten",
    "Physical": 95,
    "Cognitive": 94,
    "Social": 96,
    "Language": 95
  },
];

const staffData = [
  { month: "Jan", present: 95, absent: 5 },
  { month: "Feb", present: 97, absent: 3 },
  { month: "Mar", present: 94, absent: 6 },
  { month: "Apr", present: 96, absent: 4 },
  { month: "May", present: 98, absent: 2 },
];

const AnalyticsManagement = () => {
  return (
    <DashboardLayout role="admin" title="Analytics">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold">School Analytics</h2>
            <p className="text-muted-foreground">Comprehensive data analysis for Promise Academy</p>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Time Period:</span>
              <Select defaultValue="current">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="current">Current Academic Year</SelectItem>
                  <SelectItem value="last">Previous Academic Year</SelectItem>
                  <SelectItem value="custom">Custom Range</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-1" /> Export All Data
            </Button>
          </div>
        </div>

        <Tabs defaultValue="overview">
          <TabsList className="w-full justify-start">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="academic">Academic Performance</TabsTrigger>
            <TabsTrigger value="enrollment">Enrollment</TabsTrigger>
            <TabsTrigger value="staff">Staff</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-5 pt-5">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Students</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">127</div>
                  <p className="text-xs text-muted-foreground">+12 from last year</p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Overall Attendance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">93.5%</div>
                  <p className="text-xs text-muted-foreground">+2.1% from last quarter</p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Utilization Rate</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">88.7%</div>
                  <p className="text-xs text-muted-foreground">127/143 capacity</p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Average Student Progress</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">87.5%</div>
                  <p className="text-xs text-muted-foreground">+3.2% from last assessment</p>
                </CardContent>
              </Card>
            </div>
            
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <div className="space-y-1">
                    <CardTitle>Attendance by Class</CardTitle>
                    <CardDescription>Monthly attendance rate (%)</CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">Jan - May 2025</span>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="h-[350px]">
                    <BarChart
                      data={attendanceData}
                      index="month"
                      categories={["Infants", "Toddlers", "Pre-K (3)", "Pre-K (4)", "Kindergarten"]}
                      colors={["#9b87f5", "#65cf9a", "#f79044", "#3b82f6", "#a855f7"]}
                      valueFormatter={(value) => `${value}%`}
                    />
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="ghost" className="w-full" size="sm">
                    View Detailed Report <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                </CardFooter>
              </Card>
              
              <Card>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <div className="space-y-1">
                    <CardTitle>Student Progress by Domain</CardTitle>
                    <CardDescription>Average achievement level (%)</CardDescription>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="h-[350px]">
                    <BarChart
                      data={progressData}
                      index="class"
                      categories={["Physical", "Cognitive", "Social", "Language"]}
                      colors={["#9b87f5", "#65cf9a", "#f79044", "#3b82f6"]}
                      valueFormatter={(value) => `${value}%`}
                    />
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="ghost" className="w-full" size="sm">
                    View Detailed Report <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                </CardFooter>
              </Card>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <div className="space-y-1">
                    <CardTitle>Enrollment by Class</CardTitle>
                    <CardDescription>Current enrollment vs. capacity</CardDescription>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="h-[350px]">
                    <BarChart
                      data={enrollmentData}
                      index="class"
                      categories={["enrolled", "capacity"]}
                      colors={["#9b87f5", "#e2e2e2"]}
                      valueFormatter={(value) => `${value} students`}
                    />
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="ghost" className="w-full" size="sm">
                    View Enrollment Trends <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                </CardFooter>
              </Card>
              
              <Card>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <div className="space-y-1">
                    <CardTitle>Staff Attendance</CardTitle>
                    <CardDescription>Monthly attendance rate (%)</CardDescription>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="h-[350px]">
                    <BarChart
                      data={staffData}
                      index="month"
                      categories={["present", "absent"]}
                      colors={["#65cf9a", "#ff8c82"]}
                      valueFormatter={(value) => `${value}%`}
                    />
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="ghost" className="w-full" size="sm">
                    View Staff Reports <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="academic" className="pt-5">
            <Card>
              <CardHeader>
                <CardTitle>Academic Performance Analysis</CardTitle>
                <CardDescription>Detailed breakdown of student academic achievement</CardDescription>
              </CardHeader>
              <CardContent className="h-[500px] flex items-center justify-center">
                <div className="flex flex-col items-center">
                  <BarChart2 className="h-12 w-12 mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">Select specific metrics to view detailed academic performance data</p>
                  <Button className="mt-4">Load Academic Data</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="enrollment" className="pt-5">
            <Card>
              <CardHeader>
                <CardTitle>Enrollment Analysis</CardTitle>
                <CardDescription>Detailed breakdown of enrollment trends</CardDescription>
              </CardHeader>
              <CardContent className="h-[500px] flex items-center justify-center">
                <div className="flex flex-col items-center">
                  <BarChart2 className="h-12 w-12 mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">Select specific metrics to view detailed enrollment data</p>
                  <Button className="mt-4">Load Enrollment Data</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="staff" className="pt-5">
            <Card>
              <CardHeader>
                <CardTitle>Staff Analytics</CardTitle>
                <CardDescription>Detailed breakdown of staff performance and attendance</CardDescription>
              </CardHeader>
              <CardContent className="h-[500px] flex items-center justify-center">
                <div className="flex flex-col items-center">
                  <BarChart2 className="h-12 w-12 mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">Select specific metrics to view detailed staff data</p>
                  <Button className="mt-4">Load Staff Data</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default AnalyticsManagement;
