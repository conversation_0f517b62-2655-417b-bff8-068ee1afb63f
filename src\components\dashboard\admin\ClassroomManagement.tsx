
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ArrowUp,
  ArrowDown,
  Book,
  CalendarDays,
  Loader2,
  Plus,
  UserRound,
  Users,
  School,
} from "lucide-react";
import DashboardLayout from "../DashboardLayout";
import { useToast } from "@/hooks/use-toast";
import { studentsService } from "@/lib/firestore";

// Define classroom types
interface Classroom {
  id: string;
  name: string;
  grade_level: string;
  teacher_id: string | null;
  capacity: number;
  description: string | null;
  room_number: string | null;
  academic_year: string;
  is_active: boolean;
  teacher?: {
    first_name: string;
    last_name: string;
  };
  student_count?: number;
}

interface Teacher {
  id: string;
  name: string;
}

interface Student {
  id: string;
  name: string;
  grade_level: string;
  student_id: string;
}

interface Subject {
  id: string;
  name: string;
  grade_level: string;
}

interface ClassroomForm {
  name: string;
  grade_level: string;
  teacher_id: string;
  capacity: number;
  description: string;
  room_number: string;
  academic_year: string;
}

const ClassroomManagement = () => {
  const { toast } = useToast();
  const [classrooms, setClassrooms] = useState<Classroom[]>([]);
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [selectedClassroom, setSelectedClassroom] = useState<Classroom | null>(null);
  const [classroomStudents, setClassroomStudents] = useState<Student[]>([]);
  const [unassignedStudents, setUnassignedStudents] = useState<Student[]>([]);
  const [isAddingClassroom, setIsAddingClassroom] = useState(false);
  const [isAddingStudent, setIsAddingStudent] = useState(false);
  const [loading, setLoading] = useState(true);
  const [selectedStudentIds, setSelectedStudentIds] = useState<string[]>([]);
  
  const [newClassroom, setNewClassroom] = useState<ClassroomForm>({
    name: "",
    grade_level: "",
    teacher_id: "",
    capacity: 20,
    description: "",
    room_number: "",
    academic_year: "2024-2025"
  });
  
  // Fetch data on component mount
  useEffect(() => {
    fetchData();
  }, []);
  
  const fetchData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchClassrooms(),
        fetchTeachers(),
        fetchStudents(),
        fetchSubjects()
      ]);
    } catch (error) {
      console.error("Error fetching data:", error);
      toast({
        title: "Error",
        description: "There was a problem loading the classroom data.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };
  
  const fetchClassrooms = async () => {
    try {
      // Fetch classrooms with teacher data
      const { data, error } = await supabase
        .from('classrooms')
        .select(`
          *,
          teacher:teacher_id (
            first_name,
            last_name
          )
        `)
        .order('name');
        
      if (error) throw error;
      
      // Get student counts for each classroom
      if (data && data.length > 0) {
        const classroomsWithCounts = await Promise.all(
          data.map(async (classroom) => {
            const { count, error: countError } = await supabase
              .from('classroom_students')
              .select('id', { count: 'exact', head: true })
              .eq('classroom_id', classroom.id);
              
            return {
              ...classroom,
              student_count: countError ? 0 : count || 0
            };
          })
        );
        
        setClassrooms(classroomsWithCounts);
        
        // Set the first classroom as selected by default
        if (classroomsWithCounts.length > 0 && !selectedClassroom) {
          setSelectedClassroom(classroomsWithCounts[0]);
          fetchClassroomStudents(classroomsWithCounts[0].id);
        }
      } else {
        setClassrooms([]);
      }
    } catch (error) {
      console.error("Error fetching classrooms:", error);
      toast({
        title: "Error",
        description: "Failed to load classrooms.",
        variant: "destructive"
      });
    }
  };
  
  const fetchTeachers = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, first_name, last_name')
        .eq('role', 'teacher');
        
      if (error) throw error;
      
      const formattedTeachers = data.map(teacher => ({
        id: teacher.id,
        name: `${teacher.first_name} ${teacher.last_name}`
      }));
      
      setTeachers(formattedTeachers);
    } catch (error) {
      console.error("Error fetching teachers:", error);
    }
  };
  
  const fetchStudents = async () => {
    try {
      const studentsData = await studentsService.getAll();
      const activeStudents = studentsData.filter(student => student.isActive);

      const formattedStudents = activeStudents.map(student => ({
        id: student.id,
        name: `${student.firstName} ${student.lastName}`,
        grade_level: student.gradeLevel,
        student_id: student.studentId
      }));

      // Sort by last name
      formattedStudents.sort((a, b) => a.name.split(' ')[1].localeCompare(b.name.split(' ')[1]));

      setStudents(formattedStudents);
    } catch (error) {
      console.error("Error fetching students:", error);
    }
  };
  
  const fetchSubjects = async () => {
    try {
      const { data, error } = await supabase
        .from('subjects')
        .select('id, name, grade_level')
        .order('name');
        
      if (error) throw error;
      
      setSubjects(data || []);
    } catch (error) {
      console.error("Error fetching subjects:", error);
    }
  };
  
  const fetchClassroomStudents = async (classroomId: string) => {
    try {
      // Get students assigned to this classroom
      const { data, error } = await supabase
        .from('classroom_students')
        .select(`
          student_id,
          students (
            id,
            first_name,
            last_name,
            grade_level,
            student_id
          )
        `)
        .eq('classroom_id', classroomId);
        
      if (error) throw error;
      
      const studentList = data.map(item => ({
        id: item.students.id,
        name: `${item.students.first_name} ${item.students.last_name}`,
        grade_level: item.students.grade_level,
        student_id: item.students.student_id
      }));
      
      setClassroomStudents(studentList);
      
      // Get unassigned students (students not in this classroom)
      const assignedIds = studentList.map(s => s.id);
      setUnassignedStudents(students.filter(s => !assignedIds.includes(s.id)));
      
    } catch (error) {
      console.error("Error fetching classroom students:", error);
    }
  };
  
  const handleSelectClassroom = (classroom: Classroom) => {
    setSelectedClassroom(classroom);
    fetchClassroomStudents(classroom.id);
  };
  
  const handleInputChangeClassroom = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setNewClassroom({
      ...newClassroom,
      [id]: id === 'capacity' ? parseInt(value) || 0 : value
    });
  };
  
  const handleSelectChangeClassroom = (field: string, value: string) => {
    setNewClassroom({
      ...newClassroom,
      [field]: value
    });
  };
  
  const handleAddClassroom = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('classrooms')
        .insert({
          name: newClassroom.name,
          grade_level: newClassroom.grade_level,
          teacher_id: newClassroom.teacher_id || null,
          capacity: newClassroom.capacity,
          description: newClassroom.description || null,
          room_number: newClassroom.room_number || null,
          academic_year: newClassroom.academic_year,
          is_active: true
        })
        .select()
        .single();
        
      if (error) throw error;
      
      toast({
        title: "Success",
        description: `Classroom ${newClassroom.name} has been created.`
      });
      
      // Reset form and close dialog
      setNewClassroom({
        name: "",
        grade_level: "",
        teacher_id: "",
        capacity: 20,
        description: "",
        room_number: "",
        academic_year: "2024-2025"
      });
      
      setIsAddingClassroom(false);
      
      // Refresh classrooms
      fetchClassrooms();
      
    } catch (error: any) {
      console.error("Error adding classroom:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to create classroom.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };
  
  const handleToggleStudentSelection = (studentId: string) => {
    if (selectedStudentIds.includes(studentId)) {
      setSelectedStudentIds(selectedStudentIds.filter(id => id !== studentId));
    } else {
      setSelectedStudentIds([...selectedStudentIds, studentId]);
    }
  };
  
  const handleAddStudentsToClassroom = async () => {
    if (!selectedClassroom || selectedStudentIds.length === 0) return;
    
    try {
      setLoading(true);
      
      const studentAssignments = selectedStudentIds.map(studentId => ({
        classroom_id: selectedClassroom.id,
        student_id: studentId
      }));
      
      const { error } = await supabase
        .from('classroom_students')
        .insert(studentAssignments);
        
      if (error) throw error;
      
      toast({
        title: "Success",
        description: `${selectedStudentIds.length} students added to ${selectedClassroom.name}.`
      });
      
      // Reset selection and close dialog
      setSelectedStudentIds([]);
      setIsAddingStudent(false);
      
      // Refresh classroom students
      fetchClassroomStudents(selectedClassroom.id);
      fetchClassrooms(); // To update student counts
      
    } catch (error: any) {
      console.error("Error adding students to classroom:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to add students to classroom.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };
  
  const handleRemoveStudentFromClassroom = async (studentId: string) => {
    if (!selectedClassroom) return;
    
    try {
      setLoading(true);
      
      const { error } = await supabase
        .from('classroom_students')
        .delete()
        .eq('classroom_id', selectedClassroom.id)
        .eq('student_id', studentId);
        
      if (error) throw error;
      
      toast({
        title: "Success",
        description: "Student removed from classroom."
      });
      
      // Refresh classroom students
      fetchClassroomStudents(selectedClassroom.id);
      fetchClassrooms(); // To update student counts
      
    } catch (error: any) {
      console.error("Error removing student from classroom:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to remove student from classroom.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <DashboardLayout role="admin" title="Classroom Management">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Classroom Management</h2>
            <p className="text-gray-600">Manage classrooms, teachers, and student assignments</p>
          </div>
          <Dialog open={isAddingClassroom} onOpenChange={setIsAddingClassroom}>
            <DialogTrigger asChild>
              <Button className="bg-promise-500 hover:bg-promise-600">
                <Plus className="mr-2 h-4 w-4" /> Add Classroom
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Classroom</DialogTitle>
                <DialogDescription>
                  Create a new classroom for the current academic year.
                </DialogDescription>
              </DialogHeader>
              
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Classroom Name</Label>
                    <Input 
                      id="name"
                      placeholder="e.g., Butterfly Class"
                      value={newClassroom.name}
                      onChange={handleInputChangeClassroom}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="grade_level">Grade Level</Label>
                    <Select 
                      value={newClassroom.grade_level} 
                      onValueChange={(value) => handleSelectChangeClassroom("grade_level", value)}
                    >
                      <SelectTrigger id="grade_level">
                        <SelectValue placeholder="Select grade level" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Preschool">Preschool</SelectItem>
                        <SelectItem value="Kindergarten">Kindergarten</SelectItem>
                        <SelectItem value="Grade 1">Grade 1</SelectItem>
                        <SelectItem value="Grade 2">Grade 2</SelectItem>
                        <SelectItem value="Grade 3">Grade 3</SelectItem>
                        <SelectItem value="Grade 4">Grade 4</SelectItem>
                        <SelectItem value="Grade 5">Grade 5</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="teacher">Primary Teacher</Label>
                    <Select 
                      value={newClassroom.teacher_id} 
                      onValueChange={(value) => handleSelectChangeClassroom("teacher_id", value)}
                    >
                      <SelectTrigger id="teacher">
                        <SelectValue placeholder="Assign teacher" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">No teacher assigned</SelectItem>
                        {teachers.map(teacher => (
                          <SelectItem key={teacher.id} value={teacher.id}>
                            {teacher.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="capacity">Capacity</Label>
                    <Input 
                      id="capacity"
                      type="number"
                      value={newClassroom.capacity}
                      onChange={handleInputChangeClassroom}
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="room_number">Room Number</Label>
                    <Input 
                      id="room_number"
                      placeholder="e.g., Room 101"
                      value={newClassroom.room_number}
                      onChange={handleInputChangeClassroom}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="academic_year">Academic Year</Label>
                    <Select 
                      value={newClassroom.academic_year} 
                      onValueChange={(value) => handleSelectChangeClassroom("academic_year", value)}
                    >
                      <SelectTrigger id="academic_year">
                        <SelectValue placeholder="Select year" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="2023-2024">2023-2024</SelectItem>
                        <SelectItem value="2024-2025">2024-2025</SelectItem>
                        <SelectItem value="2025-2026">2025-2026</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Input 
                    id="description"
                    placeholder="Brief description of the classroom"
                    value={newClassroom.description}
                    onChange={handleInputChangeClassroom}
                  />
                </div>
              </div>
              
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddingClassroom(false)}>Cancel</Button>
                <Button onClick={handleAddClassroom} disabled={loading}>
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Adding...
                    </>
                  ) : 'Add Classroom'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader>
              <CardTitle>Total Classrooms</CardTitle>
              <CardDescription>Active classrooms</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{classrooms.length}</div>
              <p className="text-sm text-muted-foreground mt-2">For academic year 2024-2025</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Total Students</CardTitle>
              <CardDescription>Currently enrolled</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{students.length}</div>
              <p className="text-sm text-muted-foreground mt-2">Active students</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Total Teachers</CardTitle>
              <CardDescription>Assigned to classrooms</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{teachers.length}</div>
              <p className="text-sm text-muted-foreground mt-2">Available teachers</p>
            </CardContent>
          </Card>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle>Classrooms</CardTitle>
              <CardDescription>Select a classroom to manage</CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center py-4">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : classrooms.length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">
                  No classrooms found
                </div>
              ) : (
                <div className="space-y-2">
                  {classrooms.map(classroom => (
                    <div
                      key={classroom.id}
                      className={`p-3 rounded-lg cursor-pointer flex items-center justify-between ${
                        selectedClassroom?.id === classroom.id
                          ? "bg-promise-50 border border-promise-200"
                          : "hover:bg-gray-100 border border-transparent"
                      }`}
                      onClick={() => handleSelectClassroom(classroom)}
                    >
                      <div className="flex items-center">
                        <School className="h-5 w-5 mr-2 text-promise-500" />
                        <div>
                          <p className="font-medium">{classroom.name}</p>
                          <p className="text-xs text-muted-foreground">
                            {classroom.grade_level} · Room {classroom.room_number || "N/A"}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 text-sm">
                        <div className="flex items-center">
                          <Users className="h-4 w-4 mr-1 text-gray-400" />
                          <span>{classroom.student_count || 0}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
          
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>
                {selectedClassroom ? selectedClassroom.name : "Classroom Details"}
              </CardTitle>
              <CardDescription>
                {selectedClassroom 
                  ? `${selectedClassroom.grade_level} · Academic Year ${selectedClassroom.academic_year}`
                  : "Select a classroom to view details"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : !selectedClassroom ? (
                <div className="text-center py-8 text-muted-foreground">
                  Select a classroom from the list to view details
                </div>
              ) : (
                <Tabs defaultValue="students">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="students">
                      <Users className="mr-2 h-4 w-4" />
                      Students
                    </TabsTrigger>
                    <TabsTrigger value="teacher">
                      <UserRound className="mr-2 h-4 w-4" />
                      Teacher
                    </TabsTrigger>
                    <TabsTrigger value="schedule">
                      <CalendarDays className="mr-2 h-4 w-4" />
                      Schedule
                    </TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="students" className="mt-4">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-medium">
                        Students ({classroomStudents.length}/{selectedClassroom.capacity})
                      </h3>
                      
                      <Dialog open={isAddingStudent} onOpenChange={setIsAddingStudent}>
                        <DialogTrigger asChild>
                          <Button size="sm">
                            <Plus className="mr-2 h-4 w-4" />
                            Add Students
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Add Students to {selectedClassroom.name}</DialogTitle>
                            <DialogDescription>
                              Select students to add to this classroom.
                            </DialogDescription>
                          </DialogHeader>
                          
                          <div className="max-h-[400px] overflow-y-auto mt-4">
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead className="w-12"></TableHead>
                                  <TableHead>Name</TableHead>
                                  <TableHead>ID</TableHead>
                                  <TableHead>Grade</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {unassignedStudents.length === 0 ? (
                                  <TableRow>
                                    <TableCell colSpan={4} className="text-center">
                                      No unassigned students found
                                    </TableCell>
                                  </TableRow>
                                ) : (
                                  unassignedStudents.map(student => (
                                    <TableRow 
                                      key={student.id}
                                      className="cursor-pointer"
                                      onClick={() => handleToggleStudentSelection(student.id)}
                                    >
                                      <TableCell>
                                        <input 
                                          type="checkbox"
                                          checked={selectedStudentIds.includes(student.id)}
                                          onChange={() => handleToggleStudentSelection(student.id)}
                                          className="h-4 w-4"
                                        />
                                      </TableCell>
                                      <TableCell>{student.name}</TableCell>
                                      <TableCell>{student.student_id}</TableCell>
                                      <TableCell>{student.grade_level}</TableCell>
                                    </TableRow>
                                  ))
                                )}
                              </TableBody>
                            </Table>
                          </div>
                          
                          <DialogFooter>
                            <Button variant="outline" onClick={() => setIsAddingStudent(false)}>
                              Cancel
                            </Button>
                            <Button 
                              onClick={handleAddStudentsToClassroom}
                              disabled={selectedStudentIds.length === 0 || loading}
                            >
                              {loading ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Adding...
                                </>
                              ) : (
                                `Add ${selectedStudentIds.length} Students`
                              )}
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </div>
                    
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Student</TableHead>
                            <TableHead>ID</TableHead>
                            <TableHead>Grade Level</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {classroomStudents.length === 0 ? (
                            <TableRow>
                              <TableCell colSpan={4} className="text-center h-24">
                                No students assigned to this classroom
                              </TableCell>
                            </TableRow>
                          ) : (
                            classroomStudents.map(student => (
                              <TableRow key={student.id}>
                                <TableCell className="font-medium">{student.name}</TableCell>
                                <TableCell>{student.student_id}</TableCell>
                                <TableCell>{student.grade_level}</TableCell>
                                <TableCell className="text-right">
                                  <Button 
                                    variant="ghost" 
                                    size="sm" 
                                    onClick={() => handleRemoveStudentFromClassroom(student.id)}
                                  >
                                    Remove
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))
                          )}
                        </TableBody>
                      </Table>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="teacher" className="mt-4">
                    <div className="p-6 border rounded-lg">
                      <h3 className="text-lg font-medium mb-4">Assigned Teacher</h3>
                      
                      {selectedClassroom.teacher ? (
                        <div className="flex items-center space-x-4">
                          <div className="h-12 w-12 rounded-full bg-promise-100 flex items-center justify-center">
                            <UserRound className="h-6 w-6 text-promise-600" />
                          </div>
                          <div>
                            <p className="font-medium">{selectedClassroom.teacher.first_name} {selectedClassroom.teacher.last_name}</p>
                            <p className="text-sm text-muted-foreground">Primary Teacher</p>
                          </div>
                        </div>
                      ) : (
                        <div className="text-center py-8 text-muted-foreground">
                          No teacher assigned to this classroom
                        </div>
                      )}
                      
                      <div className="mt-6">
                        <Button variant="outline" size="sm">
                          Change Teacher
                        </Button>
                      </div>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="schedule" className="mt-4">
                    <div className="p-6 border rounded-lg">
                      <h3 className="text-lg font-medium mb-4">Class Schedule</h3>
                      
                      {subjects.length === 0 ? (
                        <div className="text-center py-8 text-muted-foreground">
                          No subjects available for scheduling
                        </div>
                      ) : (
                        <div className="space-y-6">
                          <div className="flex justify-between items-center">
                            <p className="font-medium">Monday</p>
                            <Button variant="outline" size="sm">
                              <Plus className="mr-2 h-4 w-4" />
                              Add Period
                            </Button>
                          </div>
                          
                          <div className="rounded-md border">
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead>Time</TableHead>
                                  <TableHead>Subject</TableHead>
                                  <TableHead>Teacher</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                <TableRow>
                                  <TableCell colSpan={3} className="text-center h-16">
                                    No periods scheduled for Monday
                                  </TableCell>
                                </TableRow>
                              </TableBody>
                            </Table>
                          </div>
                          
                          <div className="text-center mt-4">
                            <Button variant="outline">View Full Schedule</Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </TabsContent>
                </Tabs>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default ClassroomManagement;
