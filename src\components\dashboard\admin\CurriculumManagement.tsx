
import { useState } from "react";
import DashboardLayout from "../DashboardLayout";
import { Card, CardContent, CardDescription, CardHeader, Card<PERSON><PERSON>le, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Book, Search, PlusCircle, Download, FileText, Pencil, MoreHorizontal, Check, X, Plus } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";

const curricula = [
  {
    id: "CUR001",
    title: "Early Literacy Development",
    class: "Pre-K (4 years)",
    subject: "Language Arts",
    lastUpdated: "May 10, 2025",
    status: "active",
    objectives: [
      "Recognize and name all letters of the alphabet",
      "Identify letter-sound relationships",
      "Develop phonological awareness",
      "Build vocabulary through stories and discussion"
    ]
  },
  {
    id: "CUR002",
    title: "Foundational Math Concepts",
    class: "Pre-K (4 years)",
    subject: "Mathematics",
    lastUpdated: "May 8, 2025",
    status: "active",
    objectives: [
      "Count to 30 and beyond",
      "Recognize numerals 1-20",
      "Understand basic patterns",
      "Explore simple addition concepts"
    ]
  },
  {
    id: "CUR003",
    title: "Scientific Exploration",
    class: "Pre-K (4 years)",
    subject: "Science",
    lastUpdated: "May 5, 2025",
    status: "active",
    objectives: [
      "Observe and describe natural phenomena",
      "Conduct simple experiments",
      "Learn about plants and their growth cycle",
      "Explore basic concepts of weather"
    ]
  },
  {
    id: "CUR004",
    title: "Social Development",
    class: "Pre-K (4 years)",
    subject: "Social Studies",
    lastUpdated: "April 28, 2025",
    status: "active",
    objectives: [
      "Develop empathy and social awareness",
      "Learn to share and take turns",
      "Understand classroom rules and responsibilities",
      "Explore different cultures and communities"
    ]
  },
  {
    id: "CUR005",
    title: "Creative Expression",
    class: "Pre-K (4 years)",
    subject: "Arts",
    lastUpdated: "April 25, 2025",
    status: "active",
    objectives: [
      "Experiment with various art materials and techniques",
      "Use imagination to create original works",
      "Express feelings through art and music",
      "Develop fine motor skills through artistic activities"
    ]
  },
  {
    id: "CUR006",
    title: "Summer Learning Program",
    class: "All Classes",
    subject: "Multi-subject",
    lastUpdated: "April 15, 2025",
    status: "draft",
    objectives: [
      "Maintain academic skills during summer months",
      "Engage in hands-on learning experiences",
      "Explore nature and outdoor environments",
      "Participate in collaborative projects"
    ]
  },
];

const CurriculumManagement = () => {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredCurricula = curricula.filter(curriculum => 
    curriculum.title.toLowerCase().includes(searchTerm.toLowerCase()) || 
    curriculum.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
    curriculum.class.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <DashboardLayout role="admin" title="Curriculum Management">
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Book className="h-5 w-5" />
                  Curriculum Management
                </CardTitle>
                <CardDescription>Develop and manage educational curricula</CardDescription>
              </div>
              <Dialog>
                <DialogTrigger asChild>
                  <Button>
                    <PlusCircle className="h-4 w-4 mr-2" /> Create New Curriculum
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-3xl">
                  <DialogHeader>
                    <DialogTitle>Create New Curriculum</DialogTitle>
                    <DialogDescription>
                      Design a new curriculum by filling out the required information
                    </DialogDescription>
                  </DialogHeader>
                  <form className="space-y-6">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="title">Curriculum Title*</Label>
                        <Input id="title" placeholder="Enter curriculum title" required />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="subject">Subject*</Label>
                        <Input id="subject" placeholder="Enter subject" required />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="class">Class*</Label>
                        <Input id="class" placeholder="Enter target class/age group" required />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="duration">Duration</Label>
                        <Input id="duration" placeholder="E.g., 8 weeks, Full semester" />
                      </div>
                    </div>
                    
                    <Tabs defaultValue="overview">
                      <TabsList className="grid w-full grid-cols-3">
                        <TabsTrigger value="overview">Overview</TabsTrigger>
                        <TabsTrigger value="objectives">Objectives</TabsTrigger>
                        <TabsTrigger value="resources">Resources</TabsTrigger>
                      </TabsList>
                      
                      <TabsContent value="overview" className="space-y-4 pt-4">
                        <div className="space-y-2">
                          <Label htmlFor="description">Description*</Label>
                          <Textarea id="description" placeholder="Enter curriculum description" required rows={5} />
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="relevance">Educational Relevance</Label>
                          <Textarea id="relevance" placeholder="Explain the educational importance of this curriculum" rows={3} />
                        </div>
                      </TabsContent>
                      
                      <TabsContent value="objectives" className="space-y-4 pt-4">
                        <div className="space-y-2">
                          <Label htmlFor="objectives">Learning Objectives*</Label>
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Input placeholder="Enter learning objective" />
                              <Button variant="outline" size="icon" type="button">
                                <Plus className="h-4 w-4" />
                              </Button>
                            </div>
                            <div className="space-y-2 mt-2">
                              <div className="flex items-center gap-2 p-2 border rounded-md bg-muted/40">
                                <div className="flex-1">Recognize and name all letters of the alphabet</div>
                                <Button variant="ghost" size="icon" className="h-8 w-8">
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                              <div className="flex items-center gap-2 p-2 border rounded-md bg-muted/40">
                                <div className="flex-1">Develop phonological awareness</div>
                                <Button variant="ghost" size="icon" className="h-8 w-8">
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="assessment">Assessment Methods</Label>
                          <Textarea id="assessment" placeholder="Describe how learning will be assessed" rows={3} />
                        </div>
                      </TabsContent>
                      
                      <TabsContent value="resources" className="space-y-4 pt-4">
                        <div className="space-y-2">
                          <Label htmlFor="materials">Required Materials</Label>
                          <Textarea id="materials" placeholder="List required materials for this curriculum" rows={3} />
                        </div>
                        
                        <div className="space-y-2">
                          <Label>Additional Resources</Label>
                          <div className="border rounded-md p-4">
                            <div className="space-y-2">
                              <Label htmlFor="resource">Upload Resources</Label>
                              <Input id="resource" type="file" />
                              <p className="text-xs text-muted-foreground">Support PDFs, DOCs, PPTs, and image files up to 10MB</p>
                            </div>
                          </div>
                        </div>
                      </TabsContent>
                    </Tabs>
                    
                    <DialogFooter>
                      <Button variant="outline">Save as Draft</Button>
                      <Button type="submit">Create Curriculum</Button>
                    </DialogFooter>
                  </form>
                </DialogContent>
              </Dialog>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-2 justify-between">
                <div className="relative flex-1 max-w-sm">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search curricula..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline">
                    <FileText className="h-4 w-4 mr-1" /> Import
                  </Button>
                  <Button variant="outline">
                    <Download className="h-4 w-4 mr-1" /> Export
                  </Button>
                </div>
              </div>
              
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[300px]">Curriculum</TableHead>
                      <TableHead>Class</TableHead>
                      <TableHead>Subject</TableHead>
                      <TableHead>Last Updated</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredCurricula.map(curriculum => (
                      <TableRow key={curriculum.id}>
                        <TableCell>
                          <div className="font-medium">{curriculum.title}</div>
                          <div className="text-xs text-muted-foreground">{curriculum.id}</div>
                        </TableCell>
                        <TableCell>{curriculum.class}</TableCell>
                        <TableCell>{curriculum.subject}</TableCell>
                        <TableCell>{curriculum.lastUpdated}</TableCell>
                        <TableCell>
                          {curriculum.status === 'active' && (
                            <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                              <Check className="h-3.5 w-3.5 mr-1" /> Active
                            </Badge>
                          )}
                          {curriculum.status === 'draft' && (
                            <Badge variant="outline">Draft</Badge>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            <Button variant="outline" size="sm">
                              <Pencil className="h-3.5 w-3.5 mr-1" /> Edit
                            </Button>
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button variant="ghost" size="sm">View</Button>
                              </DialogTrigger>
                              <DialogContent className="max-w-3xl">
                                <DialogHeader>
                                  <DialogTitle>{curriculum.title}</DialogTitle>
                                  <DialogDescription>
                                    {curriculum.id} • {curriculum.subject} • {curriculum.class}
                                  </DialogDescription>
                                </DialogHeader>
                                <div className="space-y-4">
                                  <div>
                                    <h4 className="font-semibold text-sm mb-2">Learning Objectives</h4>
                                    <ul className="list-disc pl-5 space-y-1">
                                      {curriculum.objectives.map((objective, index) => (
                                        <li key={index} className="text-sm">{objective}</li>
                                      ))}
                                    </ul>
                                  </div>
                                  
                                  <div className="border-t pt-4">
                                    <h4 className="font-semibold text-sm mb-2">Associated Resources</h4>
                                    <div className="grid grid-cols-2 gap-2">
                                      <div className="flex items-center gap-2 p-2 border rounded-md">
                                        <FileText className="h-4 w-4 text-muted-foreground" />
                                        <span className="text-sm">Curriculum Guide.pdf</span>
                                      </div>
                                      <div className="flex items-center gap-2 p-2 border rounded-md">
                                        <FileText className="h-4 w-4 text-muted-foreground" />
                                        <span className="text-sm">Lesson Plans.pdf</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <DialogFooter>
                                  <Button variant="outline">
                                    <Download className="h-4 w-4 mr-1" /> Download
                                  </Button>
                                </DialogFooter>
                              </DialogContent>
                            </Dialog>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem>Duplicate</DropdownMenuItem>
                                <DropdownMenuItem>Share</DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="text-destructive">Delete</DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default CurriculumManagement;
