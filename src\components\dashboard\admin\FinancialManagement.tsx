
import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { Calendar, Coins, CreditCard, DollarSign, FileText, Loader2, Plus, TrendingUp } from "lucide-react";
import { Bar<PERSON><PERSON> } from "@/components/ui/charts/BarChart";
import { financialService } from "@/lib/firestore";
import { useAuth } from "@/contexts/AuthContext";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import type { FinancialRecord } from "@/types/firebase";

// Define types for our financial data
type Transaction = {
  id: string;
  recordType: "income" | "expense";
  category: string;
  amount: number;
  recordDate: string;
  description: string;
  paymentMethod: string | null;
  status: "completed" | "pending" | "failed";
};

type Budget = {
  id: string;
  category: string;
  allocated: number;
  spent: number;
  remaining: number;
  period: string;
};

const FinancialManagement = () => {
  const { toast } = useToast();
  const { user } = useAuth();

  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [budgets, setBudgets] = useState<Budget[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const [newTransaction, setNewTransaction] = useState({
    recordType: "income" as "income" | "expense",
    category: "",
    amount: 0,
    recordDate: new Date().toISOString().split('T')[0],
    description: "",
    paymentMethod: "",
    status: "completed"
  });
  
  const [isAddingTransaction, setIsAddingTransaction] = useState(false);
  
  // Fetch financial transactions from Firestore
  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        const data = await financialService.getAll();

        // Transform the data to match our Transaction type
        const formattedTransactions = data.map((item): Transaction => ({
          id: item.id,
          recordType: item.recordType,
          category: item.category,
          amount: item.amount,
          recordDate: item.recordDate.toDate().toISOString().split('T')[0],
          description: item.description,
          paymentMethod: item.paymentMethod || null,
          status: "completed" // Default to completed
        }));

        // Sort by record date (newest first)
        formattedTransactions.sort((a, b) => new Date(b.recordDate).getTime() - new Date(a.recordDate).getTime());

        setTransactions(formattedTransactions);
      } catch (err) {
        console.error('Failed to fetch transactions:', err);
        toast({
          title: "Failed to load transactions",
          description: "Could not fetch financial data from the database.",
          variant: "destructive"
        });
      }
    };
    
    // For now, we'll use hardcoded budgets since there's no budget table yet
    const generateBudgets = () => {
      const mockBudgets: Budget[] = [
        {
          id: "1",
          category: "Staff Salaries",
          allocated: 18000.00,
          spent: 8500.00,
          remaining: 9500.00,
          period: "May 2025"
        },
        {
          id: "2",
          category: "Educational Supplies",
          allocated: 2000.00,
          spent: 350.25,
          remaining: 1649.75,
          period: "May 2025"
        },
        {
          id: "3",
          category: "Utilities",
          allocated: 1500.00,
          spent: 425.80,
          remaining: 1074.20,
          period: "May 2025"
        },
        {
          id: "4",
          category: "Facility Maintenance",
          allocated: 1200.00,
          spent: 0.00,
          remaining: 1200.00,
          period: "May 2025"
        },
        {
          id: "5",
          category: "Food Program",
          allocated: 3000.00,
          spent: 0.00,
          remaining: 3000.00,
          period: "May 2025"
        }
      ];
      
      setBudgets(mockBudgets);
    };
    
    fetchTransactions();
    generateBudgets();
    setIsLoading(false);
  }, [toast]);
  
  // Calculate financial summary
  const totalIncome = transactions
    .filter(t => t.recordType === "income")
    .reduce((sum, transaction) => sum + transaction.amount, 0);

  const totalExpenses = transactions
    .filter(t => t.recordType === "expense")
    .reduce((sum, transaction) => sum + transaction.amount, 0);
    
  const balance = totalIncome - totalExpenses;
  
  const totalBudget = budgets.reduce((sum, budget) => sum + budget.allocated, 0);
  const totalSpent = budgets.reduce((sum, budget) => sum + budget.spent, 0);
  const budgetRemaining = totalBudget - totalSpent;
  
  // Chart data for expenses by category
  const expenseCategories = [...new Set(transactions
    .filter(t => t.recordType === "expense")
    .map(t => t.category))];

  const expensesByCategory = expenseCategories.map(category => {
    const totalAmount = transactions
      .filter(t => t.recordType === "expense" && t.category === category)
      .reduce((sum, t) => sum + t.amount, 0);
      
    return {
      name: category,
      total: totalAmount
    };
  });
  
  // Handle adding a new transaction
  const handleAddTransaction = async () => {
    try {
      setIsLoading(true);

      if (!user) {
        toast({
          title: "Authentication Error",
          description: "You must be logged in to add transactions.",
          variant: "destructive"
        });
        return;
      }

      const newTransactionData: Omit<FinancialRecord, 'id'> = {
        recordType: newTransaction.recordType,
        category: newTransaction.category,
        amount: newTransaction.amount,
        recordDate: new Date(newTransaction.recordDate) as any, // Firestore Timestamp
        description: newTransaction.description,
        paymentMethod: newTransaction.paymentMethod,
        recordedBy: user.uid,
        createdAt: new Date() as any,
        updatedAt: new Date() as any
      };

      const docId = await financialService.add(newTransactionData);

      // Add the new transaction to the state
      const formattedTransaction: Transaction = {
        id: docId,
        recordType: newTransaction.recordType,
        category: newTransaction.category,
        amount: newTransaction.amount,
        recordDate: newTransaction.recordDate,
        description: newTransaction.description,
        paymentMethod: newTransaction.paymentMethod,
        status: "completed"
      };

      setTransactions([formattedTransaction, ...transactions]);
      
      // Reset the form
      setNewTransaction({
        recordType: "income",
        category: "",
        amount: 0,
        recordDate: new Date().toISOString().split('T')[0],
        description: "",
        paymentMethod: "",
        status: "completed"
      });

      setIsAddingTransaction(false);

      toast({
        title: "Transaction Added",
        description: `${formattedTransaction.recordType === "income" ? "Income" : "Expense"} of $${formattedTransaction.amount.toFixed(2)} has been recorded.`,
      });
    } catch (err) {
      console.error('Error adding transaction:', err);
      toast({
        title: "Error",
        description: "Failed to add transaction. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <DashboardLayout role="admin" title="Financial Management">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Financial Management</h2>
            <p className="text-gray-600">Track income, expenses, and budget allocations</p>
          </div>
          <Dialog open={isAddingTransaction} onOpenChange={setIsAddingTransaction}>
            <DialogTrigger asChild>
              <Button className="bg-promise-500 hover:bg-promise-600">
                <Plus className="mr-2 h-4 w-4" /> Add Transaction
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Transaction</DialogTitle>
                <DialogDescription>
                  Record a new financial transaction for the school.
                </DialogDescription>
              </DialogHeader>
              
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="type">Transaction Type</Label>
                    <Select
                      value={newTransaction.recordType}
                      onValueChange={value => setNewTransaction({...newTransaction, recordType: value as "income" | "expense"})}
                    >
                      <SelectTrigger id="type">
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="income">Income</SelectItem>
                        <SelectItem value="expense">Expense</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="amount">Amount ($)</Label>
                    <Input 
                      id="amount"
                      type="number"
                      step="0.01"
                      value={newTransaction.amount}
                      onChange={e => setNewTransaction({...newTransaction, amount: parseFloat(e.target.value) || 0})}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Input 
                    id="category"
                    value={newTransaction.category}
                    onChange={e => setNewTransaction({...newTransaction, category: e.target.value})}
                    placeholder={newTransaction.recordType === "income" ? "e.g., Tuition, Donation" : "e.g., Supplies, Utilities"}
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="date">Date</Label>
                    <Input 
                      id="date"
                      type="date"
                      value={newTransaction.recordDate}
                      onChange={e => setNewTransaction({...newTransaction, recordDate: e.target.value})}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="paymentMethod">Payment Method</Label>
                    <Input 
                      id="paymentMethod"
                      value={newTransaction.paymentMethod || ''}
                      onChange={e => setNewTransaction({...newTransaction, paymentMethod: e.target.value})}
                      placeholder="e.g., Cash, Credit Card, Transfer"
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Input 
                    id="description"
                    value={newTransaction.description}
                    onChange={e => setNewTransaction({...newTransaction, description: e.target.value})}
                    placeholder="Brief description of the transaction"
                  />
                </div>
              </div>
              
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddingTransaction(false)}>Cancel</Button>
                <Button onClick={handleAddTransaction} disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Adding...
                    </>
                  ) : 'Add Transaction'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Total Income</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-promise-600">${totalIncome.toFixed(2)}</div>
              <p className="text-xs text-muted-foreground mt-2">Month of May 2025</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
              <Coins className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-destructive">${totalExpenses.toFixed(2)}</div>
              <p className="text-xs text-muted-foreground mt-2">Month of May 2025</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium">Net Balance</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${balance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                ${balance.toFixed(2)}
              </div>
              <p className="text-xs text-muted-foreground mt-2">Income minus expenses</p>
            </CardContent>
          </Card>
        </div>
        
        <Tabs defaultValue="transactions" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="transactions">
              <CreditCard className="mr-2 h-4 w-4" />
              Transactions
            </TabsTrigger>
            <TabsTrigger value="budget">
              <DollarSign className="mr-2 h-4 w-4" />
              Budget
            </TabsTrigger>
            <TabsTrigger value="reports">
              <FileText className="mr-2 h-4 w-4" />
              Reports
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="transactions" className="mt-4">
            <Card>
              <CardContent className="pt-6">
                {isLoading ? (
                  <div className="flex justify-center p-6">
                    <Loader2 className="h-8 w-8 animate-spin" />
                  </div>
                ) : transactions.length > 0 ? (
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Category</TableHead>
                          <TableHead>Amount</TableHead>
                          <TableHead>Description</TableHead>
                          <TableHead>Payment Method</TableHead>
                          <TableHead>Status</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {transactions.map((transaction) => (
                          <TableRow key={transaction.id}>
                            <TableCell>{transaction.recordDate}</TableCell>
                            <TableCell>
                              <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                transaction.recordType === "income"
                                  ? "bg-green-100 text-green-800"
                                  : "bg-red-100 text-red-800"
                              }`}>
                                {transaction.recordType === "income" ? "Income" : "Expense"}
                              </span>
                            </TableCell>
                            <TableCell>{transaction.category}</TableCell>
                            <TableCell className={`font-medium ${
                              transaction.recordType === "income" ? "text-green-600" : "text-red-600"
                            }`}>
                              {transaction.recordType === "income" ? "+" : "-"}${transaction.amount.toFixed(2)}
                            </TableCell>
                            <TableCell>{transaction.description}</TableCell>
                            <TableCell>{transaction.paymentMethod}</TableCell>
                            <TableCell>
                              <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                transaction.status === "completed" 
                                  ? "bg-green-100 text-green-800" 
                                  : transaction.status === "pending"
                                  ? "bg-yellow-100 text-yellow-800"
                                  : "bg-red-100 text-red-800"
                              }`}>
                                {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                              </span>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <div className="text-center p-6 text-gray-500">
                    No transactions found. Add your first transaction to get started.
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="budget" className="mt-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <CardTitle className="text-sm font-medium">Total Budget</CardTitle>
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">${totalBudget.toFixed(2)}</div>
                  <p className="text-xs text-muted-foreground mt-2">May 2025 budget allocation</p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <CardTitle className="text-sm font-medium">Spent So Far</CardTitle>
                  <Coins className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-amber-600">${totalSpent.toFixed(2)}</div>
                  <p className="text-xs text-muted-foreground mt-2">{((totalSpent / totalBudget) * 100).toFixed(1)}% of total budget</p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <CardTitle className="text-sm font-medium">Remaining</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">${budgetRemaining.toFixed(2)}</div>
                  <p className="text-xs text-muted-foreground mt-2">{((budgetRemaining / totalBudget) * 100).toFixed(1)}% of total budget</p>
                </CardContent>
              </Card>
            </div>
            
            <Card>
              <CardContent className="pt-6">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Category</TableHead>
                        <TableHead>Allocated</TableHead>
                        <TableHead>Spent</TableHead>
                        <TableHead>Remaining</TableHead>
                        <TableHead>Period</TableHead>
                        <TableHead>Usage</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {budgets.map((budget) => (
                        <TableRow key={budget.id}>
                          <TableCell className="font-medium">{budget.category}</TableCell>
                          <TableCell>${budget.allocated.toFixed(2)}</TableCell>
                          <TableCell>${budget.spent.toFixed(2)}</TableCell>
                          <TableCell className={`font-medium ${
                            (budget.remaining / budget.allocated) < 0.2 ? "text-red-600" : "text-green-600"
                          }`}>
                            ${budget.remaining.toFixed(2)}
                          </TableCell>
                          <TableCell>{budget.period}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <div className="w-full bg-gray-200 rounded-full h-2.5">
                                <div 
                                  className={`h-2.5 rounded-full ${
                                    (budget.spent / budget.allocated) > 0.8 
                                      ? "bg-red-600" 
                                      : (budget.spent / budget.allocated) > 0.6
                                      ? "bg-amber-500"
                                      : "bg-green-600"
                                  }`} 
                                  style={{ width: `${(budget.spent / budget.allocated) * 100}%` }}
                                ></div>
                              </div>
                              <span className="text-xs ml-2">
                                {((budget.spent / budget.allocated) * 100).toFixed(0)}%
                              </span>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="reports" className="mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Expenses by Category</CardTitle>
                  <CardDescription>Breakdown of May 2025 expenses</CardDescription>
                </CardHeader>
                <CardContent>
                  <BarChart
                    data={expensesByCategory}
                    index="name"
                    categories={["total"]}
                    colors={["#f43f5e"]}
                    valueFormatter={(value) => `$${value.toFixed(2)}`}
                    className="h-80"
                  />
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Financial Summary</CardTitle>
                  <CardDescription>Key financial metrics for the current month</CardDescription>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-8">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Income vs Expenses</span>
                        <span className="text-sm text-gray-500">
                          {totalIncome > 0 ? ((totalIncome - totalExpenses) / totalIncome * 100).toFixed(1) : "0"}% margin
                        </span>
                      </div>
                      <div className="h-4 w-full bg-gray-100 rounded-full overflow-hidden">
                        <div className="flex h-full">
                          <div 
                            className="bg-green-500 h-full" 
                            style={{ width: `${totalIncome + totalExpenses > 0 ? (totalIncome / (totalIncome + totalExpenses)) * 100 : 0}%` }}
                          ></div>
                          <div 
                            className="bg-red-500 h-full" 
                            style={{ width: `${totalIncome + totalExpenses > 0 ? (totalExpenses / (totalIncome + totalExpenses)) * 100 : 0}%` }}
                          ></div>
                        </div>
                      </div>
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>Income: ${totalIncome.toFixed(2)}</span>
                        <span>Expenses: ${totalExpenses.toFixed(2)}</span>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Budget Utilization</span>
                        <span className="text-sm text-gray-500">
                          {totalBudget > 0 ? (totalSpent / totalBudget * 100).toFixed(1) : "0"}% used
                        </span>
                      </div>
                      <div className="h-4 w-full bg-gray-100 rounded-full overflow-hidden">
                        <div 
                          className={`h-full ${
                            (totalSpent / totalBudget) > 0.8 
                              ? "bg-red-500" 
                              : (totalSpent / totalBudget) > 0.6
                              ? "bg-amber-500"
                              : "bg-green-500"
                          }`} 
                          style={{ width: `${totalBudget > 0 ? (totalSpent / totalBudget) * 100 : 0}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>Spent: ${totalSpent.toFixed(2)}</span>
                        <span>Remaining: ${budgetRemaining.toFixed(2)}</span>
                      </div>
                    </div>
                    
                    <div className="pt-4 border-t">
                      <h4 className="text-sm font-medium mb-2">Monthly Financial Report</h4>
                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline" className="flex-1">
                          <FileText className="mr-2 h-4 w-4" />
                          Export PDF
                        </Button>
                        <Button size="sm" variant="outline" className="flex-1">
                          <FileText className="mr-2 h-4 w-4" />
                          Export CSV
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default FinancialManagement;
