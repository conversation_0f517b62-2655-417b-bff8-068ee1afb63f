import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Archive, FileText, Plus, Settings, AlertCircle, Loader2 } from "lucide-react";
import DashboardLayout from "../DashboardLayout";
import { inventoryService } from "@/lib/firestore";
import { useAuth } from "@/contexts/AuthContext";
import type { InventoryItem as FirebaseInventoryItem } from "@/types/firebase";

// Define inventory item types
interface InventoryItem {
  id: string;
  name: string;
  category: string;
  quantity: number;
  unitPrice: number | null;
  minimumStockLevel: number | null;
  location: string | null;
  lastRestocked: string | null;
  supplier: string | null;
}

interface Supplier {
  id: string;
  name: string;
  contact: string;
  email: string;
  phone: string;
  address: string;
}

interface InventoryItemForm {
  name: string;
  category: string;
  quantity: number;
  unit: string;
  minStockLevel: number;
  location: string;
  supplier: string;
  unitPrice: number | null;
}

interface SupplierForm {
  name: string;
  contact: string;
  email: string;
  phone: string;
  address: string;
}

const InventoryManagement = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const [items, setItems] = useState<InventoryItem[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);

  const [newItem, setNewItem] = useState<InventoryItemForm>({
    name: "",
    category: "Art Supplies",
    quantity: 0,
    unit: "pieces",
    minStockLevel: 0,
    location: "",
    supplier: "",
    unitPrice: null
  });

  const [newSupplier, setNewSupplier] = useState<SupplierForm>({
    name: "",
    contact: "",
    email: "",
    phone: "",
    address: ""
  });

  const [isAddingItem, setIsAddingItem] = useState(false);
  const [isAddingSupplier, setIsAddingSupplier] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch inventory items and initialize suppliers
  useEffect(() => {
    Promise.all([
      fetchInventoryItems(),
      initializeSuppliers()
    ]).finally(() => {
      setLoading(false);
    });
  }, []);

  // Fetch inventory items
  const fetchInventoryItems = async () => {
    setError(null);

    try {
      const data = await inventoryService.getAll();

      // Transform Firebase data to component format
      const formattedItems = data.map((item: any): InventoryItem => ({
        id: item.id,
        name: item.name || '',
        category: item.category || '',
        quantity: item.quantity || 0,
        unitPrice: item.unitPrice || null,
        minimumStockLevel: item.minimumStockLevel || null,
        location: item.location || null,
        lastRestocked: item.lastRestocked ? item.lastRestocked.toDate().toISOString().split('T')[0] : null,
        supplier: item.supplier || null
      }));

      setItems(formattedItems);
    } catch (err) {
      console.error('Error fetching inventory:', err);
      setError('Failed to load inventory items. Please try again.');
      toast({
        title: "Error loading data",
        description: "Could not fetch inventory data from the database.",
        variant: "destructive"
      });
    }
  };

  // Initialize default suppliers
  const initializeSuppliers = () => {
    const defaultSuppliers = [
      {
        id: "1",
        name: "Educational Supplies Co.",
        contact: "John Smith",
        email: "<EMAIL>",
        phone: "(*************",
        address: "123 Education Ave, New York, NY 10001"
      },
      {
        id: "2",
        name: "Creative Kids Supplies",
        contact: "Sarah Johnson",
        email: "<EMAIL>",
        phone: "(*************",
        address: "456 Creative Blvd, New York, NY 10002"
      },
      {
        id: "3",
        name: "School Supply Warehouse",
        contact: "Michael Brown",
        email: "<EMAIL>",
        phone: "(*************",
        address: "789 School St, New York, NY 10003"
      },
      {
        id: "4",
        name: "Learning Tools Inc.",
        contact: "Jessica Davis",
        email: "<EMAIL>",
        phone: "(*************",
        address: "101 Learning Ln, New York, NY 10004"
      }
    ];

    setSuppliers(defaultSuppliers);
    // Set default supplier for new items
    if (defaultSuppliers.length > 0) {
      setNewItem(prev => ({ ...prev, supplier: defaultSuppliers[0].name }));
    }
  };

  const handleInputChangeItem = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setNewItem({
      ...newItem,
      [id]: id === 'quantity' || id === 'minStockLevel' || id === 'unitPrice'
        ? parseFloat(value) || 0
        : value,
    });
  };

  const handleSelectChangeItem = (field: string, value: string) => {
    setNewItem({
      ...newItem,
      [field]: value
    });
  };

  const handleInputChangeSupplier = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setNewSupplier({
      ...newSupplier,
      [id]: value,
    });
  };

  const handleAddItem = async () => {
    try {
      setLoading(true);

      if (!user) {
        toast({
          title: "Authentication Error",
          description: "You must be logged in to add inventory items.",
          variant: "destructive"
        });
        return;
      }

      const newInventoryData: Omit<FirebaseInventoryItem, 'id'> = {
        name: newItem.name,
        category: newItem.category,
        quantity: newItem.quantity,
        minimumStockLevel: newItem.minStockLevel,
        location: newItem.location || undefined,
        lastRestocked: new Date() as any, // Firestore Timestamp
        supplier: newItem.supplier || undefined,
        unitPrice: newItem.unitPrice || undefined,
        createdAt: new Date() as any,
        updatedAt: new Date() as any
      };

      await inventoryService.add(newInventoryData);

      toast({
        title: "Item Added",
        description: `${newItem.name} has been added to inventory.`,
      });

      // Reset form and close dialog
      setNewItem({
        name: "",
        category: "Art Supplies",
        quantity: 0,
        unit: "pieces",
        minStockLevel: 0,
        location: "",
        supplier: suppliers.length > 0 ? suppliers[0].name : "",
        unitPrice: null
      });

      setIsAddingItem(false);

      // Refresh inventory items
      fetchInventoryItems();

    } catch (err: any) {
      console.error('Error adding inventory item:', err);
      toast({
        title: "Error",
        description: err.message || "Failed to add inventory item. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddSupplier = async () => {
    try {
      setLoading(true);

      // Create new supplier with generated ID
      const newSupplierData: Supplier = {
        id: Date.now().toString(), // Simple ID generation
        name: newSupplier.name,
        contact: newSupplier.contact,
        email: newSupplier.email,
        phone: newSupplier.phone,
        address: newSupplier.address
      };

      // Add the new supplier to the state
      setSuppliers([...suppliers, newSupplierData]);

      setNewSupplier({
        name: "",
        contact: "",
        email: "",
        phone: "",
        address: ""
      });

      setIsAddingSupplier(false);

      toast({
        title: "Supplier Added",
        description: `${newSupplier.name} has been added to suppliers.`,
      });
    } catch (err: any) {
      console.error('Error adding supplier:', err);
      toast({
        title: "Error",
        description: err.message || "Failed to add supplier. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleReorder = async (itemId: string) => {
    try {
      const item = items.find(i => i.id === itemId);

      toast({
        title: "Reorder Initiated",
        description: item ? `A purchase order has been created for ${item.name}.` : "A purchase order has been created for this item.",
      });
    } catch (error) {
      console.error('Error reordering item:', error);
    }
  };

  // Filter low stock items
  const lowStockItems = items.filter(item =>
    item.minimumStockLevel !== null && item.quantity <= item.minimumStockLevel
  );

  return (
    <DashboardLayout role="admin" title="Inventory Management">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Inventory Management</h2>
            <p className="text-gray-600">Track and manage school supplies and assets</p>
          </div>
          <div className="flex gap-2">
            <Dialog open={isAddingItem} onOpenChange={setIsAddingItem}>
              <DialogTrigger asChild>
                <Button className="bg-promise-500 hover:bg-promise-600">
                  <Plus className="mr-2 h-4 w-4" /> Add Item
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New Inventory Item</DialogTitle>
                  <DialogDescription>
                    Enter the details of the new item to add to your inventory.
                  </DialogDescription>
                </DialogHeader>

                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Item Name</Label>
                      <Input
                        id="name"
                        value={newItem.name}
                        onChange={handleInputChangeItem}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="category">Category</Label>
                      <Select
                        value={newItem.category}
                        onValueChange={(value) => handleSelectChangeItem("category", value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Art Supplies">Art Supplies</SelectItem>
                          <SelectItem value="Educational Toys">Educational Toys</SelectItem>
                          <SelectItem value="Stationery">Stationery</SelectItem>
                          <SelectItem value="Classroom Equipment">Classroom Equipment</SelectItem>
                          <SelectItem value="Cleaning Supplies">Cleaning Supplies</SelectItem>
                          <SelectItem value="Office Supplies">Office Supplies</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="quantity">Quantity</Label>
                      <Input
                        id="quantity"
                        type="number"
                        value={newItem.quantity}
                        onChange={handleInputChangeItem}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="unit">Unit</Label>
                      <Select
                        value={newItem.unit}
                        onValueChange={(value) => handleSelectChangeItem("unit", value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select unit" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pieces">Pieces</SelectItem>
                          <SelectItem value="boxes">Boxes</SelectItem>
                          <SelectItem value="packs">Packs</SelectItem>
                          <SelectItem value="sets">Sets</SelectItem>
                          <SelectItem value="reams">Reams</SelectItem>
                          <SelectItem value="bottles">Bottles</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="minStockLevel">Min Stock Level</Label>
                      <Input
                        id="minStockLevel"
                        type="number"
                        value={newItem.minStockLevel}
                        onChange={handleInputChangeItem}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="unitPrice">Unit Price ($)</Label>
                      <Input
                        id="unitPrice"
                        type="number"
                        step="0.01"
                        value={newItem.unitPrice || ""}
                        onChange={handleInputChangeItem}
                        placeholder="0.00"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="location">Storage Location</Label>
                      <Input
                        id="location"
                        value={newItem.location}
                        onChange={handleInputChangeItem}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="supplier">Supplier</Label>
                      <Select
                        value={newItem.supplier}
                        onValueChange={(value) => handleSelectChangeItem("supplier", value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select a supplier" />
                        </SelectTrigger>
                        <SelectContent>
                          {suppliers.map(supplier => (
                            <SelectItem key={supplier.id} value={supplier.name}>
                              {supplier.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsAddingItem(false)}>Cancel</Button>
                  <Button onClick={handleAddItem} disabled={loading}>
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Adding...
                      </>
                    ) : 'Add Item'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <Dialog open={isAddingSupplier} onOpenChange={setIsAddingSupplier}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Plus className="mr-2 h-4 w-4" /> Add Supplier
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New Supplier</DialogTitle>
                  <DialogDescription>
                    Enter the details of the new supplier to add to your database.
                  </DialogDescription>
                </DialogHeader>

                <div className="grid gap-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="supplierName">Supplier Name</Label>
                    <Input
                      id="name"
                      value={newSupplier.name}
                      onChange={handleInputChangeSupplier}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="contact">Contact Person</Label>
                      <Input
                        id="contact"
                        value={newSupplier.contact}
                        onChange={handleInputChangeSupplier}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        value={newSupplier.email}
                        onChange={handleInputChangeSupplier}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone</Label>
                      <Input
                        id="phone"
                        value={newSupplier.phone}
                        onChange={handleInputChangeSupplier}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="address">Address</Label>
                      <Input
                        id="address"
                        value={newSupplier.address}
                        onChange={handleInputChangeSupplier}
                      />
                    </div>
                  </div>
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsAddingSupplier(false)}>Cancel</Button>
                  <Button onClick={handleAddSupplier} disabled={loading}>
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Adding...
                      </>
                    ) : 'Add Supplier'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader>
              <CardTitle>Total Inventory Items</CardTitle>
              <CardDescription>All tracked items</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{items.length}</div>
              <p className="text-sm text-muted-foreground mt-2">Across {new Set(items.map(item => item.category)).size} categories</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Low Stock Items</CardTitle>
              <CardDescription>Items below minimum level</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-amber-500">{lowStockItems.length}</div>
              <p className="text-sm text-muted-foreground mt-2">Items that need reordering</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Suppliers</CardTitle>
              <CardDescription>Registered vendors</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{suppliers.length}</div>
              <p className="text-sm text-muted-foreground mt-2">Active suppliers in system</p>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="inventory" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="inventory">
              <Archive className="mr-2 h-4 w-4" />
              Inventory Items
            </TabsTrigger>
            <TabsTrigger value="suppliers">
              <FileText className="mr-2 h-4 w-4" />
              Suppliers
            </TabsTrigger>
            <TabsTrigger value="low-stock">
              <Settings className="mr-2 h-4 w-4" />
              Low Stock Items
            </TabsTrigger>
          </TabsList>

          <TabsContent value="inventory" className="mt-4">
            <Card>
              <CardContent className="pt-6">
                {loading ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : error ? (
                  <div className="rounded-md bg-destructive/15 p-4 flex items-center">
                    <AlertCircle className="h-5 w-5 text-destructive mr-2" />
                    <p className="text-destructive">{error}</p>
                  </div>
                ) : items.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-12">
                    <Archive className="h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium">No Inventory Items</h3>
                    <p className="text-sm text-gray-500 mt-2">Add your first item to start tracking inventory.</p>
                    <Button className="mt-4" onClick={() => setIsAddingItem(true)}>
                      <Plus className="mr-2 h-4 w-4" />
                      Add First Item
                    </Button>
                  </div>
                ) : (
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Name</TableHead>
                          <TableHead>Category</TableHead>
                          <TableHead>Quantity</TableHead>
                          <TableHead>Location</TableHead>
                          <TableHead>Supplier</TableHead>
                          <TableHead>Last Restocked</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {items.map((item) => (
                          <TableRow key={item.id}>
                            <TableCell className="font-medium">{item.name}</TableCell>
                            <TableCell>{item.category}</TableCell>
                            <TableCell>
                              <span className={item.minimumStockLevel !== null && item.quantity <= item.minimumStockLevel ? "text-red-500 font-medium" : ""}>
                                {item.quantity}
                              </span>
                            </TableCell>
                            <TableCell>{item.location || "N/A"}</TableCell>
                            <TableCell>{item.supplier || "N/A"}</TableCell>
                            <TableCell>{item.lastRestocked || "Never"}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="suppliers" className="mt-4">
            <Card>
              <CardContent className="pt-6">
                {loading ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : suppliers.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-12">
                    <FileText className="h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium">No Suppliers</h3>
                    <p className="text-sm text-gray-500 mt-2">Add your first supplier to get started.</p>
                    <Button className="mt-4" onClick={() => setIsAddingSupplier(true)}>
                      <Plus className="mr-2 h-4 w-4" />
                      Add First Supplier
                    </Button>
                  </div>
                ) : (
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Supplier Name</TableHead>
                          <TableHead>Contact Person</TableHead>
                          <TableHead>Email</TableHead>
                          <TableHead>Phone</TableHead>
                          <TableHead>Address</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {suppliers.map((supplier) => (
                          <TableRow key={supplier.id}>
                            <TableCell className="font-medium">{supplier.name}</TableCell>
                            <TableCell>{supplier.contact}</TableCell>
                            <TableCell>{supplier.email}</TableCell>
                            <TableCell>{supplier.phone}</TableCell>
                            <TableCell>{supplier.address}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="low-stock" className="mt-4">
            <Card>
              <CardContent className="pt-6">
                {loading ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : lowStockItems.length > 0 ? (
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Name</TableHead>
                          <TableHead>Current Quantity</TableHead>
                          <TableHead>Minimum Level</TableHead>
                          <TableHead>Supplier</TableHead>
                          <TableHead></TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {lowStockItems.map((item) => (
                          <TableRow key={item.id}>
                            <TableCell className="font-medium">{item.name}</TableCell>
                            <TableCell className="text-red-500">
                              {item.quantity}
                            </TableCell>
                            <TableCell>{item.minimumStockLevel}</TableCell>
                            <TableCell>{item.supplier || "N/A"}</TableCell>
                            <TableCell>
                              <Button size="sm" variant="outline" onClick={() => handleReorder(item.id)}>
                                Reorder
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-12">
                    <Archive className="h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium">No Low Stock Items</h3>
                    <p className="text-sm text-gray-500 mt-2">All inventory items are above their minimum stock levels.</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default InventoryManagement;
