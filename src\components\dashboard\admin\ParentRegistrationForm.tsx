
import { useState } from "react";
import UserRegistrationForm from "@/components/auth/UserRegistrationForm";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle } from "lucide-react";

const ParentRegistrationForm = () => {
  const [registrationSuccess, setRegistrationSuccess] = useState(false);
  const [parentId, setParentId] = useState<string | null>(null);
  
  const handleRegistrationSuccess = (userId: string) => {
    setParentId(userId);
    setRegistrationSuccess(true);
    
    // Reset success message after 5 seconds
    setTimeout(() => {
      setRegistrationSuccess(false);
    }, 5000);
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-2xl font-quicksand">Parent Registration</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {registrationSuccess && (
          <Alert className="bg-green-50 border-green-200">
            <CheckCircle className="h-5 w-5 text-green-500" />
            <AlertTitle className="text-green-800">Registration Successful</AlertTitle>
            <AlertDescription className="text-green-700">
              The parent account has been created successfully. The parent will receive a confirmation email with login instructions.
            </AlertDescription>
          </Alert>
        )}
        
        <UserRegistrationForm 
          onSuccess={handleRegistrationSuccess} 
          initialRole="parent"
          hideRoleSelector={true}
        />
      </CardContent>
    </Card>
  );
};

export default ParentRegistrationForm;
