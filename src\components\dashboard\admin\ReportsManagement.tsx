
import DashboardLayout from "../DashboardLayout";
import { <PERSON>, CardContent, CardDescription, CardHeader, Card<PERSON><PERSON>le, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { FileText, Download, Search, Calendar, Filter, PlusCircle, ArrowUpDown, FileBarChart } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Bar<PERSON><PERSON> } from "@/components/ui/charts/BarChart";
import { useState } from "react";

const reportData = [
  {
    id: "REP001",
    title: "Monthly Attendance Summary",
    type: "attendance",
    date: "May 1, 2025",
    status: "generated",
    author: "System"
  },
  {
    id: "REP002",
    title: "Q2 Financial Overview",
    type: "financial",
    date: "April 30, 2025",
    status: "generated",
    author: "Admin Office"
  },
  {
    id: "REP003",
    title: "Pre-K (4 years) Progress Assessment",
    type: "academic",
    date: "April 25, 2025",
    status: "generated",
    author: "Ms. Davis"
  },
  {
    id: "REP004",
    title: "Inventory Status Report",
    type: "inventory",
    date: "April 22, 2025",
    status: "generated",
    author: "Admin Office"
  },
  {
    id: "REP005",
    title: "End of Year Academic Summary",
    type: "academic",
    date: "June 15, 2025",
    status: "scheduled",
    author: "All Teachers"
  },
  {
    id: "REP006",
    title: "Staff Performance Review",
    type: "hr",
    date: "May 20, 2025",
    status: "in-progress",
    author: "Principal"
  },
];

const attendanceData = [
  { class: "Infants", present: 92, absent: 8 },
  { class: "Toddlers", present: 94, absent: 6 },
  { class: "Pre-K (3)", present: 96, absent: 4 },
  { class: "Pre-K (4)", present: 95, absent: 5 },
  { class: "Kindergarten", present: 98, absent: 2 },
];

const enrollmentData = [
  { month: "Jan", enrolled: 125, capacity: 150 },
  { month: "Feb", enrolled: 127, capacity: 150 },
  { month: "Mar", enrolled: 130, capacity: 150 },
  { month: "Apr", enrolled: 132, capacity: 150 },
  { month: "May", enrolled: 135, capacity: 150 },
];

const ReportsManagement = () => {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredReports = reportData.filter(report => 
    report.title.toLowerCase().includes(searchTerm.toLowerCase()) || 
    report.type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <DashboardLayout role="admin" title="Reports Management">
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <div className="space-y-1">
                <CardTitle>Attendance Overview</CardTitle>
                <CardDescription>Current month summary</CardDescription>
              </div>
            </CardHeader>
            <CardContent>
              <div className="h-[220px]">
                <BarChart
                  data={attendanceData}
                  index="class"
                  categories={["present", "absent"]}
                  colors={["#65cf9a", "#ff8c82"]}
                  valueFormatter={(value) => `${value}%`}
                />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <div className="space-y-1">
                <CardTitle>Enrollment Trends</CardTitle>
                <CardDescription>Current year</CardDescription>
              </div>
            </CardHeader>
            <CardContent>
              <div className="h-[220px]">
                <BarChart
                  data={enrollmentData}
                  index="month"
                  categories={["enrolled", "capacity"]}
                  colors={["#9b87f5", "#e2e2e2"]}
                  valueFormatter={(value) => `${value} students`}
                />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Report Statistics</CardTitle>
              <CardDescription>Generated reports overview</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>Total reports generated</span>
                  <span className="font-bold">24</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Academic reports</span>
                  <span className="font-bold">10</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Financial reports</span>
                  <span className="font-bold">6</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Attendance reports</span>
                  <span className="font-bold">5</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Other reports</span>
                  <span className="font-bold">3</span>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button className="w-full" variant="outline">
                <FileBarChart className="mr-2 h-4 w-4" /> View All Statistics
              </Button>
            </CardFooter>
          </Card>
        </div>

        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Reports Management
                </CardTitle>
                <CardDescription>Generate, view, and manage reports</CardDescription>
              </div>
              <Button>
                <PlusCircle className="h-4 w-4 mr-2" />
                Generate New Report
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all">
              <div className="flex items-center justify-between mb-4">
                <TabsList>
                  <TabsTrigger value="all">All Reports</TabsTrigger>
                  <TabsTrigger value="academic">Academic</TabsTrigger>
                  <TabsTrigger value="attendance">Attendance</TabsTrigger>
                  <TabsTrigger value="financial">Financial</TabsTrigger>
                </TabsList>
                <div className="flex items-center gap-2">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search reports..."
                      className="pl-8 w-[250px]"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                  <Button variant="outline" size="icon">
                    <Filter className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <TabsContent value="all" className="m-0">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[300px]">
                          <div className="flex items-center gap-1">
                            Report Title
                            <ArrowUpDown className="h-3 w-3" />
                          </div>
                        </TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>
                          <div className="flex items-center gap-1">
                            Date
                            <ArrowUpDown className="h-3 w-3" />
                          </div>
                        </TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Author</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredReports.map(report => (
                        <TableRow key={report.id}>
                          <TableCell>
                            <div className="font-medium">{report.title}</div>
                            <div className="text-xs text-muted-foreground">{report.id}</div>
                          </TableCell>
                          <TableCell>
                            {report.type === 'academic' && <Badge className="bg-promise-500">Academic</Badge>}
                            {report.type === 'attendance' && <Badge className="bg-meadow-500">Attendance</Badge>}
                            {report.type === 'financial' && <Badge className="bg-peach-500">Financial</Badge>}
                            {report.type === 'inventory' && <Badge className="bg-sunlight-600">Inventory</Badge>}
                            {report.type === 'hr' && <Badge variant="outline">HR</Badge>}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3.5 w-3.5 text-muted-foreground" />
                              <span>{report.date}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            {report.status === 'generated' && <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50">Generated</Badge>}
                            {report.status === 'scheduled' && <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-50">Scheduled</Badge>}
                            {report.status === 'in-progress' && <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50">In Progress</Badge>}
                          </TableCell>
                          <TableCell>{report.author}</TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end gap-2">
                              {report.status === 'generated' && (
                                <>
                                  <Button variant="ghost" size="sm">View</Button>
                                  <Button variant="outline" size="sm">
                                    <Download className="h-3.5 w-3.5 mr-1" /> Download
                                  </Button>
                                </>
                              )}
                              {report.status === 'scheduled' && (
                                <Button variant="outline" size="sm">Edit Schedule</Button>
                              )}
                              {report.status === 'in-progress' && (
                                <Button variant="outline" size="sm">Check Progress</Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
              
              <TabsContent value="academic" className="m-0">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[300px]">Report Title</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Author</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredReports
                        .filter(report => report.type === 'academic')
                        .map(report => (
                          <TableRow key={report.id}>
                            <TableCell>
                              <div className="font-medium">{report.title}</div>
                              <div className="text-xs text-muted-foreground">{report.id}</div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3.5 w-3.5 text-muted-foreground" />
                                <span>{report.date}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              {report.status === 'generated' && <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50">Generated</Badge>}
                              {report.status === 'scheduled' && <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-50">Scheduled</Badge>}
                              {report.status === 'in-progress' && <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50">In Progress</Badge>}
                            </TableCell>
                            <TableCell>{report.author}</TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end gap-2">
                                {report.status === 'generated' && (
                                  <>
                                    <Button variant="ghost" size="sm">View</Button>
                                    <Button variant="outline" size="sm">
                                      <Download className="h-3.5 w-3.5 mr-1" /> Download
                                    </Button>
                                  </>
                                )}
                                {report.status === 'scheduled' && (
                                  <Button variant="outline" size="sm">Edit Schedule</Button>
                                )}
                                {report.status === 'in-progress' && (
                                  <Button variant="outline" size="sm">Check Progress</Button>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
              
              <TabsContent value="attendance" className="m-0">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[300px]">Report Title</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Author</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredReports
                        .filter(report => report.type === 'attendance')
                        .map(report => (
                          <TableRow key={report.id}>
                            <TableCell>
                              <div className="font-medium">{report.title}</div>
                              <div className="text-xs text-muted-foreground">{report.id}</div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3.5 w-3.5 text-muted-foreground" />
                                <span>{report.date}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              {report.status === 'generated' && <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50">Generated</Badge>}
                              {report.status === 'scheduled' && <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-50">Scheduled</Badge>}
                              {report.status === 'in-progress' && <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50">In Progress</Badge>}
                            </TableCell>
                            <TableCell>{report.author}</TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end gap-2">
                                {report.status === 'generated' && (
                                  <>
                                    <Button variant="ghost" size="sm">View</Button>
                                    <Button variant="outline" size="sm">
                                      <Download className="h-3.5 w-3.5 mr-1" /> Download
                                    </Button>
                                  </>
                                )}
                                {report.status === 'scheduled' && (
                                  <Button variant="outline" size="sm">Edit Schedule</Button>
                                )}
                                {report.status === 'in-progress' && (
                                  <Button variant="outline" size="sm">Check Progress</Button>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
              
              <TabsContent value="financial" className="m-0">
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[300px]">Report Title</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Author</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredReports
                        .filter(report => report.type === 'financial')
                        .map(report => (
                          <TableRow key={report.id}>
                            <TableCell>
                              <div className="font-medium">{report.title}</div>
                              <div className="text-xs text-muted-foreground">{report.id}</div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3.5 w-3.5 text-muted-foreground" />
                                <span>{report.date}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              {report.status === 'generated' && <Badge variant="outline" className="bg-green-50 text-green-700 hover:bg-green-50">Generated</Badge>}
                              {report.status === 'scheduled' && <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-50">Scheduled</Badge>}
                              {report.status === 'in-progress' && <Badge variant="outline" className="bg-blue-50 text-blue-700 hover:bg-blue-50">In Progress</Badge>}
                            </TableCell>
                            <TableCell>{report.author}</TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end gap-2">
                                {report.status === 'generated' && (
                                  <>
                                    <Button variant="ghost" size="sm">View</Button>
                                    <Button variant="outline" size="sm">
                                      <Download className="h-3.5 w-3.5 mr-1" /> Download
                                    </Button>
                                  </>
                                )}
                                {report.status === 'scheduled' && (
                                  <Button variant="outline" size="sm">Edit Schedule</Button>
                                )}
                                {report.status === 'in-progress' && (
                                  <Button variant="outline" size="sm">Check Progress</Button>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default ReportsManagement;
