
import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { format } from "date-fns";
import { CalendarIcon, Eye, Pencil, Plus, Search, Trash2, UserPlus } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { studentsService, usersService } from "@/lib/firestore";
import UserRegistrationForm from "@/components/auth/UserRegistrationForm";
import { useNavigate } from "react-router-dom";
import type { Student as FirebaseStudent, UserProfile } from "@/types/firebase";

// Type definitions for the student data with parent info
interface Student extends FirebaseStudent {
  parent?: {
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber?: string;
  } | null;
}

// Form schema for adding a new student
const studentFormSchema = z.object({
  firstName: z.string().min(2, { message: "First name must be at least 2 characters." }),
  lastName: z.string().min(2, { message: "Last name must be at least 2 characters." }),
  dateOfBirth: z.date({ required_error: "Date of birth is required." }),
  gradeLevel: z.string({ required_error: "Grade level is required." }),
  gender: z.string().optional(),
  address: z.string().optional(),
  parentId: z.string().optional(),
});

type StudentFormValues = z.infer<typeof studentFormSchema>;

const StudentsManagement = () => {
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("add-student");
  const [parents, setParents] = useState<{id: string, name: string}[]>([]);
  const [newParentId, setNewParentId] = useState<string | null>(null);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const { toast } = useToast();
  const navigate = useNavigate();
  
  const form = useForm<StudentFormValues>({
    resolver: zodResolver(studentFormSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      gradeLevel: "",
      gender: "",
      address: "",
    },
  });
  
  // Fetch students on component mount
  useEffect(() => {
    fetchStudents();
    fetchParents();
  }, []);
  
  const fetchStudents = async () => {
    try {
      setLoading(true);
      const studentsData = await studentsService.getAll();

      // Fetch parent information for each student
      const studentsWithParents = await Promise.all(
        studentsData.map(async (student) => {
          let parent = null;
          if (student.parentId) {
            try {
              const parentData = await usersService.getById(student.parentId);
              if (parentData) {
                parent = {
                  firstName: parentData.firstName,
                  lastName: parentData.lastName,
                  email: parentData.email,
                  phoneNumber: parentData.phoneNumber,
                };
              }
            } catch (error) {
              console.error(`Error fetching parent for student ${student.id}:`, error);
            }
          }
          return { ...student, parent };
        })
      );

      // Sort by last name
      studentsWithParents.sort((a, b) => a.lastName.localeCompare(b.lastName));

      setStudents(studentsWithParents);
    } catch (error) {
      console.error('Error fetching students:', error);
      toast({
        title: "Error",
        description: "Failed to load students data.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };
  
  const fetchParents = async () => {
    try {
      const allUsers = await usersService.getAll();
      const parentUsers = allUsers.filter(user => user.role === 'parent');

      setParents(parentUsers.map(p => ({
        id: p.id,
        name: `${p.firstName} ${p.lastName}`
      })));
    } catch (error) {
      console.error('Error fetching parents:', error);
    }
  };
  
  const handleParentRegistered = (userId: string) => {
    toast({
      title: "Parent registered",
      description: "Parent account has been created successfully.",
    });
    setNewParentId(userId);
    setActiveTab("add-student");
    fetchParents();
  };
  
  const onSubmit = async (data: StudentFormValues) => {
    setIsLoading(true);

    try {
      // Generate a student ID (e.g., S + current year + random 4 digits)
      const year = new Date().getFullYear().toString().substr(-2);
      const randomDigits = Math.floor(1000 + Math.random() * 9000);
      const studentId = `S${year}${randomDigits}`;

      // Create the student record
      const studentData: Omit<FirebaseStudent, 'id'> = {
        firstName: data.firstName,
        lastName: data.lastName,
        dateOfBirth: format(data.dateOfBirth, 'yyyy-MM-dd'),
        gradeLevel: data.gradeLevel,
        gender: data.gender,
        address: data.address,
        studentId: studentId,
        parentId: newParentId || data.parentId,
        isActive: true,
        enrollmentDate: new Date().toISOString().split('T')[0],
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await studentsService.add(studentData);

      toast({
        title: "Success",
        description: "Student added successfully.",
      });

      // Reset form and close dialog
      form.reset();
      setIsAddDialogOpen(false);
      setNewParentId(null);

      // Refresh student list
      fetchStudents();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to add student.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Function to view student details
  const handleViewStudent = (student: Student) => {
    setSelectedStudent(student);
    // You can navigate to a detailed view or show a modal
    toast({
      title: "View Student",
      description: `Viewing ${student.firstName} ${student.lastName}'s details.`,
    });
  };

  // Function to edit student
  const handleEditStudent = (student: Student) => {
    // Navigate to edit page or open edit modal
    toast({
      title: "Edit Student",
      description: `Editing ${student.firstName} ${student.lastName}'s details.`,
    });
  };
  
  // Function to delete student
  const handleDeleteStudent = async (studentId: string) => {
    try {
      await studentsService.delete(studentId);

      toast({
        title: "Success",
        description: "Student deleted successfully.",
      });

      // Refresh student list
      fetchStudents();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete student.",
        variant: "destructive",
      });
    }
  };
  
  // Filter students based on search query
  const filteredStudents = students.filter(student => {
    const fullName = `${student.firstName} ${student.lastName}`.toLowerCase();
    const query = searchQuery.toLowerCase();
    return fullName.includes(query) ||
           student.studentId.toLowerCase().includes(query) ||
           student.gradeLevel.toLowerCase().includes(query);
  });

  return (
    <DashboardLayout role="admin" title="Students Management">
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Students Management</h1>
            <p className="text-muted-foreground">
              Manage all student records and enrollment
            </p>
          </div>
          
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="bg-promise-500 hover:bg-promise-600">
                <Plus className="mr-2 h-4 w-4" /> Add Student
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Add New Student</DialogTitle>
                <DialogDescription>
                  Register a new student to Promise Academy
                </DialogDescription>
              </DialogHeader>
              
              <Tabs defaultValue="add-student" value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="add-student">Student Details</TabsTrigger>
                  <TabsTrigger value="add-parent">Register Parent</TabsTrigger>
                </TabsList>
                
                <TabsContent value="add-student">
                  <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="firstName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>First Name</FormLabel>
                              <FormControl>
                                <Input placeholder="John" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="lastName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Last Name</FormLabel>
                              <FormControl>
                                <Input placeholder="Doe" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="dateOfBirth"
                          render={({ field }) => (
                            <FormItem className="flex flex-col">
                              <FormLabel>Date of Birth</FormLabel>
                              <Popover>
                                <PopoverTrigger asChild>
                                  <FormControl>
                                    <Button
                                      variant={"outline"}
                                      className={cn(
                                        "pl-3 text-left font-normal",
                                        !field.value && "text-muted-foreground"
                                      )}
                                    >
                                      {field.value ? (
                                        format(field.value, "PPP")
                                      ) : (
                                        <span>Pick a date</span>
                                      )}
                                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                    </Button>
                                  </FormControl>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0" align="start">
                                  <Calendar
                                    mode="single"
                                    selected={field.value}
                                    onSelect={field.onChange}
                                    disabled={(date) =>
                                      date > new Date() || date < new Date("1900-01-01")
                                    }
                                    initialFocus
                                  />
                                </PopoverContent>
                              </Popover>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="gradeLevel"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Grade Level</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select grade level" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="Preschool">Preschool</SelectItem>
                                  <SelectItem value="Kindergarten">Kindergarten</SelectItem>
                                  <SelectItem value="Grade 1">Grade 1</SelectItem>
                                  <SelectItem value="Grade 2">Grade 2</SelectItem>
                                  <SelectItem value="Grade 3">Grade 3</SelectItem>
                                  <SelectItem value="Grade 4">Grade 4</SelectItem>
                                  <SelectItem value="Grade 5">Grade 5</SelectItem>
                                  <SelectItem value="Grade 6">Grade 6</SelectItem>
                                  <SelectItem value="Grade 7">Grade 7</SelectItem>
                                  <SelectItem value="Grade 8">Grade 8</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="gender"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Gender</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select gender" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="Male">Male</SelectItem>
                                  <SelectItem value="Female">Female</SelectItem>
                                  <SelectItem value="Other">Other</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="parentId"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Parent</FormLabel>
                              <Select 
                                onValueChange={field.onChange} 
                                value={newParentId || field.value}
                                disabled={!!newParentId}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select parent" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {parents.map(parent => (
                                    <SelectItem key={parent.id} value={parent.id}>
                                      {parent.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                {newParentId ? "Using newly registered parent" : "Select existing parent or register a new one"}
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <FormField
                        control={form.control}
                        name="address"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Address</FormLabel>
                            <FormControl>
                              <Input placeholder="123 School Street, City, State" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <DialogFooter>
                        <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>Cancel</Button>
                        <Button type="submit" disabled={isLoading}>
                          {isLoading ? "Adding..." : "Add Student"}
                        </Button>
                      </DialogFooter>
                    </form>
                  </Form>
                </TabsContent>
                
                <TabsContent value="add-parent">
                  <div className="max-h-[60vh] overflow-y-auto">
                    <UserRegistrationForm 
                      onSuccess={handleParentRegistered} 
                      initialRole="parent"
                      hideRoleSelector={true}
                    />
                  </div>
                </TabsContent>
              </Tabs>
            </DialogContent>
          </Dialog>
        </div>
        
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Students Directory</CardTitle>
            <CardDescription>
              Total of {students.length} students currently enrolled
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="relative mb-4">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search for students..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-promise-500"></div>
              </div>
            ) : (
              <div className="rounded-md border">
                <Table>
                  <TableCaption>List of all enrolled students</TableCaption>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Student ID</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Grade</TableHead>
                      <TableHead>Parent</TableHead>
                      <TableHead className="w-[150px]">Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredStudents.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center h-40">
                          {searchQuery ? "No students found matching your search." : "No students found."}
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredStudents.map((student) => (
                        <TableRow key={student.id}>
                          <TableCell className="font-medium">{student.studentId}</TableCell>
                          <TableCell>
                            {student.firstName} {student.lastName}
                          </TableCell>
                          <TableCell>{student.gradeLevel}</TableCell>
                          <TableCell>
                            {student.parent ? `${student.parent.firstName} ${student.parent.lastName}` : 'No parent assigned'}
                          </TableCell>
                          <TableCell>
                            {student.isActive ? (
                              <Badge variant="default" className="bg-green-500">Active</Badge>
                            ) : (
                              <Badge variant="outline">Inactive</Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button 
                                variant="ghost" 
                                size="icon"
                                onClick={() => handleViewStudent(student)}
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="icon"
                                onClick={() => handleEditStudent(student)}
                              >
                                <Pencil className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="icon"
                                onClick={() => handleDeleteStudent(student.id)}
                              >
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default StudentsManagement;
