
import React, { useState, useEffect } from 'react';
import { CalendarIcon, ChevronLeft, ChevronRight, Plus, Trash } from 'lucide-react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Calendar } from "@/components/ui/calendar"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { useToast } from "@/hooks/use-toast";
import { usersService, classroomsService, subjectsService, timetableService } from "@/lib/firestore";
import { useAuth } from "@/contexts/AuthContext";
import DashboardLayout from "@/components/dashboard/DashboardLayout";

// Define types
type Class = {
  id: string;
  name: string;
};

type Subject = {
  id: string;
  name: string;
};

type Teacher = {
  id: string;
  name: string;
};

type TimetableSlot = {
  id: string;
  day: string;
  time: string;
  class: string;
  subject: string;
  teacher: string;
};

const TimetableManagement = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  // State variables
  const [classes, setClasses] = useState<Class[]>([]);
  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [timetable, setTimetable] = useState<TimetableSlot[]>([]);
  const [isAddingSlot, setIsAddingSlot] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  
  const [newSlot, setNewSlot] = useState({
    day: "Monday",
    time: "09:00",
    class: "",
    subject: "",
    teacher: ""
  });
  
  const [viewDate, setViewDate] = useState(new Date());
  
  const daysOfWeek = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
  
  // Fetch data when component mounts
  useEffect(() => {
    Promise.all([
      fetchClasses(),
      fetchSubjects(),
      fetchTeachers(),
      fetchTimetableSlots()
    ]).finally(() => {
      setIsLoading(false);
    });
  }, []);
  
  // Fetch classes from database
  const fetchClasses = async () => {
    try {
      const data = await classroomsService.getAll();

      const formattedClasses = data.map(classroom => ({
        id: classroom.id,
        name: classroom.name
      }));

      setClasses(formattedClasses);
      if (formattedClasses.length > 0) {
        setNewSlot(prev => ({ ...prev, class: formattedClasses[0].id }));
      }
    } catch (error) {
      console.error('Error fetching classes:', error);
      toast({
        title: "Error",
        description: "Failed to fetch classes.",
        variant: "destructive"
      });
    }
  };
  
  // Fetch subjects from database
  const fetchSubjects = async () => {
    try {
      const data = await subjectsService.getAll();

      const formattedSubjects = data.map(subject => ({
        id: subject.id,
        name: subject.name
      }));

      setSubjects(formattedSubjects);
      if (formattedSubjects.length > 0) {
        setNewSlot(prev => ({ ...prev, subject: formattedSubjects[0].id }));
      }
    } catch (error) {
      console.error('Error fetching subjects:', error);
      toast({
        title: "Error",
        description: "Failed to fetch subjects.",
        variant: "destructive"
      });
    }
  };
  
  // Fetch teachers from database
  const fetchTeachers = async () => {
    try {
      const data = await usersService.getAll();

      const teacherUsers = data.filter(user => user.role === 'teacher');

      const formattedTeachers = teacherUsers.map(teacher => ({
        id: teacher.id,
        name: `${teacher.firstName} ${teacher.lastName}`
      }));

      setTeachers(formattedTeachers);
      if (formattedTeachers.length > 0) {
        setNewSlot(prev => ({ ...prev, teacher: formattedTeachers[0].id }));
      }
    } catch (error) {
      console.error('Error fetching teachers:', error);
      toast({
        title: "Error",
        description: "Failed to fetch teachers.",
        variant: "destructive"
      });
    }
  };
  
  // Fetch timetable slots from database
  const fetchTimetableSlots = async () => {
    try {
      const data = await timetableService.getAll();

      // For now, create some sample timetable slots since we don't have the complex joins
      // In a real implementation, you would store the names directly or make separate queries
      const formattedSlots = data.map(slot => ({
        id: slot.id,
        day: slot.dayOfWeek || 'Monday',
        time: slot.startTime || '09:00',
        class: slot.className || 'Unknown',
        subject: slot.subjectName || 'Unknown',
        teacher: slot.teacherName || 'Unknown'
      }));

      setTimetable(formattedSlots);
    } catch (error) {
      console.error('Error fetching timetable slots:', error);
      toast({
        title: "Error",
        description: "Failed to fetch timetable slots.",
        variant: "destructive"
      });
    }
  };
  
  // Get the current selected day's slots
  const visibleTimetable = timetable.filter(slot => {
    const slotDate = new Date(viewDate);
    const dayIndex = daysOfWeek.indexOf(slot.day);
    slotDate.setDate(viewDate.getDate() - viewDate.getDay() + dayIndex);
    return slot.day === daysOfWeek[slotDate.getDay()];
  });
  
  // Handle adding a new timetable slot
  const handleAddSlot = async () => {
    try {
      setIsLoading(true);

      // Create timetable slot data
      const slotData = {
        dayOfWeek: newSlot.day,
        startTime: newSlot.time,
        classroomId: newSlot.class,
        subjectId: newSlot.subject,
        teacherId: newSlot.teacher,
        className: classes.find(c => c.id === newSlot.class)?.name || 'Unknown',
        subjectName: subjects.find(s => s.id === newSlot.subject)?.name || 'Unknown',
        teacherName: teachers.find(t => t.id === newSlot.teacher)?.name || 'Unknown'
      };

      // Insert into database
      await timetableService.add(slotData);

      toast({
        title: "Success",
        description: "Timetable slot added successfully.",
      });

      // Reset form and close dialog
      setNewSlot({
        day: "Monday",
        time: "09:00",
        class: classes[0]?.id || "",
        subject: subjects[0]?.id || "",
        teacher: teachers[0]?.id || ""
      });

      setIsAddingSlot(false);

      // Refresh timetable slots
      fetchTimetableSlots();
    } catch (error: any) {
      console.error('Error adding timetable slot:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to add timetable slot.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle removing a timetable slot
  const handleRemoveSlot = async (id: string) => {
    try {
      setIsLoading(true);

      await timetableService.delete(id);

      toast({
        title: "Success",
        description: "Timetable slot removed successfully.",
      });

      // Refresh timetable slots
      fetchTimetableSlots();
    } catch (error: any) {
      console.error('Error removing timetable slot:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to remove timetable slot.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <DashboardLayout role="admin" title="Timetable Management">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-800">Timetable Management</h2>
            <p className="text-gray-600">Manage and schedule classes</p>
          </div>
          <Dialog open={isAddingSlot} onOpenChange={setIsAddingSlot}>
            <DialogTrigger asChild>
              <Button className="bg-promise-500 hover:bg-promise-600">
                <Plus className="mr-2 h-4 w-4" /> Add Timetable Slot
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Timetable Slot</DialogTitle>
                <DialogDescription>
                  Schedule a new class for a specific day and time.
                </DialogDescription>
              </DialogHeader>
              
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="day">Day</Label>
                    <Select 
                      value={newSlot.day} 
                      onValueChange={value => setNewSlot({...newSlot, day: value})}
                    >
                      <SelectTrigger id="day">
                        <SelectValue placeholder="Select day" />
                      </SelectTrigger>
                      <SelectContent>
                        {daysOfWeek.map(day => (
                          <SelectItem key={day} value={day}>{day}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="time">Time</Label>
                    <Input 
                      id="time"
                      type="time"
                      value={newSlot.time}
                      onChange={e => setNewSlot({...newSlot, time: e.target.value})}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="class">Class</Label>
                  <Select 
                    value={newSlot.class} 
                    onValueChange={value => setNewSlot({...newSlot, class: value})}
                  >
                    <SelectTrigger id="class">
                      <SelectValue placeholder="Select class" />
                    </SelectTrigger>
                    <SelectContent>
                      {classes.map(cls => (
                        <SelectItem key={cls.id} value={cls.id}>{cls.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="subject">Subject</Label>
                    <Select 
                      value={newSlot.subject} 
                      onValueChange={value => setNewSlot({...newSlot, subject: value})}
                    >
                      <SelectTrigger id="subject">
                        <SelectValue placeholder="Select subject" />
                      </SelectTrigger>
                      <SelectContent>
                        {subjects.map(subject => (
                          <SelectItem key={subject.id} value={subject.id}>{subject.name}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="teacher">Teacher</Label>
                    <Select 
                      value={newSlot.teacher} 
                      onValueChange={value => setNewSlot({...newSlot, teacher: value})}
                    >
                      <SelectTrigger id="teacher">
                        <SelectValue placeholder="Select teacher" />
                      </SelectTrigger>
                      <SelectContent>
                        {teachers.map(teacher => (
                          <SelectItem key={teacher.id} value={teacher.id}>{teacher.name}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
              
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddingSlot(false)}>Cancel</Button>
                <Button onClick={handleAddSlot} disabled={isLoading}>
                  {isLoading ? "Adding..." : "Add Slot"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Timetable</CardTitle>
            <CardDescription>View and manage class schedules</CardDescription>
          </CardHeader>
          <CardContent className="pl-6">
            <div className="flex items-center space-x-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-[200px] justify-start text-left font-normal",
                      !viewDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {viewDate ? format(viewDate, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={viewDate}
                    onSelect={(date) => date && setViewDate(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <ChevronLeft 
                className="h-5 w-5 cursor-pointer text-gray-500 hover:text-gray-700"
                onClick={() => {
                  const newDate = new Date(viewDate);
                  newDate.setDate(newDate.getDate() - 1);
                  setViewDate(newDate);
                }}
              />
              <ChevronRight 
                className="h-5 w-5 cursor-pointer text-gray-500 hover:text-gray-700"
                onClick={() => {
                  const newDate = new Date(viewDate);
                  newDate.setDate(newDate.getDate() + 1);
                  setViewDate(newDate);
                }}
              />
            </div>
            
            <div className="rounded-md border mt-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Time</TableHead>
                    <TableHead>Class</TableHead>
                    <TableHead>Subject</TableHead>
                    <TableHead>Teacher</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={5} className="h-24 text-center">
                        <div className="flex justify-center">
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-promise-500"></div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : visibleTimetable.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="h-24 text-center text-muted-foreground">
                        No timetable slots found for {daysOfWeek[viewDate.getDay()]}.
                      </TableCell>
                    </TableRow>
                  ) : (
                    visibleTimetable.map((slot) => (
                      <TableRow key={slot.id}>
                        <TableCell>{slot.time}</TableCell>
                        <TableCell>{slot.class}</TableCell>
                        <TableCell>{slot.subject}</TableCell>
                        <TableCell>{slot.teacher}</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm" onClick={() => handleRemoveSlot(slot.id)}>
                            <Trash className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default TimetableManagement;
