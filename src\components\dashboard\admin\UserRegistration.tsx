
import { useState } from "react";
import DashboardLayout from "@/components/dashboard/DashboardLayout";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import TeacherRegistrationForm from "@/components/dashboard/admin/TeacherRegistrationForm";
import ParentRegistrationForm from "@/components/dashboard/admin/ParentRegistrationForm";

const UserRegistration = () => {
  const [activeTab, setActiveTab] = useState("teachers");

  return (
    <DashboardLayout role="admin" title="User Registration">
      <div className="space-y-4">
        <h1 className="text-2xl font-semibold">User Registration</h1>
        <p className="text-muted-foreground">
          Register new teachers or parents in the system. They will receive an email with login instructions.
        </p>

        <Card>
          <CardContent className="p-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid grid-cols-2 mb-4">
                <TabsTrigger value="teachers">Register Teachers</TabsTrigger>
                <TabsTrigger value="parents">Register Parents</TabsTrigger>
              </TabsList>
              
              <TabsContent value="teachers">
                <TeacherRegistrationForm />
              </TabsContent>
              
              <TabsContent value="parents">
                <ParentRegistrationForm />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default UserRegistration;
