
import { <PERSON>, Clock, FileText, Heart } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from "@/components/ui/tabs";
import StatCard from "../StatCard";
import DashboardLayout from "../DashboardLayout";
import { Progress } from "@/components/ui/progress";

const ParentDashboard = () => {
  return (
    <DashboardLayout role="parent" title="Parent Dashboard">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Attendance Rate"
          value="95%"
          icon={<Clock className="h-4 w-4 text-muted-foreground" />}
          description="Last 30 days"
        />
        <StatCard
          title="Next Event"
          value="May 20"
          icon={<Calendar className="h-4 w-4 text-muted-foreground" />}
          description="Spring Festival"
        />
        <StatCard
          title="Health Status"
          value="Excellent"
          icon={<Heart className="h-4 w-4 text-muted-foreground" />}
          description="Last check: May 10"
        />
        <StatCard
          title="Upcoming Reports"
          value="1"
          icon={<FileText className="h-4 w-4 text-muted-foreground" />}
          description="End of month assessment"
        />
      </div>

      <div className="mt-4">
        <Card className="overflow-hidden">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Emma's Progress</CardTitle>
                <CardDescription>Pre-K (4 years) - Ms. Davis's Class</CardDescription>
              </div>
              <Avatar className="h-14 w-14 border-2 border-promise-200">
                <AvatarImage src="https://ui-avatars.com/api/?name=ET&background=random" alt="Emma Thompson" />
                <AvatarFallback>ET</AvatarFallback>
              </Avatar>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <Tabs defaultValue="academics">
              <div className="border-b px-6">
                <TabsList className="w-full justify-start rounded-none border-b-0 p-0">
                  <TabsTrigger
                    value="academics"
                    className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-promise-500 data-[state=active]:shadow-none"
                  >
                    Academics
                  </TabsTrigger>
                  <TabsTrigger
                    value="social"
                    className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-promise-500 data-[state=active]:shadow-none"
                  >
                    Social Development
                  </TabsTrigger>
                  <TabsTrigger
                    value="activities"
                    className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-promise-500 data-[state=active]:shadow-none"
                  >
                    Activities
                  </TabsTrigger>
                </TabsList>
              </div>
              
              <TabsContent value="academics" className="p-6 space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Language & Literacy</span>
                    <span className="text-sm">Advanced</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Progress value={90} className="h-2" />
                    <span className="text-sm font-medium">90%</span>
                  </div>
                  <p className="text-sm text-muted-foreground">Emma excels in storytelling and letter recognition. She's beginning to read simple words.</p>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Mathematics</span>
                    <span className="text-sm">On Target</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Progress value={75} className="h-2 bg-muted [&>*]:bg-meadow-500" />
                    <span className="text-sm font-medium">75%</span>
                  </div>
                  <p className="text-sm text-muted-foreground">She counts to 30 confidently and recognizes basic patterns. We're working on simple addition concepts.</p>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Science Concepts</span>
                    <span className="text-sm">Advanced</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Progress value={85} className="h-2 bg-muted [&>*]:bg-sunlight-600" />
                    <span className="text-sm font-medium">85%</span>
                  </div>
                  <p className="text-sm text-muted-foreground">Shows strong curiosity about natural world. Particularly interested in our plant growth project.</p>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Fine Motor Skills</span>
                    <span className="text-sm">On Target</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Progress value={80} className="h-2 bg-muted [&>*]:bg-peach-500" />
                    <span className="text-sm font-medium">80%</span>
                  </div>
                  <p className="text-sm text-muted-foreground">Good progress with scissors and pencil control. Drawing becoming more detailed.</p>
                </div>
              </TabsContent>
              
              <TabsContent value="social" className="p-6">
                <div className="space-y-6">
                  <div>
                    <h4 className="font-medium mb-2">Peer Relationships</h4>
                    <div className="flex items-center gap-2 mb-3">
                      <Progress value={95} className="h-2 bg-muted [&>*]:bg-promise-500" />
                      <span className="text-sm font-medium">Excellent</span>
                    </div>
                    <p className="text-sm text-muted-foreground">Emma forms friendships easily and shows kindness to her classmates. She's particularly helpful with new students.</p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Emotional Regulation</h4>
                    <div className="flex items-center gap-2 mb-3">
                      <Progress value={80} className="h-2 bg-muted [&>*]:bg-meadow-500" />
                      <span className="text-sm font-medium">Good</span>
                    </div>
                    <p className="text-sm text-muted-foreground">Usually manages emotions well. Occasionally needs support during transitions, but shows improvement.</p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Following Directions</h4>
                    <div className="flex items-center gap-2 mb-3">
                      <Progress value={90} className="h-2 bg-muted [&>*]:bg-sunlight-600" />
                      <span className="text-sm font-medium">Excellent</span>
                    </div>
                    <p className="text-sm text-muted-foreground">Listens attentively and follows multi-step instructions. Often helps remind other children of classroom rules.</p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Teacher Notes</h4>
                    <p className="text-sm text-muted-foreground italic">
                      "Emma is a joy to have in class. She demonstrates leadership qualities and shows great empathy toward her peers. She's particularly skilled at including others in activities and sharing materials."
                    </p>
                    <p className="text-sm text-muted-foreground italic mt-3">
                      "During our community helper unit, Emma showed exceptional interest in the roles that help others, particularly doctors and teachers."
                    </p>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="activities" className="p-6">
                <div className="space-y-4">
                  <h4 className="font-medium">Recent Projects</h4>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="border rounded-md overflow-hidden">
                      <div className="aspect-video bg-muted flex items-center justify-center">
                        <img 
                          src="https://images.unsplash.com/photo-1501854140801-50d01698950b" 
                          alt="Nature artwork" 
                          className="object-cover w-full h-full"
                        />
                      </div>
                      <div className="p-3">
                        <h5 className="font-medium">Spring Nature Collage</h5>
                        <p className="text-sm text-muted-foreground">Created May 10, 2025</p>
                      </div>
                    </div>
                    
                    <div className="border rounded-md overflow-hidden">
                      <div className="aspect-video bg-muted flex items-center justify-center">
                        <img 
                          src="https://images.unsplash.com/photo-1506744038136-46273834b3fb" 
                          alt="Butterfly lifecycle" 
                          className="object-cover w-full h-full"
                        />
                      </div>
                      <div className="p-3">
                        <h5 className="font-medium">Butterfly Lifecycle Drawing</h5>
                        <p className="text-sm text-muted-foreground">Created May 3, 2025</p>
                      </div>
                    </div>
                  </div>
                  
                  <h4 className="font-medium mt-8">Upcoming Field Trips</h4>
                  
                  <div className="space-y-3">
                    <div className="bg-muted/50 p-4 rounded-md border">
                      <div className="flex justify-between items-start">
                        <div>
                          <h5 className="font-medium">Local Zoo Visit</h5>
                          <p className="text-sm text-muted-foreground">May 28, 2025 • 9:00 AM - 2:00 PM</p>
                          <p className="text-sm mt-2">Permission slip due by May 21st</p>
                        </div>
                        <Button variant="outline" size="sm">Submit Form</Button>
                      </div>
                    </div>
                    
                    <div className="bg-muted/50 p-4 rounded-md border">
                      <div className="flex justify-between items-start">
                        <div>
                          <h5 className="font-medium">Children's Museum</h5>
                          <p className="text-sm text-muted-foreground">June 12, 2025 • 10:00 AM - 3:00 PM</p>
                          <p className="text-sm mt-2">Volunteers needed (3 parent helpers)</p>
                        </div>
                        <Button variant="outline" size="sm">Volunteer</Button>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter className="border-t p-6 flex justify-between">
            <Button variant="outline">Message Teacher</Button>
            <Button>View Full Report</Button>
          </CardFooter>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2 mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Recent Communications</CardTitle>
            <CardDescription>Messages from teachers and staff</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start gap-3 pb-4 border-b">
                <Avatar className="h-10 w-10">
                  <AvatarImage src="https://ui-avatars.com/api/?name=SD&background=random" alt="Sarah Davis" />
                  <AvatarFallback>SD</AvatarFallback>
                </Avatar>
                <div>
                  <div className="flex items-center gap-2">
                    <p className="font-medium">Ms. Sarah Davis</p>
                    <span className="text-xs text-muted-foreground">Teacher</span>
                  </div>
                  <p className="text-sm mt-1">Emma has been enjoying our butterfly project! She's been very engaged during our science activities.</p>
                  <div className="flex items-center gap-3 mt-2">
                    <span className="text-xs text-muted-foreground">May 15, 2025 • 2:34 PM</span>
                    <Button variant="ghost" size="sm" className="h-auto py-1 px-2">Reply</Button>
                  </div>
                </div>
              </div>
              
              <div className="flex items-start gap-3 pb-4 border-b">
                <Avatar className="h-10 w-10">
                  <AvatarImage src="https://ui-avatars.com/api/?name=AW&background=random" alt="Admin Wilson" />
                  <AvatarFallback>AW</AvatarFallback>
                </Avatar>
                <div>
                  <div className="flex items-center gap-2">
                    <p className="font-medium">Admin Office</p>
                    <span className="text-xs text-muted-foreground">Administrative</span>
                  </div>
                  <p className="text-sm mt-1">Reminder: Tuition payment for June is due by May 25th. Please submit payment online or at the front office.</p>
                  <div className="flex items-center gap-3 mt-2">
                    <span className="text-xs text-muted-foreground">May 12, 2025 • 9:15 AM</span>
                    <Button variant="ghost" size="sm" className="h-auto py-1 px-2">Reply</Button>
                  </div>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <Avatar className="h-10 w-10">
                  <AvatarImage src="https://ui-avatars.com/api/?name=MT&background=random" alt="Mark Thompson" />
                  <AvatarFallback>MT</AvatarFallback>
                </Avatar>
                <div>
                  <div className="flex items-center gap-2">
                    <p className="font-medium">Mr. Mark Thompson</p>
                    <span className="text-xs text-muted-foreground">Music Teacher</span>
                  </div>
                  <p className="text-sm mt-1">Emma has been selected to perform a solo in our Spring Festival on May 20th! She's been practicing "Twinkle Twinkle" on the xylophone.</p>
                  <div className="flex items-center gap-3 mt-2">
                    <span className="text-xs text-muted-foreground">May 8, 2025 • 3:45 PM</span>
                    <Button variant="ghost" size="sm" className="h-auto py-1 px-2">Reply</Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full">View All Messages</Button>
          </CardFooter>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Upcoming Events</CardTitle>
            <CardDescription>School calendar highlights</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-4 items-start">
              <div className="h-12 w-12 rounded-md bg-promise-100 text-promise-800 flex flex-col items-center justify-center">
                <span className="text-xs font-bold">MAY</span>
                <span className="text-base font-bold">20</span>
              </div>
              <div>
                <p className="font-medium">Spring Festival</p>
                <p className="text-sm text-muted-foreground">10:00 AM - 12:00 PM</p>
                <p className="text-sm mt-1">Annual celebration showcasing student performances and art</p>
              </div>
            </div>
            
            <div className="flex gap-4 items-start">
              <div className="h-12 w-12 rounded-md bg-meadow-100 text-meadow-800 flex flex-col items-center justify-center">
                <span className="text-xs font-bold">MAY</span>
                <span className="text-base font-bold">25</span>
              </div>
              <div>
                <p className="font-medium">Memorial Day</p>
                <p className="text-sm text-muted-foreground">All Day</p>
                <p className="text-sm mt-1">School closed for Memorial Day holiday</p>
              </div>
            </div>
            
            <div className="flex gap-4 items-start">
              <div className="h-12 w-12 rounded-md bg-sunlight-100 text-sunlight-900 flex flex-col items-center justify-center">
                <span className="text-xs font-bold">MAY</span>
                <span className="text-base font-bold">28</span>
              </div>
              <div>
                <p className="font-medium">Zoo Field Trip</p>
                <p className="text-sm text-muted-foreground">9:00 AM - 2:00 PM</p>
                <p className="text-sm mt-1">Pre-K class trip to the local zoo</p>
              </div>
            </div>
            
            <div className="flex gap-4 items-start">
              <div className="h-12 w-12 rounded-md bg-peach-100 text-peach-800 flex flex-col items-center justify-center">
                <span className="text-xs font-bold">JUN</span>
                <span className="text-base font-bold">10</span>
              </div>
              <div>
                <p className="font-medium">Parent-Teacher Conferences</p>
                <p className="text-sm text-muted-foreground">1:00 PM - 7:00 PM</p>
                <p className="text-sm mt-1">End-of-year progress discussions</p>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full">View Full Calendar</Button>
          </CardFooter>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default ParentDashboard;
