
import { useState } from "react";
import DashboardLayout from "../DashboardLayout";
import { Card, CardContent, CardDescription, CardHeader, Card<PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Heart, Calendar, AlertCircle, Check, X, Clock, Send, Download } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";

const healthHistory = [
  {
    date: "May 8, 2025",
    issue: "Seasonal Allergies",
    symptoms: "Runny nose, watery eyes",
    action: "Administered prescribed allergy medication",
    notes: "Symptoms improved after lunch. Continued regular activities.",
    resolved: true,
    reportedBy: "Ms. <PERSON>"
  },
  {
    date: "April 15, 2025",
    issue: "Minor Scrape",
    symptoms: "Small scrape on right knee from playground",
    action: "Cleaned wound, applied antiseptic and bandage",
    notes: "Occurred during outdoor play. Emma was comforted and returned to activities.",
    resolved: true,
    reportedBy: "Mr. Johnson"
  },
  {
    date: "March 23, 2025",
    issue: "Fever",
    symptoms: "Temperature of 100.4°F, fatigue",
    action: "Parents contacted and picked up Emma. Recommended doctor visit.",
    notes: "Absent for 2 days following incident. Doctor confirmed seasonal flu.",
    resolved: true,
    reportedBy: "Nurse Wilson"
  }
];

const healthNotifications = [
  {
    id: 1,
    date: "May 15, 2025",
    title: "Health Check Reminder",
    content: "Annual health checkup forms due by June 1st",
    priority: "normal",
    read: false
  },
  {
    id: 2,
    date: "May 10, 2025",
    title: "Allergy Alert",
    content: "Reminder to refill Emma's allergy medication for school use",
    priority: "high",
    read: true
  },
  {
    id: 3,
    date: "April 28, 2025",
    title: "Nutrition Update",
    content: "Emma has been enjoying the new healthy snack options in class",
    priority: "normal",
    read: true
  }
];

const ParentHealth = () => {
  const [absenceReason, setAbsenceReason] = useState("");
  const [absenceType, setAbsenceType] = useState("");
  const [absenceDate, setAbsenceDate] = useState("");
  const [absenceReturnDate, setAbsenceReturnDate] = useState("");
  const { toast } = useToast();

  const handleSubmitAbsence = (e) => {
    e.preventDefault();
    if (!absenceReason || !absenceType || !absenceDate) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }
    
    toast({
      title: "Absence reported",
      description: "Your absence notification has been sent successfully",
    });
    
    // Reset the form
    setAbsenceReason("");
    setAbsenceType("");
    setAbsenceDate("");
    setAbsenceReturnDate("");
  };

  const handleMarkAsRead = (id) => {
    toast({
      title: "Notification marked as read",
      description: "The notification has been marked as read",
    });
  };

  return (
    <DashboardLayout role="parent" title="Child's Health">
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold">Emma Thompson's Health Portal</h2>
          <p className="text-muted-foreground">Pre-K (4 years) - Ms. Davis's Class</p>
        </div>

        <Card>
          <CardHeader className="pb-3 border-b">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Heart className="h-5 w-5 text-rose-500" />
                  Health Dashboard
                </CardTitle>
                <CardDescription>Monitor and manage Emma's health information</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <Tabs defaultValue="notifications">
              <div className="border-b px-6">
                <TabsList className="w-full justify-start rounded-none border-b-0 p-0">
                  <TabsTrigger
                    value="notifications"
                    className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-promise-500 data-[state=active]:shadow-none"
                  >
                    Notifications
                  </TabsTrigger>
                  <TabsTrigger
                    value="report"
                    className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-promise-500 data-[state=active]:shadow-none"
                  >
                    Report Absence
                  </TabsTrigger>
                  <TabsTrigger
                    value="history"
                    className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-promise-500 data-[state=active]:shadow-none"
                  >
                    Health History
                  </TabsTrigger>
                </TabsList>
              </div>
              
              <TabsContent value="notifications" className="p-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Health Notifications</h3>
                  
                  {healthNotifications.map(notification => (
                    <div 
                      key={notification.id} 
                      className={`border rounded-lg p-4 ${notification.read ? 'bg-white' : 'bg-muted/20'}`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex gap-3">
                          {notification.priority === "high" ? (
                            <AlertCircle className="h-5 w-5 text-red-500" />
                          ) : (
                            <Heart className="h-5 w-5 text-slate-400" />
                          )}
                          <div>
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium">{notification.title}</h4>
                              {!notification.read && (
                                <Badge className="bg-promise-500">New</Badge>
                              )}
                              {notification.priority === "high" && (
                                <Badge variant="destructive">Important</Badge>
                              )}
                            </div>
                            <div className="flex items-center gap-1 mt-1">
                              <Calendar className="h-3.5 w-3.5 text-muted-foreground" />
                              <span className="text-xs text-muted-foreground">{notification.date}</span>
                            </div>
                            <p className="mt-2 text-sm">{notification.content}</p>
                          </div>
                        </div>
                        {!notification.read && (
                          <Button 
                            size="sm" 
                            variant="outline" 
                            onClick={() => handleMarkAsRead(notification.id)}
                          >
                            Mark as read
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                  
                  {healthNotifications.length === 0 && (
                    <div className="text-center py-10">
                      <Check className="h-10 w-10 text-green-500 mx-auto mb-3" />
                      <p className="text-muted-foreground">No health notifications at this time</p>
                    </div>
                  )}
                </div>
              </TabsContent>
              
              <TabsContent value="report" className="p-6">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium">Report an Absence</h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      Use this form to notify the school when Emma will be absent due to illness or other reasons
                    </p>
                  </div>
                  
                  <form onSubmit={handleSubmitAbsence} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="absenceDate">Absence Date*</Label>
                        <Input 
                          id="absenceDate" 
                          type="date"
                          value={absenceDate}
                          onChange={(e) => setAbsenceDate(e.target.value)}
                          required
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="returnDate">Expected Return Date</Label>
                        <Input 
                          id="returnDate" 
                          type="date"
                          value={absenceReturnDate}
                          onChange={(e) => setAbsenceReturnDate(e.target.value)}
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="absenceType">Type of Absence*</Label>
                      <Select 
                        value={absenceType}
                        onValueChange={setAbsenceType}
                        required
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select absence type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="illness">Illness/Medical</SelectItem>
                          <SelectItem value="appointment">Medical Appointment</SelectItem>
                          <SelectItem value="family">Family Emergency</SelectItem>
                          <SelectItem value="vacation">Vacation/Travel</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="absenceReason">Details/Reason*</Label>
                      <Textarea 
                        id="absenceReason" 
                        placeholder="Please provide details about the absence..."
                        value={absenceReason}
                        onChange={(e) => setAbsenceReason(e.target.value)}
                        rows={4}
                        required
                      />
                    </div>
                    
                    <div className="flex justify-end gap-2">
                      <Button type="reset" variant="outline">Cancel</Button>
                      <Button type="submit">
                        <Send className="h-4 w-4 mr-2" /> Submit Report
                      </Button>
                    </div>
                  </form>
                </div>
              </TabsContent>
              
              <TabsContent value="history" className="p-6">
                <div className="space-y-6">
                  <h3 className="text-lg font-medium">Health Incident History</h3>
                  
                  <div className="space-y-4">
                    {healthHistory.map((incident, index) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex items-start gap-4">
                          <div className="rounded-full bg-muted h-10 w-10 flex items-center justify-center">
                            {incident.resolved ? (
                              <Check className="h-5 w-5 text-green-500" />
                            ) : (
                              <Clock className="h-5 w-5 text-amber-500" />
                            )}
                          </div>
                          
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <h4 className="font-medium">{incident.issue}</h4>
                                <Badge variant={incident.resolved ? "outline" : "secondary"}>
                                  {incident.resolved ? "Resolved" : "Ongoing"}
                                </Badge>
                              </div>
                              <span className="text-xs text-muted-foreground">{incident.date}</span>
                            </div>
                            
                            <div className="mt-2 space-y-2 text-sm">
                              <p><span className="font-medium">Symptoms:</span> {incident.symptoms}</p>
                              <p><span className="font-medium">Action taken:</span> {incident.action}</p>
                              <p><span className="font-medium">Additional notes:</span> {incident.notes}</p>
                              <p className="text-xs text-muted-foreground">Reported by: {incident.reportedBy}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter className="border-t p-6 flex justify-between">
            <Button variant="outline">Contact School Nurse</Button>
            <Button>Update Health Information</Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Health Records</CardTitle>
            <CardDescription>Medical and health documentation</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start justify-between border-b pb-4">
                <div className="flex items-start gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src="https://ui-avatars.com/api/?name=Dr+MB&background=random" alt="Dr. Martinez" />
                    <AvatarFallback>DR</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">Annual Physical Examination</p>
                    <p className="text-sm text-muted-foreground">Completed January 15, 2025</p>
                    <p className="text-sm mt-1">By Dr. Martinez at Bayview Pediatrics</p>
                  </div>
                </div>
                <Button size="sm" variant="outline">
                  <Download className="h-4 w-4 mr-1" /> Download
                </Button>
              </div>
              
              <div className="flex items-start justify-between border-b pb-4">
                <div className="flex items-start gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src="https://ui-avatars.com/api/?name=Dr+LC&background=random" alt="Dr. Chen" />
                    <AvatarFallback>DC</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">Immunization Records</p>
                    <p className="text-sm text-muted-foreground">Updated March 5, 2025</p>
                    <p className="text-sm mt-1">All required immunizations current</p>
                  </div>
                </div>
                <Button size="sm" variant="outline">
                  <Download className="h-4 w-4 mr-1" /> Download
                </Button>
              </div>
              
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src="https://ui-avatars.com/api/?name=NW&background=random" alt="Nurse Wilson" />
                    <AvatarFallback>NW</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">Allergy Action Plan</p>
                    <p className="text-sm text-muted-foreground">Created December 10, 2024</p>
                    <p className="text-sm mt-1">Seasonal allergy management protocol</p>
                  </div>
                </div>
                <Button size="sm" variant="outline">
                  <Download className="h-4 w-4 mr-1" /> Download
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default ParentHealth;
