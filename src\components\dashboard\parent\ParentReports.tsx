
import DashboardLayout from "../DashboardLayout";
import { <PERSON>, CardContent, CardDescription, CardHeader, Card<PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { FileText, Download, Calendar, ArrowUpRight } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { BarChart } from "@/components/ui/charts/BarChart";

const reports = [
  { 
    id: 1,
    title: "End of Term Progress Report",
    date: "April 15, 2025",
    description: "Comprehensive assessment of academic and social development",
    author: "Ms. <PERSON>",
    status: "final"
  },
  {
    id: 2,
    title: "Monthly Development Update",
    date: "May 5, 2025",
    description: "Regular update on <PERSON>'s progress in key learning areas",
    author: "Ms. <PERSON>",
    status: "final"
  },
  {
    id: 3,
    title: "Literacy Assessment",
    date: "March 20, 2025",
    description: "Detailed evaluation of reading and writing skills",
    author: "Ms. <PERSON>",
    status: "final"
  },
  {
    id: 4,
    title: "End of Year Report",
    date: "June 15, 2025 (Upcoming)",
    description: "Final assessment for the academic year",
    author: "Ms. <PERSON>",
    status: "upcoming"
  }
];

const progressData = [
  { month: "Jan", Language: 65, Math: 55, Social: 70, Motor: 60 },
  { month: "Feb", Language: 70, Math: 60, Social: 75, Motor: 65 },
  { month: "Mar", Language: 75, Math: 65, Social: 80, Motor: 70 },
  { month: "Apr", Language: 80, Math: 70, Social: 85, Motor: 75 },
  { month: "May", Language: 90, Math: 75, Social: 95, Motor: 80 },
];

const ParentReports = () => {
  return (
    <DashboardLayout role="parent" title="Child's Reports">
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold">Emma Thompson's Reports</h2>
          <p className="text-muted-foreground">Pre-K (4 years) - Ms. Davis's Class</p>
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <div className="space-y-1">
                <CardTitle>Development Progress</CardTitle>
                <CardDescription>Growth over the academic year</CardDescription>
              </div>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <BarChart
                  data={progressData}
                  index="month"
                  categories={["Language", "Math", "Social", "Motor"]}
                  colors={["#9b87f5", "#65cf9a", "#f79044", "#3b82f6"]}
                  valueFormatter={(value) => `${value}%`}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Current Skills Assessment</CardTitle>
              <CardDescription>Updated May 10, 2025</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Language & Literacy</span>
                  <span className="text-sm">Advanced</span>
                </div>
                <div className="flex items-center gap-2">
                  <Progress value={90} className="h-2" />
                  <span className="text-sm font-medium">90%</span>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Mathematics</span>
                  <span className="text-sm">On Target</span>
                </div>
                <div className="flex items-center gap-2">
                  <Progress value={75} className="h-2 bg-muted [&>*]:bg-meadow-500" />
                  <span className="text-sm font-medium">75%</span>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Social Skills</span>
                  <span className="text-sm">Advanced</span>
                </div>
                <div className="flex items-center gap-2">
                  <Progress value={95} className="h-2 bg-muted [&>*]:bg-sunlight-600" />
                  <span className="text-sm font-medium">95%</span>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Fine Motor Skills</span>
                  <span className="text-sm">On Target</span>
                </div>
                <div className="flex items-center gap-2">
                  <Progress value={80} className="h-2 bg-muted [&>*]:bg-peach-500" />
                  <span className="text-sm font-medium">80%</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Report Documents</CardTitle>
            <CardDescription>View and download official reports</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {reports.map(report => (
                <div key={report.id} className="flex items-start justify-between border-b pb-4 last:border-0 last:pb-0">
                  <div className="flex items-start gap-3">
                    <div className="mt-1 h-9 w-9 rounded-md bg-muted flex items-center justify-center">
                      <FileText className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <div>
                      <h4 className="font-medium">{report.title}</h4>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground mt-1">
                        <Calendar className="h-3.5 w-3.5" />
                        <span>{report.date}</span>
                      </div>
                      <p className="text-sm mt-1">{report.description}</p>
                      <p className="text-xs text-muted-foreground mt-1">Prepared by: {report.author}</p>
                    </div>
                  </div>
                  <div>
                    {report.status === "final" ? (
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline" className="h-8">
                          <Download className="h-3.5 w-3.5 mr-1" /> Download
                        </Button>
                        <Button size="sm" className="h-8">
                          <ArrowUpRight className="h-3.5 w-3.5 mr-1" /> View
                        </Button>
                      </div>
                    ) : (
                      <Button size="sm" variant="outline" disabled className="h-8">
                        Upcoming
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter className="border-t pt-6 flex justify-between">
            <Button variant="outline">Request Additional Report</Button>
            <Button variant="default">Schedule Discussion</Button>
          </CardFooter>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default ParentReports;
