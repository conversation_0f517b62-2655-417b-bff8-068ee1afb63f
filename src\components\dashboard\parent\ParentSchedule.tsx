
import DashboardLayout from "../DashboardLayout";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Calendar, Clock } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";

const scheduleData = [
  {
    day: "Monday",
    activities: [
      { time: "08:30 - 09:15", activity: "Morning Circle", teacher: "<PERSON><PERSON> <PERSON>", location: "Main Classroom" },
      { time: "09:30 - 10:15", activity: "Language Arts", teacher: "<PERSON><PERSON> <PERSON>", location: "Main Classroom" },
      { time: "10:30 - 11:15", activity: "Math Activities", teacher: "<PERSON><PERSON> <PERSON>", location: "Main Classroom" },
      { time: "11:30 - 12:15", activity: "Lunch", teacher: "", location: "Cafeteria" },
      { time: "12:30 - 13:15", activity: "Outdoor Play", teacher: "<PERSON><PERSON> <PERSON>", location: "Playground" },
      { time: "13:30 - 14:15", activity: "Science Exploration", teacher: "<PERSON><PERSON> <PERSON>", location: "Science Corner" },
      { time: "14:30 - 15:00", activity: "Story Time & Dismissal", teacher: "Ms. <PERSON>", location: "Reading Nook" }
    ]
  },
  {
    day: "Tuesday",
    activities: [
      { time: "08:30 - 09:15", activity: "Morning Circle", teacher: "Ms. <PERSON>", location: "Main Classroom" },
      { time: "09:30 - 10:15", activity: "Art Class", teacher: "Ms. <PERSON>", location: "Art Room" },
      { time: "10:30 - 11:15", activity: "Math Activities", teacher: "Ms. <PERSON>", location: "Main Classroom" },
      { time: "11:30 - 12:15", activity: "Lunch", teacher: "", location: "Cafeteria" },
      { time: "12:30 - 13:15", activity: "Music & Movement", teacher: "Mr. Thompson", location: "Music Room" },
      { time: "13:30 - 14:15", activity: "Center Time", teacher: "Ms. Davis", location: "Main Classroom" },
      { time: "14:30 - 15:00", activity: "Story Time & Dismissal", teacher: "Ms. Davis", location: "Reading Nook" }
    ]
  },
  {
    day: "Wednesday",
    activities: [
      { time: "08:30 - 09:15", activity: "Morning Circle", teacher: "Ms. Davis", location: "Main Classroom" },
      { time: "09:30 - 10:15", activity: "Physical Education", teacher: "Mr. Johnson", location: "Gym" },
      { time: "10:30 - 11:15", activity: "Language Arts", teacher: "Ms. Davis", location: "Main Classroom" },
      { time: "11:30 - 12:15", activity: "Lunch", teacher: "", location: "Cafeteria" },
      { time: "12:30 - 13:15", activity: "Outdoor Play", teacher: "Mr. Thompson", location: "Playground" },
      { time: "13:30 - 14:15", activity: "STEM Activities", teacher: "Ms. Davis", location: "STEM Lab" },
      { time: "14:30 - 15:00", activity: "Story Time & Dismissal", teacher: "Ms. Davis", location: "Reading Nook" }
    ]
  },
  {
    day: "Thursday",
    activities: [
      { time: "08:30 - 09:15", activity: "Morning Circle", teacher: "Ms. Davis", location: "Main Classroom" },
      { time: "09:30 - 10:15", activity: "Language Arts", teacher: "Ms. Davis", location: "Main Classroom" },
      { time: "10:30 - 11:15", activity: "Math Activities", teacher: "Ms. Davis", location: "Main Classroom" },
      { time: "11:30 - 12:15", activity: "Lunch", teacher: "", location: "Cafeteria" },
      { time: "12:30 - 13:15", activity: "Library Time", teacher: "Ms. Wilson", location: "Library" },
      { time: "13:30 - 14:15", activity: "Social Studies", teacher: "Ms. Davis", location: "Main Classroom" },
      { time: "14:30 - 15:00", activity: "Story Time & Dismissal", teacher: "Ms. Davis", location: "Reading Nook" }
    ]
  },
  {
    day: "Friday",
    activities: [
      { time: "08:30 - 09:15", activity: "Morning Circle", teacher: "Ms. Davis", location: "Main Classroom" },
      { time: "09:30 - 10:15", activity: "Show and Tell", teacher: "Ms. Davis", location: "Main Classroom" },
      { time: "10:30 - 11:15", activity: "Fun Friday Activities", teacher: "Ms. Davis", location: "Main Classroom" },
      { time: "11:30 - 12:15", activity: "Lunch", teacher: "", location: "Cafeteria" },
      { time: "12:30 - 13:15", activity: "Outdoor Play", teacher: "Mr. Thompson", location: "Playground" },
      { time: "13:30 - 14:15", activity: "Creative Expression", teacher: "Ms. Davis", location: "Main Classroom" },
      { time: "14:30 - 15:00", activity: "Weekly Reflection & Dismissal", teacher: "Ms. Davis", location: "Main Classroom" }
    ]
  }
];

const ParentSchedule = () => {
  return (
    <DashboardLayout role="parent" title="Child's Schedule">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">Emma Thompson's Weekly Schedule</h2>
            <p className="text-muted-foreground">Pre-K (4 years) - Ms. Davis's Class</p>
          </div>
          <Badge className="bg-promise-500">Spring Term 2025</Badge>
        </div>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Class Schedule</CardTitle>
              <CardDescription>Weekly timetable for Emma</CardDescription>
            </div>
            <Calendar className="h-5 w-5 text-muted-foreground" />
          </CardHeader>
          <CardContent className="p-0">
            <div className="flex flex-col gap-6 p-6">
              {scheduleData.map((day) => (
                <div key={day.day}>
                  <h3 className="font-semibold text-lg mb-3">{day.day}</h3>
                  <div className="rounded-md border overflow-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[120px]">Time</TableHead>
                          <TableHead>Activity</TableHead>
                          <TableHead>Teacher</TableHead>
                          <TableHead className="hidden md:table-cell">Location</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {day.activities.map((activity, idx) => (
                          <TableRow key={`${day.day}-${idx}`}>
                            <TableCell className="flex items-center gap-2 font-medium">
                              <Clock className="h-3 w-3 text-muted-foreground" />
                              {activity.time}
                            </TableCell>
                            <TableCell>{activity.activity}</TableCell>
                            <TableCell>{activity.teacher}</TableCell>
                            <TableCell className="hidden md:table-cell">{activity.location}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Special Activities</CardTitle>
            <CardDescription>Upcoming special events and activities</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-muted/50 p-4 rounded-md border">
                <div className="flex justify-between items-start">
                  <div>
                    <h5 className="font-medium">Local Zoo Visit</h5>
                    <p className="text-sm text-muted-foreground">May 28, 2025 • 9:00 AM - 2:00 PM</p>
                    <p className="text-sm mt-2">Permission slip due by May 21st</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-muted/50 p-4 rounded-md border">
                <div className="flex justify-between items-start">
                  <div>
                    <h5 className="font-medium">Spring Festival Performance</h5>
                    <p className="text-sm text-muted-foreground">May 20, 2025 • 10:00 AM - 12:00 PM</p>
                    <p className="text-sm mt-2">Emma will perform "Twinkle Twinkle" on the xylophone</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default ParentSchedule;
