
import { Book, CheckCircle, Clock, Users } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import StatCard from "../StatCard";
import DashboardLayout from "../DashboardLayout";
import { Progress } from "@/components/ui/progress";

const TeacherDashboard = () => {
  return (
    <DashboardLayout role="teacher" title="Teacher Dashboard">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Students Present"
          value="18/20"
          icon={<CheckCircle className="h-4 w-4 text-muted-foreground" />}
          description="90% attendance today"
          trendValue={2}
          trendDirection="up"
        />
        <StatCard
          title="Class Average"
          value="87%"
          icon={<Book className="h-4 w-4 text-muted-foreground" />}
          description="3% increase from last month"
          trendValue={3}
          trendDirection="up"
        />
        <StatCard
          title="Upcoming Assessments"
          value="3"
          icon={<Clock className="h-4 w-4 text-muted-foreground" />}
          description="Next assessment: Language"
        />
        <StatCard
          title="Parent Meetings"
          value="5"
          icon={<Users className="h-4 w-4 text-muted-foreground" />}
          description="Scheduled for this week"
        />
      </div>

      <div className="grid gap-4 md:grid-cols-7 mt-4">
        <Card className="col-span-7 md:col-span-4">
          <CardHeader>
            <CardTitle>Today's Attendance</CardTitle>
            <CardDescription>Pre-K (4 years) - May 17, 2025</CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Student</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="hidden md:table-cell">Note</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {studentAttendance.map((student) => (
                  <TableRow key={student.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={student.avatar} alt={student.name} />
                          <AvatarFallback>{student.name.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <span>{student.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        student.status === "present"
                          ? "bg-green-100 text-green-800"
                          : student.status === "absent"
                          ? "bg-red-100 text-red-800"
                          : "bg-yellow-100 text-yellow-800"
                      }`}>
                        {student.status === "present" ? "Present" : student.status === "absent" ? "Absent" : "Late"}
                      </div>
                    </TableCell>
                    <TableCell className="hidden md:table-cell">{student.note}</TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm">Update</Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
          <CardFooter className="flex justify-between">
            <div className="text-sm text-muted-foreground">Showing 8 of 20 students</div>
            <Button variant="outline" size="sm">View All</Button>
          </CardFooter>
        </Card>

        <Card className="col-span-7 md:col-span-3">
          <CardHeader>
            <CardTitle>Daily Schedule</CardTitle>
            <CardDescription>Your class timetable for today</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {schedule.map((item, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className="text-right min-w-[60px]">
                  <span className="text-xs font-medium">{item.time}</span>
                </div>
                <div className={`w-1 rounded h-full flex-shrink-0 ${
                  item.current ? "bg-promise-500" : "bg-gray-200"
                }`}></div>
                <div className={`p-2 rounded-md flex-1 ${
                  item.current ? "bg-promise-50 border border-promise-200" : ""
                }`}>
                  <p className="font-medium">{item.activity}</p>
                  <p className="text-xs text-muted-foreground">{item.details}</p>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
      
      <div className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Weekly Progress</CardTitle>
            <CardDescription>Student achievement across key learning areas</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Language & Literacy</span>
                <span className="text-sm font-medium">85%</span>
              </div>
              <Progress value={85} className="h-2" />
              <p className="text-xs text-muted-foreground">Most students mastered letter recognition and basic phonics</p>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Math Concepts</span>
                <span className="text-sm font-medium">78%</span>
              </div>
              <Progress value={78} className="h-2 bg-muted [&>*]:bg-meadow-500" />
              <p className="text-xs text-muted-foreground">Good progress in counting and shape recognition, need work on patterns</p>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Social Development</span>
                <span className="text-sm font-medium">92%</span>
              </div>
              <Progress value={92} className="h-2 bg-muted [&>*]:bg-sunlight-600" />
              <p className="text-xs text-muted-foreground">Excellent cooperation and sharing skills displayed this week</p>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Fine Motor Skills</span>
                <span className="text-sm font-medium">80%</span>
              </div>
              <Progress value={80} className="h-2 bg-muted [&>*]:bg-peach-500" />
              <p className="text-xs text-muted-foreground">Improvement in scissor use and pencil grip</p>
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full">Generate Detailed Report</Button>
          </CardFooter>
        </Card>
      </div>
    </DashboardLayout>
  );
};

// Sample data
const studentAttendance = [
  {
    id: 1,
    name: "Emma Thompson",
    avatar: "https://ui-avatars.com/api/?name=ET&background=random",
    status: "present",
    note: ""
  },
  {
    id: 2,
    name: "Noah Garcia",
    avatar: "https://ui-avatars.com/api/?name=NG&background=random",
    status: "present",
    note: ""
  },
  {
    id: 3,
    name: "Olivia Martinez",
    avatar: "https://ui-avatars.com/api/?name=OM&background=random",
    status: "late",
    note: "Arrived at 9:45 AM"
  },
  {
    id: 4,
    name: "Liam Wilson",
    avatar: "https://ui-avatars.com/api/?name=LW&background=random",
    status: "present",
    note: ""
  },
  {
    id: 5,
    name: "Ava Johnson",
    avatar: "https://ui-avatars.com/api/?name=AJ&background=random",
    status: "present",
    note: ""
  },
  {
    id: 6,
    name: "Ethan Brown",
    avatar: "https://ui-avatars.com/api/?name=EB&background=random",
    status: "absent",
    note: "Parent called sick"
  },
  {
    id: 7,
    name: "Sophia Davis",
    avatar: "https://ui-avatars.com/api/?name=SD&background=random",
    status: "present",
    note: ""
  },
  {
    id: 8,
    name: "Mason Taylor",
    avatar: "https://ui-avatars.com/api/?name=MT&background=random",
    status: "present",
    note: ""
  }
];

const schedule = [
  {
    time: "8:30 AM",
    activity: "Morning Circle",
    details: "Welcome, calendar, weather, songs",
    current: false
  },
  {
    time: "9:15 AM",
    activity: "Language Arts",
    details: "Letter recognition and phonics exercise",
    current: false
  },
  {
    time: "10:00 AM",
    activity: "Snack Time",
    details: "Healthy snacks and socialization",
    current: false
  },
  {
    time: "10:30 AM",
    activity: "Math Concepts",
    details: "Counting and shape patterns",
    current: true
  },
  {
    time: "11:15 AM",
    activity: "Outside Play",
    details: "Structured playground activities",
    current: false
  },
  {
    time: "12:00 PM",
    activity: "Lunch",
    details: "Lunch and quiet reading",
    current: false
  },
  {
    time: "12:45 PM",
    activity: "Rest Time",
    details: "Nap and quiet activities",
    current: false
  },
  {
    time: "2:00 PM",
    activity: "Art Project",
    details: "Spring-themed crafts",
    current: false
  }
];

export default TeacherDashboard;
