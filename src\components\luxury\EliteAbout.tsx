import React, { useRef, useState, useEffect } from 'react';
import { Crown, Star, Award, Users, Heart, Shield, Sparkles, TrendingUp } from 'lucide-react';
import { useLuxuryTheme } from '@/contexts/LuxuryThemeContext';
import { useLuxuryIntersectionObserver } from '@/utils/luxuryAnimations';

interface LuxuryStatistic {
  id: string;
  value: number;
  label: string;
  suffix: string;
  icon: React.ReactNode;
  color: 'gold' | 'platinum' | 'purple' | 'emerald' | 'rose-gold';
}

const EliteAbout: React.FC = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const statsRef = useRef<HTMLDivElement>(null);
  const [animatedStats, setAnimatedStats] = useState<Record<string, number>>({});
  const [hasAnimated, setHasAnimated] = useState(false);
  const { theme } = useLuxuryTheme();

  useLuxuryIntersectionObserver(sectionRef, 'animate-luxury-fade-in');

  const statistics: LuxuryStatistic[] = [
    {
      id: '1',
      value: 98,
      label: 'Elite Family Satisfaction',
      suffix: '%',
      icon: <Crown className="h-8 w-8" />,
      color: 'gold'
    },
    {
      id: '2',
      value: 25,
      label: 'Years of Excellence',
      suffix: '+',
      icon: <Award className="h-8 w-8" />,
      color: 'platinum'
    },
    {
      id: '3',
      value: 500,
      label: 'Elite Graduates',
      suffix: '+',
      icon: <Star className="h-8 w-8" />,
      color: 'purple'
    },
    {
      id: '4',
      value: 15,
      label: 'World-Class Educators',
      suffix: '',
      icon: <Users className="h-8 w-8" />,
      color: 'emerald'
    }
  ];

  // Animated counter effect
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasAnimated) {
            setHasAnimated(true);
            statistics.forEach((stat) => {
              animateCounter(stat.id, stat.value);
            });
          }
        });
      },
      { threshold: 0.5 }
    );

    if (statsRef.current) {
      observer.observe(statsRef.current);
    }

    return () => observer.disconnect();
  }, [hasAnimated]);

  const animateCounter = (id: string, targetValue: number) => {
    const duration = 2000;
    const steps = 60;
    const increment = targetValue / steps;
    let current = 0;
    let step = 0;

    const timer = setInterval(() => {
      current += increment;
      step++;
      
      if (step >= steps) {
        current = targetValue;
        clearInterval(timer);
      }
      
      setAnimatedStats(prev => ({
        ...prev,
        [id]: Math.floor(current)
      }));
    }, duration / steps);
  };

  const getStatColor = (color: string) => {
    const colors = {
      gold: 'text-luxury-gold',
      platinum: 'text-luxury-platinum',
      purple: 'text-luxury-purple',
      emerald: 'text-luxury-emerald',
      'rose-gold': 'text-luxury-rose-gold'
    };
    return colors[color as keyof typeof colors];
  };

  const getStatBg = (color: string) => {
    const colors = {
      gold: 'bg-luxury-gold/20',
      platinum: 'bg-luxury-platinum/20',
      purple: 'bg-luxury-purple/20',
      emerald: 'bg-luxury-emerald/20',
      'rose-gold': 'bg-luxury-rose-gold/20'
    };
    return colors[color as keyof typeof colors];
  };

  return (
    <section 
      ref={sectionRef}
      className="luxury-section bg-white relative overflow-hidden"
    >
      <div className="luxury-container">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-playfair font-bold text-luxury-navy mb-6">
            Elite Heritage & Excellence
          </h2>
          <p className="text-xl text-gray-700 font-montserrat max-w-4xl mx-auto leading-relaxed">
            For over two decades, Promise Academy has been the premier destination for elite families seeking the finest early childhood education.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Brand Story */}
          <div className="space-y-8">
            <div className="space-y-6">
              <h3 className="text-3xl font-playfair font-bold text-luxury-navy">
                A Legacy of Luxury Learning
              </h3>
              
              <p className="text-lg text-gray-700 font-montserrat leading-relaxed">
                Founded by visionary educator Lady Margaret Pemberton, Promise Academy was born from a simple yet profound belief: every child deserves an education as extraordinary as their potential.
              </p>
              
              <p className="text-lg text-gray-700 font-montserrat leading-relaxed">
                Our exclusive approach combines the finest educational methodologies with luxury amenities, creating an environment where young minds flourish in comfort and style. From our hand-selected educators to our bespoke curriculum, every detail reflects our commitment to excellence.
              </p>
            </div>

            {/* Core Values */}
            <div className="space-y-4">
              <h4 className="text-2xl font-playfair font-bold text-luxury-navy">
                Our Elite Values
              </h4>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="flex items-center gap-3 p-4 bg-luxury-gold/10 rounded-xl">
                  <Crown className="h-6 w-6 text-luxury-gold" />
                  <span className="font-montserrat font-semibold text-luxury-navy">Excellence</span>
                </div>
                
                <div className="flex items-center gap-3 p-4 bg-luxury-purple/10 rounded-xl">
                  <Heart className="h-6 w-6 text-luxury-purple" />
                  <span className="font-montserrat font-semibold text-luxury-navy">Compassion</span>
                </div>
                
                <div className="flex items-center gap-3 p-4 bg-luxury-emerald/10 rounded-xl">
                  <Shield className="h-6 w-6 text-luxury-emerald" />
                  <span className="font-montserrat font-semibold text-luxury-navy">Integrity</span>
                </div>
                
                <div className="flex items-center gap-3 p-4 bg-luxury-rose-gold/10 rounded-xl">
                  <TrendingUp className="h-6 w-6 text-luxury-rose-gold" />
                  <span className="font-montserrat font-semibold text-luxury-navy">Innovation</span>
                </div>
              </div>
            </div>
          </div>

          {/* Premium Imagery */}
          <div className="relative">
            {/* Background Effects */}
            <div className="absolute -top-10 -right-10 w-40 h-40 bg-luxury-gold/20 rounded-full blur-3xl" />
            <div className="absolute -bottom-10 -left-10 w-48 h-48 bg-luxury-purple/20 rounded-full blur-3xl" />
            
            <div className="relative z-10 space-y-6">
              {/* Main Image */}
              <div className="luxury-card p-6 rounded-3xl luxury-hover-lift">
                <img 
                  src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b" 
                  alt="Luxury learning environment at Promise Academy" 
                  className="w-full h-80 object-cover rounded-2xl shadow-luxury"
                />
                
                {/* Floating Achievement */}
                <div className="absolute -top-4 -right-4 luxury-card p-4 rounded-2xl animate-luxury-float">
                  <div className="bg-luxury-gold/20 p-3 rounded-xl text-center">
                    <Award className="h-6 w-6 text-luxury-gold mx-auto mb-2" />
                    <span className="font-montserrat font-bold text-luxury-gold text-sm block">
                      Award Winning
                    </span>
                  </div>
                </div>
              </div>

              {/* Secondary Images */}
              <div className="grid grid-cols-2 gap-4">
                <div className="luxury-card p-4 rounded-2xl">
                  <img 
                    src="https://images.unsplash.com/photo-1503454537195-1dcabb73ffb9" 
                    alt="Elite classroom" 
                    className="w-full h-32 object-cover rounded-xl"
                  />
                </div>
                <div className="luxury-card p-4 rounded-2xl">
                  <img 
                    src="https://images.unsplash.com/photo-1544717297-fa95b6ee9643" 
                    alt="Luxury playground" 
                    className="w-full h-32 object-cover rounded-xl"
                  />
                </div>
              </div>
            </div>

            {/* Sparkle Effects */}
            {theme.effects.animations && (
              <>
                <Sparkles className="absolute top-4 left-4 h-4 w-4 text-luxury-gold animate-luxury-sparkle" />
                <Sparkles className="absolute bottom-4 right-4 h-4 w-4 text-luxury-gold animate-luxury-sparkle" style={{ animationDelay: '1s' }} />
              </>
            )}
          </div>
        </div>

        {/* Luxury Statistics */}
        <div 
          ref={statsRef}
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-16"
        >
          {statistics.map((stat, index) => (
            <div 
              key={stat.id}
              className="text-center luxury-card p-8 rounded-3xl luxury-hover-lift group"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className={`inline-flex items-center justify-center p-4 rounded-2xl mb-6 ${getStatBg(stat.color)} group-hover:scale-110 transition-transform duration-300`}>
                <div className={getStatColor(stat.color)}>
                  {stat.icon}
                </div>
              </div>
              
              <div className="space-y-2">
                <div className={`text-4xl font-playfair font-bold ${getStatColor(stat.color)}`}>
                  {animatedStats[stat.id] || 0}{stat.suffix}
                </div>
                <p className="font-montserrat font-semibold text-luxury-navy">
                  {stat.label}
                </p>
              </div>

              {/* Sparkle Effect */}
              {theme.effects.animations && (
                <Sparkles className="absolute top-2 right-2 h-3 w-3 text-luxury-gold/50 animate-luxury-sparkle" style={{ animationDelay: `${index * 0.5}s` }} />
              )}
            </div>
          ))}
        </div>

        {/* Elite Team Preview */}
        <div className="text-center">
          <h3 className="text-3xl font-playfair font-bold text-luxury-navy mb-8">
            Meet Our Elite Leadership
          </h3>
          
          <div className="flex justify-center items-center gap-8 flex-wrap">
            <div className="text-center group">
              <div className="relative mb-4">
                <img 
                  src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d" 
                  alt="Dr. James Wellington, Head of Academy" 
                  className="w-24 h-24 rounded-full object-cover border-4 border-luxury-gold shadow-luxury-gold group-hover:scale-110 transition-transform duration-300"
                />
                <Crown className="absolute -top-2 -right-2 h-6 w-6 text-luxury-gold" />
              </div>
              <h4 className="font-playfair font-bold text-luxury-navy">Dr. James Wellington</h4>
              <p className="text-luxury-gold font-montserrat text-sm">Head of Academy</p>
            </div>
            
            <div className="text-center group">
              <div className="relative mb-4">
                <img 
                  src="https://images.unsplash.com/photo-1494790108755-2616b612b786" 
                  alt="Lady Margaret Pemberton, Founder" 
                  className="w-24 h-24 rounded-full object-cover border-4 border-luxury-purple shadow-luxury-purple group-hover:scale-110 transition-transform duration-300"
                />
                <Star className="absolute -top-2 -right-2 h-6 w-6 text-luxury-purple" />
              </div>
              <h4 className="font-playfair font-bold text-luxury-navy">Lady Margaret Pemberton</h4>
              <p className="text-luxury-purple font-montserrat text-sm">Founder & Visionary</p>
            </div>
          </div>
        </div>
      </div>

      {/* Background Decorative Elements */}
      {theme.effects.animations && (
        <>
          <Crown className="absolute top-20 right-20 h-10 w-10 text-luxury-gold/5 animate-luxury-float" />
          <Star className="absolute bottom-20 left-20 h-8 w-8 text-luxury-purple/5 animate-luxury-float" style={{ animationDelay: '2s' }} />
        </>
      )}
    </section>
  );
};

export default EliteAbout;
