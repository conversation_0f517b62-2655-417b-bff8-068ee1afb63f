import React, { useRef } from 'react';
import { Crown, Star, Gem, Shield, Award, Sparkles, Heart, BookOpen } from 'lucide-react';
import PremiumBadge from './PremiumBadge';
import { useLuxuryTheme } from '@/contexts/LuxuryThemeContext';
import { useLuxuryIntersectionObserver, useLuxuryHover } from '@/utils/luxuryAnimations';

interface LuxuryFeature {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  premium: boolean;
  color: 'gold' | 'platinum' | 'purple' | 'emerald' | 'rose-gold';
  badge?: 'exclusive' | 'limited' | 'vip-only' | 'elite-access' | 'premium' | 'featured';
}

interface ExclusiveFeaturesProps {
  layout?: 'grid' | 'carousel';
  showBadges?: boolean;
}

const ExclusiveFeatures: React.FC<ExclusiveFeaturesProps> = ({
  layout = 'grid',
  showBadges = true
}) => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const { theme, isVIP, isElite } = useLuxuryTheme();

  useLuxuryIntersectionObserver(sectionRef, 'animate-luxury-fade-in');

  const features: LuxuryFeature[] = [
    {
      id: '1',
      title: 'Elite Curriculum',
      description: 'Bespoke educational programs designed exclusively for gifted children, featuring advanced cognitive development and creative excellence.',
      icon: <Crown className="h-8 w-8" />,
      premium: true,
      color: 'gold',
      badge: 'exclusive'
    },
    {
      id: '2',
      title: 'VIP Educators',
      description: 'World-class teachers with advanced degrees and specialized training in luxury early childhood education.',
      icon: <Star className="h-8 w-8" />,
      premium: true,
      color: 'platinum',
      badge: 'vip-only'
    },
    {
      id: '3',
      title: 'Premium Environment',
      description: 'State-of-the-art facilities with luxury amenities, ensuring the highest standards of safety and comfort.',
      icon: <Gem className="h-8 w-8" />,
      premium: true,
      color: 'emerald',
      badge: 'premium'
    },
    {
      id: '4',
      title: 'Elite Spaces',
      description: 'Architecturally designed learning environments that inspire creativity and foster intellectual growth.',
      icon: <Shield className="h-8 w-8" />,
      premium: false,
      color: 'purple',
      badge: 'featured'
    },
    {
      id: '5',
      title: 'Luxury Care',
      description: 'Personalized attention with low student-to-teacher ratios, ensuring each child receives premium care.',
      icon: <Heart className="h-8 w-8" />,
      premium: true,
      color: 'rose-gold',
      badge: 'elite-access'
    },
    {
      id: '6',
      title: 'Advanced Learning',
      description: 'Cutting-edge educational technology and innovative teaching methods for accelerated development.',
      icon: <BookOpen className="h-8 w-8" />,
      premium: false,
      color: 'gold',
      badge: 'limited'
    }
  ];

  const getCardClass = (feature: LuxuryFeature) => {
    const baseClass = 'bg-gradient-to-br from-luxury-purple/20 to-luxury-purple/10 backdrop-blur-sm border border-luxury-gold/30 p-8 rounded-3xl transition-all duration-500 group relative overflow-hidden hover:shadow-luxury-gold hover:border-luxury-gold/60 hover:scale-105';

    return baseClass;
  };

  const getIconClass = (feature: LuxuryFeature) => {
    return 'inline-flex items-center justify-center p-4 rounded-2xl mb-6 bg-gradient-to-br from-luxury-gold to-luxury-red text-white shadow-luxury-gold group-hover:scale-110 group-hover:shadow-luxury-red transition-all duration-500';
  };

  const FeatureCard: React.FC<{ feature: LuxuryFeature; index: number }> = ({ feature, index }) => {
    const cardRef = useRef<HTMLDivElement>(null);

    return (
      <div
        ref={cardRef}
        className={getCardClass(feature)}
        style={{ animationDelay: `${index * 0.1}s` }}
      >
        {/* Purple Background with Gold Highlights */}
        <div className="absolute inset-0 bg-gradient-to-br from-luxury-purple/10 to-luxury-purple/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

        {/* Premium Badge */}
        {showBadges && feature.badge && (
          <div className="absolute top-4 right-4">
            <PremiumBadge type={feature.badge} size="sm" animated={false} />
          </div>
        )}

        {/* Gold Highlight Border */}
        <div className="absolute inset-0 rounded-3xl border border-luxury-gold/20 group-hover:border-luxury-gold/60 transition-all duration-500" />

        {/* Icon with Gold-Red Gradient */}
        <div className={getIconClass(feature)}>
          {feature.icon}
        </div>

        {/* Content with Cormorant Garamond Headings */}
        <div className="relative z-10">
          <h3 className="text-3xl font-cormorant font-semibold text-white mb-4 group-hover:text-luxury-gold transition-colors duration-500">
            {feature.title}
          </h3>
          <p className="text-white/90 font-montserrat leading-relaxed group-hover:text-white transition-colors duration-500">
            {feature.description}
          </p>
        </div>

        {/* Red Accent for Premium Features */}
        {feature.premium && (
          <div className="absolute bottom-4 left-4 px-3 py-1 bg-luxury-red/20 border border-luxury-red/40 rounded-full text-luxury-red text-xs font-montserrat font-semibold">
            VIP EXCLUSIVE
          </div>
        )}

        {/* Subtle Glow Effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-luxury-gold/5 to-luxury-red/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl" />
      </div>
    );
  };

  return (
    <section
      ref={sectionRef}
      className="luxury-section relative py-24"
      style={{
        background: 'linear-gradient(135deg, #663399 0%, #4B0082 50%, #2D1B69 100%)'
      }}
    >
      <div className="luxury-container">
        {/* Section Header with Cormorant Garamond */}
        <div className="text-center mb-20">
          <h2 className="text-5xl md:text-6xl font-cormorant font-semibold mb-8">
            <span className="bg-gradient-to-r from-luxury-gold via-white to-luxury-red bg-clip-text text-transparent">
              Exclusive Elite Features
            </span>
          </h2>
          <p className="text-2xl text-white/95 font-montserrat font-light max-w-4xl mx-auto leading-relaxed">
            Discover the premium advantages that set Promise Academy apart as the ultimate destination for elite early childhood education.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <FeatureCard key={feature.id} feature={feature} index={index} />
          ))}
        </div>

        {/* Luxury CTA with Red Accents */}
        <div className="text-center mt-20">
          <div className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-luxury-gold via-luxury-purple to-luxury-red rounded-full text-white font-montserrat font-semibold text-lg shadow-luxury-red hover:scale-105 transition-all duration-500 cursor-pointer">
            <Crown className="h-6 w-6" />
            <span>Experience Elite Education Today</span>
          </div>
        </div>
      </div>

      {/* Subtle Background Elements */}
      <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-luxury-gold/5 rounded-full blur-xl" />
      <div className="absolute bottom-1/3 right-1/4 w-48 h-48 bg-luxury-red/5 rounded-full blur-2xl" />
      <Crown className="absolute top-16 right-16 h-8 w-8 text-luxury-gold/20" />
      <Star className="absolute bottom-16 left-16 h-6 w-6 text-luxury-red/20" />
    </section>
  );
};

export default ExclusiveFeatures;
