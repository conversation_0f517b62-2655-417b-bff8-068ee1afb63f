import React, { useEffect, useState, useRef } from 'react';
import { useLuxuryTheme } from '@/contexts/LuxuryThemeContext';

// Accessibility preferences hook
export const useLuxuryAccessibility = () => {
  const [preferences, setPreferences] = useState({
    highContrast: false,
    reducedMotion: false,
    largeText: false,
    screenReader: false
  });

  useEffect(() => {
    // Check system preferences
    const highContrast = window.matchMedia('(prefers-contrast: high)').matches;
    const reducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    setPreferences(prev => ({
      ...prev,
      highContrast,
      reducedMotion
    }));

    // Listen for changes
    const contrastQuery = window.matchMedia('(prefers-contrast: high)');
    const motionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');

    const handleContrastChange = (e: MediaQueryListEvent) => {
      setPreferences(prev => ({ ...prev, highContrast: e.matches }));
    };

    const handleMotionChange = (e: MediaQueryListEvent) => {
      setPreferences(prev => ({ ...prev, reducedMotion: e.matches }));
    };

    contrastQuery.addEventListener('change', handleContrastChange);
    motionQuery.addEventListener('change', handleMotionChange);

    return () => {
      contrastQuery.removeEventListener('change', handleContrastChange);
      motionQuery.removeEventListener('change', handleMotionChange);
    };
  }, []);

  const togglePreference = (key: keyof typeof preferences) => {
    setPreferences(prev => ({ ...prev, [key]: !prev[key] }));
  };

  return { preferences, togglePreference };
};

// High contrast luxury theme
export const getHighContrastLuxuryColors = () => ({
  primary: '#FFFF00', // Bright yellow instead of gold
  secondary: '#FFFFFF', // Pure white instead of platinum
  accent: '#00FF00', // Bright green instead of emerald
  background: '#000000', // Pure black
  surface: '#1A1A1A', // Dark gray
  text: '#FFFFFF', // Pure white text
  border: '#FFFFFF', // White borders
  focus: '#FFFF00', // Bright yellow focus
});

// Accessible luxury button
interface AccessibleLuxuryButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  ariaLabel?: string;
  ariaDescribedBy?: string;
  className?: string;
}

export const AccessibleLuxuryButton: React.FC<AccessibleLuxuryButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  ariaLabel,
  ariaDescribedBy,
  className = ''
}) => {
  const { preferences } = useLuxuryAccessibility();
  const buttonRef = useRef<HTMLButtonElement>(null);

  const getAccessibleClasses = () => {
    const baseClasses = 'relative font-semibold transition-all duration-200 focus:outline-none focus:ring-4';
    
    if (preferences.highContrast) {
      return `${baseClasses} bg-yellow-400 text-black border-2 border-white focus:ring-yellow-300 hover:bg-yellow-300`;
    }
    
    const variantClasses = {
      primary: 'bg-luxury-gold text-luxury-navy focus:ring-luxury-gold/50 hover:bg-luxury-gold/90',
      secondary: 'bg-luxury-platinum text-luxury-navy focus:ring-luxury-platinum/50 hover:bg-luxury-platinum/90'
    };
    
    const sizeClasses = {
      sm: 'px-4 py-2 text-sm rounded-lg',
      md: 'px-6 py-3 text-base rounded-xl',
      lg: 'px-8 py-4 text-lg rounded-2xl'
    };
    
    return `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]}`;
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onClick?.();
    }
  };

  return (
    <button
      ref={buttonRef}
      onClick={onClick}
      onKeyDown={handleKeyDown}
      disabled={disabled}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
      className={`${getAccessibleClasses()} ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'} ${className}`}
      role="button"
      tabIndex={disabled ? -1 : 0}
    >
      {children}
    </button>
  );
};

// Screen reader announcements
export const useLuxuryScreenReader = () => {
  const announce = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', priority);
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  };

  return { announce };
};

// Accessible luxury navigation
interface AccessibleLuxuryNavProps {
  items: Array<{
    label: string;
    href?: string;
    onClick?: () => void;
    current?: boolean;
  }>;
  ariaLabel?: string;
}

export const AccessibleLuxuryNav: React.FC<AccessibleLuxuryNavProps> = ({
  items,
  ariaLabel = 'Main navigation'
}) => {
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const navRef = useRef<HTMLElement>(null);
  const { preferences } = useLuxuryAccessibility();

  const handleKeyDown = (e: React.KeyboardEvent, index: number) => {
    switch (e.key) {
      case 'ArrowRight':
      case 'ArrowDown':
        e.preventDefault();
        setFocusedIndex((prev) => (prev + 1) % items.length);
        break;
      case 'ArrowLeft':
      case 'ArrowUp':
        e.preventDefault();
        setFocusedIndex((prev) => (prev - 1 + items.length) % items.length);
        break;
      case 'Home':
        e.preventDefault();
        setFocusedIndex(0);
        break;
      case 'End':
        e.preventDefault();
        setFocusedIndex(items.length - 1);
        break;
    }
  };

  useEffect(() => {
    if (focusedIndex >= 0 && navRef.current) {
      const focusedElement = navRef.current.children[focusedIndex] as HTMLElement;
      focusedElement?.focus();
    }
  }, [focusedIndex]);

  const getNavItemClasses = (current: boolean) => {
    const baseClasses = 'block px-4 py-2 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2';
    
    if (preferences.highContrast) {
      return `${baseClasses} ${current ? 'bg-yellow-400 text-black' : 'text-white hover:bg-white hover:text-black'} focus:ring-yellow-300`;
    }
    
    return `${baseClasses} ${current ? 'bg-luxury-gold text-luxury-navy' : 'text-white hover:text-luxury-gold'} focus:ring-luxury-gold/50`;
  };

  return (
    <nav ref={navRef} aria-label={ariaLabel} role="navigation">
      <ul className="space-y-2">
        {items.map((item, index) => (
          <li key={index}>
            {item.href ? (
              <a
                href={item.href}
                className={getNavItemClasses(item.current || false)}
                onKeyDown={(e) => handleKeyDown(e, index)}
                aria-current={item.current ? 'page' : undefined}
                tabIndex={0}
              >
                {item.label}
              </a>
            ) : (
              <button
                onClick={item.onClick}
                className={getNavItemClasses(item.current || false)}
                onKeyDown={(e) => handleKeyDown(e, index)}
                aria-current={item.current ? 'page' : undefined}
                tabIndex={0}
              >
                {item.label}
              </button>
            )}
          </li>
        ))}
      </ul>
    </nav>
  );
};

// Accessible luxury form field
interface AccessibleLuxuryFormFieldProps {
  id: string;
  label: string;
  type?: 'text' | 'email' | 'tel' | 'password';
  value: string;
  onChange: (value: string) => void;
  error?: string;
  required?: boolean;
  placeholder?: string;
  helpText?: string;
}

export const AccessibleLuxuryFormField: React.FC<AccessibleLuxuryFormFieldProps> = ({
  id,
  label,
  type = 'text',
  value,
  onChange,
  error,
  required = false,
  placeholder,
  helpText
}) => {
  const { preferences } = useLuxuryAccessibility();
  const { announce } = useLuxuryScreenReader();
  const inputRef = useRef<HTMLInputElement>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  };

  const handleBlur = () => {
    if (error) {
      announce(`Error in ${label}: ${error}`, 'assertive');
    }
  };

  const getInputClasses = () => {
    const baseClasses = 'w-full px-4 py-3 rounded-xl border-2 transition-all duration-200 focus:outline-none focus:ring-4';
    
    if (preferences.highContrast) {
      return `${baseClasses} bg-black text-white border-white focus:ring-yellow-300 ${error ? 'border-red-500' : ''}`;
    }
    
    return `${baseClasses} bg-white/10 text-white border-luxury-gold/30 focus:border-luxury-gold focus:ring-luxury-gold/20 ${error ? 'border-red-400' : ''}`;
  };

  const errorId = error ? `${id}-error` : undefined;
  const helpId = helpText ? `${id}-help` : undefined;
  const describedBy = [errorId, helpId].filter(Boolean).join(' ') || undefined;

  return (
    <div className="space-y-2">
      <label 
        htmlFor={id}
        className={`block font-semibold ${preferences.highContrast ? 'text-white' : 'text-white'} ${preferences.largeText ? 'text-lg' : 'text-base'}`}
      >
        {label}
        {required && (
          <span className="text-red-400 ml-1" aria-label="required">*</span>
        )}
      </label>
      
      <input
        ref={inputRef}
        id={id}
        type={type}
        value={value}
        onChange={handleChange}
        onBlur={handleBlur}
        placeholder={placeholder}
        required={required}
        aria-invalid={!!error}
        aria-describedby={describedBy}
        className={getInputClasses()}
      />
      
      {helpText && (
        <p id={helpId} className="text-sm text-white/70">
          {helpText}
        </p>
      )}
      
      {error && (
        <p id={errorId} className="text-sm text-red-400" role="alert">
          {error}
        </p>
      )}
    </div>
  );
};

// Skip to content link
export const LuxurySkipLink: React.FC = () => {
  const { preferences } = useLuxuryAccessibility();

  const handleSkip = () => {
    const mainContent = document.getElementById('main-content');
    if (mainContent) {
      mainContent.focus();
      mainContent.scrollIntoView();
    }
  };

  const getSkipLinkClasses = () => {
    const baseClasses = 'absolute top-4 left-4 px-4 py-2 rounded-lg font-semibold transition-all duration-200 transform -translate-y-full focus:translate-y-0 z-50';
    
    if (preferences.highContrast) {
      return `${baseClasses} bg-yellow-400 text-black focus:ring-4 focus:ring-yellow-300`;
    }
    
    return `${baseClasses} bg-luxury-gold text-luxury-navy focus:ring-4 focus:ring-luxury-gold/50`;
  };

  return (
    <button
      onClick={handleSkip}
      className={getSkipLinkClasses()}
      onFocus={(e) => e.target.classList.add('translate-y-0')}
      onBlur={(e) => e.target.classList.remove('translate-y-0')}
    >
      Skip to main content
    </button>
  );
};
