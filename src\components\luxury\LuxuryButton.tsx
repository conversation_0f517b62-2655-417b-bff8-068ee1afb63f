import React, { useRef } from 'react';
import { Button } from '@/components/ui/button';
import { useLuxuryHover, playLuxurySound } from '@/utils/luxuryAnimations';
import { useLuxuryTheme } from '@/contexts/LuxuryThemeContext';

interface LuxuryButtonProps {
  children: React.ReactNode;
  variant?: 'gold' | 'platinum' | 'purple' | 'emerald' | 'rose-gold' | 'rainbow';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  glow?: boolean;
  sparkles?: boolean;
  soundEffect?: boolean;
  className?: string;
  onClick?: () => void;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
}

const LuxuryButton: React.FC<LuxuryButtonProps> = ({
  children,
  variant = 'gold',
  size = 'md',
  glow = true,
  sparkles = true,
  soundEffect = false,
  className = '',
  onClick,
  disabled = false,
  type = 'button'
}) => {
  const buttonRef = useRef<HTMLButtonElement>(null);
  const { theme } = useLuxuryTheme();
  
  // Apply luxury hover effects
  useLuxuryHover(buttonRef);

  const getVariantClass = () => {
    const baseClass = 'relative overflow-hidden transition-all duration-500 ease-out font-bold border-0';
    
    switch (variant) {
      case 'gold':
        return `${baseClass} bg-luxury-gold text-luxury-navy shadow-luxury-gold hover:shadow-glow-gold`;
      case 'platinum':
        return `${baseClass} bg-luxury-platinum text-luxury-navy shadow-luxury hover:shadow-glow-gold`;
      case 'purple':
        return `${baseClass} bg-luxury-purple text-white shadow-luxury-purple hover:shadow-glow-purple`;
      case 'emerald':
        return `${baseClass} bg-luxury-emerald text-white shadow-luxury-emerald hover:shadow-glow-gold`;
      case 'rose-gold':
        return `${baseClass} bg-luxury-rose-gold text-white shadow-luxury-rose hover:shadow-glow-gold`;
      case 'rainbow':
        return `${baseClass} bg-luxury-rainbow text-white shadow-luxury hover:shadow-glow-gold`;
      default:
        return `${baseClass} bg-luxury-gold text-luxury-navy shadow-luxury-gold hover:shadow-glow-gold`;
    }
  };

  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'px-4 py-2 text-sm';
      case 'lg':
        return 'px-8 py-4 text-lg';
      case 'xl':
        return 'px-10 py-5 text-xl';
      default:
        return 'px-6 py-3 text-base';
    }
  };

  const handleClick = () => {
    if (soundEffect && theme.effects.sounds) {
      playLuxurySound('click');
    }
    onClick?.();
  };

  const handleMouseEnter = () => {
    if (soundEffect && theme.effects.sounds) {
      playLuxurySound('hover');
    }
  };

  return (
    <Button
      ref={buttonRef}
      type={type}
      disabled={disabled}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      className={`
        ${getVariantClass()}
        ${getSizeClass()}
        ${glow ? 'animate-luxury-glow' : ''}
        ${sparkles ? 'luxury-hover-lift' : ''}
        ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        ${className}
      `}
    >
      {/* Shimmer effect overlay */}
      <div className="absolute inset-0 bg-shimmer opacity-0 hover:opacity-100 transition-opacity duration-300" />
      
      {/* Button content */}
      <span className="relative z-10 flex items-center gap-2">
        {children}
      </span>
      
      {/* Sparkle effects */}
      {sparkles && theme.effects.animations && (
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-1 right-1 w-1 h-1 bg-white rounded-full animate-luxury-sparkle opacity-70" />
          <div className="absolute bottom-1 left-1 w-1 h-1 bg-white rounded-full animate-luxury-sparkle opacity-70" style={{ animationDelay: '0.5s' }} />
        </div>
      )}
    </Button>
  );
};

export default LuxuryButton;
