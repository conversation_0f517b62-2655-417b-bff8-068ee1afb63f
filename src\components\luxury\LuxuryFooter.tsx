import React, { useRef, useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Crown, Star, Mail, Phone, MapPin, Facebook, Instagram, Twitter, Linkedin, Youtube, Sparkles, Send, Shield, Award } from 'lucide-react';
import { Input } from '@/components/ui/input';
import LuxuryButton from './LuxuryButton';
import VIPIndicator from './VIPIndicator';
import { useLuxuryTheme } from '@/contexts/LuxuryThemeContext';
import { useLuxuryIntersectionObserver } from '@/utils/luxuryAnimations';

const LuxuryFooter: React.FC = () => {
  const footerRef = useRef<HTMLDivElement>(null);
  const [newsletterEmail, setNewsletterEmail] = useState('');
  const [isSubscribing, setIsSubscribing] = useState(false);
  const { theme, isVIP } = useLuxuryTheme();

  useLuxuryIntersectionObserver(footerRef, 'animate-luxury-fade-in');

  const handleNewsletterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubscribing(true);
    
    // Simulate subscription
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    setIsSubscribing(false);
    setNewsletterEmail('');
    // Handle success state here
  };

  const socialLinks = [
    { icon: <Facebook className="h-5 w-5" />, href: '#', label: 'Facebook', color: 'hover:text-blue-400' },
    { icon: <Instagram className="h-5 w-5" />, href: '#', label: 'Instagram', color: 'hover:text-pink-400' },
    { icon: <Twitter className="h-5 w-5" />, href: '#', label: 'Twitter', color: 'hover:text-blue-300' },
    { icon: <Linkedin className="h-5 w-5" />, href: '#', label: 'LinkedIn', color: 'hover:text-blue-600' },
    { icon: <Youtube className="h-5 w-5" />, href: '#', label: 'YouTube', color: 'hover:text-red-500' }
  ];

  const quickLinks = [
    { label: 'About Us', href: '#about' },
    { label: 'Programs', href: '#programs' },
    { label: 'Admissions', href: '#contact' },
    { label: 'VIP Portal', href: '/login' },
    { label: 'Campus Tour', href: '#contact' },
    { label: 'Elite Blog', href: '#' }
  ];

  const legalLinks = [
    { label: 'Privacy Policy', href: '#' },
    { label: 'Terms of Service', href: '#' },
    { label: 'Cookie Policy', href: '#' },
    { label: 'Accessibility', href: '#' }
  ];

  return (
    <footer 
      ref={footerRef}
      className="relative overflow-hidden"
      style={{
        background: 'linear-gradient(135deg, #0F0F23 0%, #1B1B3A 50%, #2D1B69 100%)'
      }}
    >
      {/* Main Footer Content */}
      <div className="luxury-container py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
          {/* Brand Section */}
          <div className="lg:col-span-2 space-y-6">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-3 group">
              <div className="relative h-12 w-12 bg-luxury-gold rounded-xl flex items-center justify-center shadow-luxury-gold animate-luxury-glow group-hover:scale-110 transition-transform duration-300">
                <Crown className="h-6 w-6 text-luxury-navy animate-luxury-float" />
                
                {theme.effects.animations && (
                  <Sparkles className="absolute -top-1 -right-1 h-3 w-3 text-luxury-gold animate-luxury-sparkle" />
                )}
              </div>
              
              <div className="flex flex-col">
                <span className="text-2xl font-playfair font-bold luxury-heading">
                  Promise Academy
                </span>
                <span className="text-sm font-montserrat text-luxury-gold/80 tracking-wider">
                  ELITE EDUCATION
                </span>
              </div>
            </Link>

            {/* Description */}
            <p className="text-white/80 font-montserrat leading-relaxed max-w-md">
              For over two decades, Promise Academy has been the premier destination for elite families seeking the finest early childhood education, combining luxury amenities with world-class instruction.
            </p>

            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center gap-3 text-white/70">
                <Phone className="h-4 w-4 text-luxury-gold" />
                <span className="font-montserrat">+1 (555) 123-ELITE</span>
              </div>
              <div className="flex items-center gap-3 text-white/70">
                <Mail className="h-4 w-4 text-luxury-gold" />
                <span className="font-montserrat"><EMAIL></span>
              </div>
              <div className="flex items-center gap-3 text-white/70">
                <MapPin className="h-4 w-4 text-luxury-gold" />
                <span className="font-montserrat">123 Luxury Lane, Beverly Hills, CA</span>
              </div>
            </div>

            {/* Awards */}
            <div className="flex items-center gap-4 pt-4">
              <div className="flex items-center gap-2 px-3 py-2 bg-luxury-gold/20 rounded-full">
                <Award className="h-4 w-4 text-luxury-gold" />
                <span className="text-luxury-gold font-montserrat text-sm font-semibold">Award Winning</span>
              </div>
              <div className="flex items-center gap-2 px-3 py-2 bg-luxury-emerald/20 rounded-full">
                <Shield className="h-4 w-4 text-luxury-emerald" />
                <span className="text-luxury-emerald font-montserrat text-sm font-semibold">Accredited</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-6">
            <h3 className="text-xl font-playfair font-bold text-white">
              Quick Links
            </h3>
            <ul className="space-y-3">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <Link 
                    to={link.href}
                    className="text-white/70 hover:text-luxury-gold transition-colors duration-300 font-montserrat luxury-hover-glow"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* VIP Newsletter */}
          <div className="space-y-6">
            <div className="flex items-center gap-2">
              <h3 className="text-xl font-playfair font-bold text-white">
                VIP Newsletter
              </h3>
              {isVIP && <VIPIndicator size="sm" animated={theme.effects.animations} />}
            </div>
            
            <p className="text-white/70 font-montserrat text-sm">
              Receive exclusive updates, elite events, and premium educational insights.
            </p>

            <form onSubmit={handleNewsletterSubmit} className="space-y-4">
              <Input
                type="email"
                value={newsletterEmail}
                onChange={(e) => setNewsletterEmail(e.target.value)}
                placeholder="Enter your email"
                className="bg-white/10 border-luxury-gold/30 text-white placeholder:text-white/50 focus:border-luxury-gold"
                required
              />
              
              <LuxuryButton
                type="submit"
                variant="gold"
                size="sm"
                glow={true}
                sparkles={true}
                soundEffect={theme.effects.sounds}
                disabled={isSubscribing}
                className="w-full justify-center"
              >
                {isSubscribing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-luxury-navy" />
                    Subscribing...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4" />
                    Subscribe
                  </>
                )}
              </LuxuryButton>
            </form>

            {/* Social Media */}
            <div className="space-y-4">
              <h4 className="font-montserrat font-semibold text-white">Follow Our Elite Community</h4>
              <div className="flex gap-3">
                {socialLinks.map((social, index) => (
                  <a
                    key={index}
                    href={social.href}
                    aria-label={social.label}
                    className={`p-3 bg-white/10 rounded-xl text-white/70 ${social.color} transition-all duration-300 luxury-hover-glow hover:scale-110`}
                  >
                    {social.icon}
                  </a>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-luxury-gold/20 bg-black/20">
        <div className="luxury-container py-6">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            {/* Copyright */}
            <div className="text-white/60 font-montserrat text-sm">
              © 2024 Promise Academy. All rights reserved. Elite education since 1999.
            </div>

            {/* Legal Links */}
            <div className="flex gap-6">
              {legalLinks.map((link, index) => (
                <Link
                  key={index}
                  to={link.href}
                  className="text-white/60 hover:text-luxury-gold transition-colors duration-300 font-montserrat text-sm"
                >
                  {link.label}
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Background Decorative Elements */}
      {theme.effects.animations && (
        <>
          <Crown className="absolute top-10 right-10 h-8 w-8 text-luxury-gold/5 animate-luxury-float" />
          <Star className="absolute bottom-20 left-10 h-6 w-6 text-luxury-platinum/5 animate-luxury-float" style={{ animationDelay: '2s' }} />
          <Sparkles className="absolute top-1/2 right-1/4 h-4 w-4 text-luxury-emerald/5 animate-luxury-sparkle" style={{ animationDelay: '1s' }} />
        </>
      )}

      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent pointer-events-none" />
    </footer>
  );
};

export default LuxuryFooter;
