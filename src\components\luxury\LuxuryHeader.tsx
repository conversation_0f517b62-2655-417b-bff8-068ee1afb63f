import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { <PERSON>u, Crown } from 'lucide-react';
import LuxuryButton from './LuxuryButton';
import VIPIndicator from './VIPIndicator';
import { useLuxuryTheme } from '@/contexts/LuxuryThemeContext';
import { useGSAPEntrance, useGSAPSmoothScroll } from '@/utils/gsapLuxuryAnimations';

interface LuxuryHeaderProps {
  onAboutClick?: () => void;
  onProgramsClick?: () => void;
  onContactClick?: () => void;
  showVIPIndicator?: boolean;
}

const LuxuryHeader: React.FC<LuxuryHeaderProps> = ({
  onAboutClick,
  onProgramsClick,
  onContactClick,
  showVIPIndicator = true
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const headerRef = useRef<HTMLElement>(null);
  const logoRef = useRef<HTMLDivElement>(null);
  const { theme, isVIP } = useLuxuryTheme();
  const { scrollToSection } = useGSAPSmoothScroll();

  // Add elegant entrance animation
  useGSAPEntrance(headerRef, { from: 'top', duration: 1.0 });

  // Handle scroll effects
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const isHomePage = window.location.pathname === "/";

  const headerClass = `
    fixed top-0 left-0 right-0 z-50 transition-all duration-700 ease-out
    ${isScrolled
      ? 'bg-gradient-to-r from-luxury-gold/10 via-luxury-purple/10 to-luxury-red/10 backdrop-blur-md border-b border-luxury-gold/30 shadow-luxury-gold'
      : 'bg-transparent'
    }
  `;

  return (
    <header ref={headerRef} className={headerClass}>
      <div className="luxury-container">
        <div className="flex justify-between items-center py-6">
          {/* Minimalist Luxury Logo */}
          <Link to="/" className="flex items-center space-x-4 group">
            <div
              ref={logoRef}
              className="relative h-14 w-14 bg-gradient-to-br from-luxury-gold via-luxury-purple to-luxury-red rounded-2xl flex items-center justify-center shadow-luxury-gold group-hover:shadow-luxury-red transition-all duration-500"
            >
              <Crown className="h-7 w-7 text-white" />
            </div>

            <div className="flex flex-col">
              <span className="text-2xl font-cormorant font-semibold bg-gradient-to-r from-luxury-gold via-luxury-purple to-luxury-red bg-clip-text text-transparent">
                Promise Academy
              </span>
              <span className="text-sm font-montserrat text-luxury-gold/90 tracking-widest">
                ELITE EDUCATION
              </span>
            </div>
          </Link>

          {/* Elegant Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-10">
            {isHomePage ? (
              <>
                <button
                  onClick={onAboutClick}
                  className="font-montserrat font-medium text-white/95 hover:text-luxury-purple transition-all duration-500 relative group"
                >
                  About
                  <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-luxury-gold to-luxury-purple transition-all duration-500 group-hover:w-full"></span>
                </button>
                <button
                  onClick={onProgramsClick}
                  className="font-montserrat font-medium text-white/95 hover:text-luxury-purple transition-all duration-500 relative group"
                >
                  Programs
                  <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-luxury-gold to-luxury-purple transition-all duration-500 group-hover:w-full"></span>
                </button>
                <button
                  onClick={onContactClick}
                  className="font-montserrat font-medium text-white/95 hover:text-luxury-purple transition-all duration-500 relative group"
                >
                  Contact
                  <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-luxury-gold to-luxury-purple transition-all duration-500 group-hover:w-full"></span>
                </button>
              </>
            ) : (
              <Link
                to="/"
                className="font-montserrat font-medium text-white/95 hover:text-luxury-purple transition-all duration-500 relative group"
              >
                Home
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-luxury-gold to-luxury-purple transition-all duration-500 group-hover:w-full"></span>
              </Link>
            )}

            {/* VIP Indicator */}
            {showVIPIndicator && isVIP && (
              <VIPIndicator size="sm" animated={false} />
            )}

            {/* Luxury Login Button */}
            <Link to="/login" className="group">
              <div className="px-6 py-2 bg-gradient-to-r from-luxury-gold via-luxury-purple to-luxury-red rounded-full text-white font-montserrat font-medium transition-all duration-500 hover:shadow-luxury-red hover:scale-105 flex items-center gap-2">
                <Crown className="h-4 w-4" />
                VIP Portal
              </div>
            </Link>
          </nav>

          {/* Elegant Mobile Menu Button */}
          <button
            className="md:hidden rounded-2xl p-3 text-white/95 hover:bg-gradient-to-r hover:from-luxury-gold/20 hover:to-luxury-purple/20 hover:text-luxury-gold transition-all duration-500"
            onClick={toggleMenu}
          >
            <Menu className="h-6 w-6" />
          </button>
        </div>

        {/* Elegant Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-8 border-t border-gradient-to-r from-luxury-gold/30 to-luxury-purple/30 bg-gradient-to-br from-luxury-gold/5 via-luxury-purple/5 to-luxury-red/5 backdrop-blur-md rounded-b-3xl">
            <nav className="flex flex-col space-y-6">
              {isHomePage ? (
                <>
                  <button
                    onClick={() => {
                      onAboutClick?.();
                      setIsMenuOpen(false);
                    }}
                    className="font-montserrat font-medium text-white/95 hover:text-luxury-purple transition-all duration-500 px-6 py-3 text-left relative group"
                  >
                    About
                    <span className="absolute left-6 -bottom-1 w-0 h-0.5 bg-gradient-to-r from-luxury-gold to-luxury-purple transition-all duration-500 group-hover:w-12"></span>
                  </button>
                  <button
                    onClick={() => {
                      onProgramsClick?.();
                      setIsMenuOpen(false);
                    }}
                    className="font-montserrat font-medium text-white/95 hover:text-luxury-purple transition-all duration-500 px-6 py-3 text-left relative group"
                  >
                    Programs
                    <span className="absolute left-6 -bottom-1 w-0 h-0.5 bg-gradient-to-r from-luxury-gold to-luxury-purple transition-all duration-500 group-hover:w-16"></span>
                  </button>
                  <button
                    onClick={() => {
                      onContactClick?.();
                      setIsMenuOpen(false);
                    }}
                    className="font-montserrat font-medium text-white/95 hover:text-luxury-purple transition-all duration-500 px-6 py-3 text-left relative group"
                  >
                    Contact
                    <span className="absolute left-6 -bottom-1 w-0 h-0.5 bg-gradient-to-r from-luxury-gold to-luxury-purple transition-all duration-500 group-hover:w-14"></span>
                  </button>
                </>
              ) : (
                <Link
                  to="/"
                  className="font-montserrat font-medium text-white/95 hover:text-luxury-purple transition-all duration-500 px-6 py-3 relative group"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Home
                  <span className="absolute left-6 -bottom-1 w-0 h-0.5 bg-gradient-to-r from-luxury-gold to-luxury-purple transition-all duration-500 group-hover:w-12"></span>
                </Link>
              )}

              {/* Mobile VIP Indicator */}
              {showVIPIndicator && isVIP && (
                <div className="px-6 py-2">
                  <VIPIndicator size="sm" animated={false} />
                </div>
              )}

              {/* Mobile Luxury Login Button */}
              <div className="px-6 py-2">
                <Link to="/login" onClick={() => setIsMenuOpen(false)}>
                  <div className="w-full px-6 py-3 bg-gradient-to-r from-luxury-gold via-luxury-purple to-luxury-red rounded-full text-white font-montserrat font-medium transition-all duration-500 hover:shadow-luxury-red flex items-center justify-center gap-2">
                    <Crown className="h-4 w-4" />
                    VIP Portal
                  </div>
                </Link>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default LuxuryHeader;
