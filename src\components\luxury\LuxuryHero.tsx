import React, { useRef, useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Crown, Star } from 'lucide-react';
import VIPIndicator from './VIPIndicator';
import { useLuxuryTheme } from '@/contexts/LuxuryThemeContext';
import { useGSAPParallax, useGSAPEntrance, useGSAPFloating } from '@/utils/gsapLuxuryAnimations';

interface LuxuryHeroProps {
  title?: string;
  subtitle?: string;
  ctaText?: string;
  backgroundAnimation?: boolean;
}

const LuxuryHero: React.FC<LuxuryHeroProps> = ({
  title = "Elite Promise Academy",
  subtitle = "Where luxury meets learning in an exclusive educational experience designed for extraordinary children",
  ctaText = "Enter VIP Portal",
  backgroundAnimation = true
}) => {
  const heroRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);
  const backgroundRef = useRef<HTMLDivElement>(null);
  const floatingElement1Ref = useRef<HTMLDivElement>(null);
  const floatingElement2Ref = useRef<HTMLDivElement>(null);
  const { theme, isVIP, isElite } = useLuxuryTheme();

  // GSAP Animations
  useGSAPEntrance(heroRef, { from: 'fade', duration: 1.5 });
  useGSAPParallax(backgroundRef, { yPercent: -30, scale: 1.1 });
  useGSAPParallax(imageRef, { yPercent: -20, scale: 1.05 });
  useGSAPFloating(floatingElement1Ref, { amplitude: 15, duration: 6 });
  useGSAPFloating(floatingElement2Ref, { amplitude: 20, duration: 8, delay: 2 });

  // Golden ratio proportions for asymmetric layout
  const goldenRatio = 1.618;
  const contentWidth = `${100 / goldenRatio}%`; // ~61.8%
  const imageWidth = `${100 - (100 / goldenRatio)}%`; // ~38.2%

  return (
    <div
      ref={heroRef}
      className="relative min-h-screen flex items-center overflow-hidden"
    >
      {/* Luxury Gold-Purple-Red Gradient Background */}
      <div
        ref={backgroundRef}
        className="absolute inset-0 bg-hero-luxury"
        style={{
          background: 'linear-gradient(135deg, #FFD700 0%, #663399 50%, #DC143C 100%)'
        }}
      />

      {/* Sophisticated Floating Elements */}
      <div
        ref={floatingElement1Ref}
        className="absolute top-1/4 left-1/4 w-32 h-32 bg-luxury-gold/10 rounded-full blur-xl"
      />
      <div
        ref={floatingElement2Ref}
        className="absolute bottom-1/3 right-1/4 w-48 h-48 bg-luxury-purple/10 rounded-full blur-2xl"
      />

      {/* Elegant Geometric Shapes */}
      <Crown className="absolute top-20 right-20 h-12 w-12 text-luxury-gold/20" />
      <Star className="absolute bottom-32 left-16 h-8 w-8 text-luxury-red/30" />

      {/* Responsive Asymmetric Artistic Layout */}
      <div className="luxury-container relative z-10 h-full flex items-center">
        <div className="flex flex-col lg:flex-row items-center h-full w-full">
          {/* Text Content - Responsive Width */}
          <div
            className="flex flex-col justify-center space-y-8 lg:space-y-12 text-center lg:text-left px-4 sm:px-8 lg:px-16 w-full lg:w-auto"
            style={{ width: window.innerWidth >= 1024 ? contentWidth : '100%', minHeight: '80vh' }}
          >
            {/* VIP Status Indicator */}
            {isVIP && (
              <div className="flex justify-start">
                <VIPIndicator level={theme.premiumLevel} size="lg" animated={false} />
              </div>
            )}

            {/* Responsive Main Title with Cormorant Garamond */}
            <div className="space-y-6 lg:space-y-8">
              <h1
                ref={titleRef}
                className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-cormorant font-semibold leading-tight text-white"
              >
                <span className="block bg-gradient-to-r from-luxury-gold via-white to-luxury-red bg-clip-text text-transparent">
                  {title}
                </span>
              </h1>

              {/* Responsive Subtitle */}
              <p
                ref={subtitleRef}
                className="text-lg sm:text-xl md:text-2xl lg:text-3xl text-white/95 leading-relaxed font-montserrat font-light max-w-2xl mx-auto lg:mx-0"
              >
                {subtitle}
              </p>
            </div>
            
            {/* Responsive CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 lg:gap-6 justify-center lg:justify-start">
              <Link to="/login" className="group">
                <div className="px-6 sm:px-8 py-3 sm:py-4 bg-gradient-to-r from-luxury-gold via-luxury-purple to-luxury-red rounded-full text-white font-montserrat font-semibold text-base sm:text-lg transition-all duration-500 hover:shadow-luxury-red hover:scale-105 flex items-center justify-center gap-3">
                  <Crown className="h-5 w-5 sm:h-6 sm:w-6" />
                  {ctaText}
                  <ArrowRight className="h-5 w-5 sm:h-6 sm:w-6 group-hover:translate-x-1 transition-transform duration-300" />
                </div>
              </Link>

              <Link to="/login" className="group">
                <div className="px-6 sm:px-8 py-3 sm:py-4 border-2 border-white/30 rounded-full text-white font-montserrat font-medium text-base sm:text-lg transition-all duration-500 hover:bg-white/10 hover:border-white/50 flex items-center justify-center gap-3">
                  <Star className="h-5 w-5 sm:h-6 sm:w-6" />
                  Staff Access
                </div>
              </Link>
            </div>

            {/* Minimalist Trust Indicators */}
            <div className="flex items-center gap-8 mt-12">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 rounded-full bg-gradient-to-br from-luxury-gold to-luxury-red flex items-center justify-center text-white font-cormorant font-bold text-lg shadow-luxury-gold">
                  100%
                </div>
                <div className="text-white/90">
                  <div className="font-montserrat font-semibold">Elite Excellence</div>
                  <div className="font-montserrat text-sm text-white/70">Satisfaction Rate</div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Responsive Hero Image */}
          <div
            className="relative flex items-center justify-center w-full lg:w-auto mt-8 lg:mt-0"
            style={{ width: window.innerWidth >= 1024 ? imageWidth : '100%', minHeight: window.innerWidth >= 1024 ? '80vh' : 'auto' }}
          >
            {/* Subtle Background Glow */}
            <div className="absolute top-1/4 right-1/4 w-32 sm:w-48 lg:w-64 h-32 sm:h-48 lg:h-64 bg-luxury-red/20 rounded-full blur-3xl" />

            <div
              ref={imageRef}
              className="relative z-10 bg-white/10 backdrop-blur-sm p-4 sm:p-6 rounded-2xl lg:rounded-3xl shadow-luxury-gold border border-white/20 max-w-md lg:max-w-none"
            >
              <img
                src="https://images.unsplash.com/photo-1518495973542-4542c06a5843"
                alt="Elite children in luxury learning environment"
                className="w-full h-auto rounded-xl lg:rounded-2xl shadow-luxury-purple"
              />

              {/* Responsive Achievement Badge */}
              <div className="absolute -top-2 -right-2 sm:-top-4 sm:-right-4 bg-gradient-to-br from-luxury-gold to-luxury-red p-2 sm:p-4 rounded-xl lg:rounded-2xl shadow-luxury-red">
                <Crown className="h-4 w-4 sm:h-6 sm:w-6 text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Elegant Bottom Gradient */}
      <div className="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-luxury-purple/20 to-transparent" />
    </div>
  );
};

export default LuxuryHero;
