import React from 'react';
import { Crown, Sparkles } from 'lucide-react';

interface LuxuryLoaderProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  showSparkles?: boolean;
}

const LuxuryLoader: React.FC<LuxuryLoaderProps> = ({
  message = "Loading Elite Experience...",
  size = 'md',
  showSparkles = true
}) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          container: 'w-16 h-16',
          crown: 'h-6 w-6',
          text: 'text-sm'
        };
      case 'lg':
        return {
          container: 'w-32 h-32',
          crown: 'h-12 w-12',
          text: 'text-xl'
        };
      default:
        return {
          container: 'w-24 h-24',
          crown: 'h-8 w-8',
          text: 'text-base'
        };
    }
  };

  const sizeClasses = getSizeClasses();

  return (
    <div className="flex flex-col items-center justify-center space-y-6">
      {/* Animated Crown */}
      <div className="relative">
        <div className={`${sizeClasses.container} relative flex items-center justify-center`}>
          {/* Rotating Ring */}
          <div className="absolute inset-0 rounded-full border-4 border-luxury-gold/20 border-t-luxury-gold animate-spin" />
          
          {/* Inner Glow Ring */}
          <div className="absolute inset-2 rounded-full border-2 border-luxury-purple/30 border-b-luxury-purple animate-spin" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }} />
          
          {/* Crown Icon */}
          <Crown className={`${sizeClasses.crown} text-luxury-gold animate-luxury-glow z-10`} />
          
          {/* Sparkle Effects */}
          {showSparkles && (
            <>
              <Sparkles className="absolute -top-2 -right-2 h-4 w-4 text-luxury-gold animate-luxury-sparkle" />
              <Sparkles className="absolute -bottom-2 -left-2 h-3 w-3 text-luxury-platinum animate-luxury-sparkle" style={{ animationDelay: '0.5s' }} />
              <Sparkles className="absolute top-1/2 -left-4 h-2 w-2 text-luxury-emerald animate-luxury-sparkle" style={{ animationDelay: '1s' }} />
              <Sparkles className="absolute top-1/2 -right-4 h-2 w-2 text-luxury-rose-gold animate-luxury-sparkle" style={{ animationDelay: '1.5s' }} />
            </>
          )}
        </div>
        
        {/* Outer Glow Effect */}
        <div className="absolute inset-0 rounded-full bg-luxury-gold/10 blur-xl animate-pulse" />
      </div>

      {/* Loading Message */}
      <div className="text-center space-y-2">
        <p className={`${sizeClasses.text} font-playfair font-bold luxury-heading`}>
          {message}
        </p>
        
        {/* Loading Dots */}
        <div className="flex justify-center space-x-1">
          <div className="w-2 h-2 bg-luxury-gold rounded-full animate-bounce" />
          <div className="w-2 h-2 bg-luxury-gold rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
          <div className="w-2 h-2 bg-luxury-gold rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
        </div>
      </div>
    </div>
  );
};

export default LuxuryLoader;
