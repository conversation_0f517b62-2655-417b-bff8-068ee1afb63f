import React, { useRef, useEffect } from 'react';
import { useLuxuryTheme } from '@/contexts/LuxuryThemeContext';
import { createLuxuryRipple, playLuxurySound, useLuxury3DHover } from '@/utils/luxuryAnimations';

interface LuxuryInteractiveElementProps {
  children: React.ReactNode;
  type?: 'button' | 'card' | 'input' | 'link';
  soundEffect?: boolean;
  rippleEffect?: boolean;
  hover3D?: boolean;
  glowOnHover?: boolean;
  className?: string;
  onClick?: (e: React.MouseEvent) => void;
  onHover?: () => void;
}

const LuxuryInteractiveElement: React.FC<LuxuryInteractiveElementProps> = ({
  children,
  type = 'button',
  soundEffect = true,
  rippleEffect = true,
  hover3D = false,
  glowOnHover = true,
  className = '',
  onClick,
  onHover
}) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const { theme } = useLuxuryTheme();

  // Apply 3D hover effect if enabled
  if (hover3D) {
    useLuxury3DHover(elementRef);
  }

  const handleClick = (e: React.MouseEvent) => {
    if (rippleEffect && elementRef.current) {
      const rect = elementRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      createLuxuryRipple(elementRef.current, x, y);
    }

    if (soundEffect && theme.effects.sounds) {
      playLuxurySound('click');
    }

    onClick?.(e);
  };

  const handleMouseEnter = () => {
    if (soundEffect && theme.effects.sounds) {
      playLuxurySound('hover');
    }

    if (glowOnHover && elementRef.current) {
      elementRef.current.style.boxShadow = '0 0 30px rgba(255, 215, 0, 0.5)';
    }

    onHover?.();
  };

  const handleMouseLeave = () => {
    if (glowOnHover && elementRef.current) {
      elementRef.current.style.boxShadow = '';
    }
  };

  const getTypeClasses = () => {
    const baseClasses = 'relative overflow-hidden transition-all duration-300 ease-out cursor-pointer';
    
    switch (type) {
      case 'button':
        return `${baseClasses} px-6 py-3 rounded-xl font-semibold`;
      case 'card':
        return `${baseClasses} p-6 rounded-2xl`;
      case 'input':
        return `${baseClasses} px-4 py-2 rounded-lg border`;
      case 'link':
        return `${baseClasses} inline-block`;
      default:
        return baseClasses;
    }
  };

  return (
    <div
      ref={elementRef}
      className={`${getTypeClasses()} ${className}`}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {children}
    </div>
  );
};

// Enhanced form validation with luxury feedback
interface LuxuryFormFieldProps {
  label: string;
  type?: 'text' | 'email' | 'tel' | 'password';
  value: string;
  onChange: (value: string) => void;
  validation?: (value: string) => string | null;
  required?: boolean;
  placeholder?: string;
  className?: string;
}

const LuxuryFormField: React.FC<LuxuryFormFieldProps> = ({
  label,
  type = 'text',
  value,
  onChange,
  validation,
  required = false,
  placeholder,
  className = ''
}) => {
  const [error, setError] = React.useState<string | null>(null);
  const [isFocused, setIsFocused] = React.useState(false);
  const [isValid, setIsValid] = React.useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const { theme } = useLuxuryTheme();

  const validateField = (val: string) => {
    if (required && !val.trim()) {
      setError('This field is required');
      setIsValid(false);
      return;
    }

    if (validation) {
      const validationError = validation(val);
      setError(validationError);
      setIsValid(!validationError);
    } else {
      setError(null);
      setIsValid(!!val.trim());
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
    validateField(newValue);
  };

  const handleFocus = () => {
    setIsFocused(true);
    if (theme.effects.sounds) {
      playLuxurySound('chime');
    }
  };

  const handleBlur = () => {
    setIsFocused(false);
    validateField(value);
    if (theme.effects.sounds && isValid) {
      playLuxurySound('success');
    }
  };

  const getFieldClasses = () => {
    let classes = 'w-full px-4 py-3 rounded-xl border-2 transition-all duration-300 font-montserrat bg-white/10 text-white placeholder:text-white/50';
    
    if (error) {
      classes += ' border-red-400 focus:border-red-500';
    } else if (isValid) {
      classes += ' border-luxury-emerald focus:border-luxury-emerald';
    } else if (isFocused) {
      classes += ' border-luxury-gold focus:border-luxury-gold';
    } else {
      classes += ' border-luxury-gold/30 focus:border-luxury-gold';
    }

    if (isFocused) {
      classes += ' shadow-glow-gold';
    }

    return classes;
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <label className="block text-white font-montserrat font-semibold">
        {label}
        {required && <span className="text-luxury-gold ml-1">*</span>}
      </label>
      
      <div className="relative">
        <input
          ref={inputRef}
          type={type}
          value={value}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          className={getFieldClasses()}
        />
        
        {/* Validation Icon */}
        {value && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {isValid ? (
              <div className="w-5 h-5 bg-luxury-emerald rounded-full flex items-center justify-center">
                <span className="text-white text-xs">✓</span>
              </div>
            ) : error ? (
              <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                <span className="text-white text-xs">!</span>
              </div>
            ) : null}
          </div>
        )}
      </div>
      
      {/* Error Message */}
      {error && (
        <p className="text-red-400 text-sm font-montserrat animate-luxury-fade-in">
          {error}
        </p>
      )}
      
      {/* Success Message */}
      {isValid && !error && value && (
        <p className="text-luxury-emerald text-sm font-montserrat animate-luxury-fade-in">
          Perfect! ✨
        </p>
      )}
    </div>
  );
};

// Luxury cursor effects
export const useLuxuryCursor = () => {
  useEffect(() => {
    const cursor = document.createElement('div');
    cursor.className = 'luxury-cursor';
    cursor.style.cssText = `
      position: fixed;
      width: 20px;
      height: 20px;
      background: radial-gradient(circle, rgba(255, 215, 0, 0.8) 0%, transparent 70%);
      border-radius: 50%;
      pointer-events: none;
      z-index: 9999;
      transition: transform 0.1s ease-out;
    `;
    document.body.appendChild(cursor);

    const trail = document.createElement('div');
    trail.className = 'luxury-cursor-trail';
    trail.style.cssText = `
      position: fixed;
      width: 40px;
      height: 40px;
      background: radial-gradient(circle, rgba(255, 215, 0, 0.2) 0%, transparent 70%);
      border-radius: 50%;
      pointer-events: none;
      z-index: 9998;
      transition: transform 0.2s ease-out;
    `;
    document.body.appendChild(trail);

    const handleMouseMove = (e: MouseEvent) => {
      cursor.style.left = e.clientX - 10 + 'px';
      cursor.style.top = e.clientY - 10 + 'px';
      trail.style.left = e.clientX - 20 + 'px';
      trail.style.top = e.clientY - 20 + 'px';
    };

    const handleMouseDown = () => {
      cursor.style.transform = 'scale(0.8)';
      trail.style.transform = 'scale(1.2)';
    };

    const handleMouseUp = () => {
      cursor.style.transform = 'scale(1)';
      trail.style.transform = 'scale(1)';
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('mouseup', handleMouseUp);
      cursor.remove();
      trail.remove();
    };
  }, []);
};

export { LuxuryInteractiveElement, LuxuryFormField };
