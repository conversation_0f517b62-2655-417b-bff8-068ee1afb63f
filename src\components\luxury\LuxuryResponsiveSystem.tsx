import React, { useState, useEffect } from 'react';
import { useLuxuryTheme } from '@/contexts/LuxuryThemeContext';

// Responsive breakpoint hook
export const useLuxuryBreakpoint = () => {
  const [breakpoint, setBreakpoint] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');

  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setBreakpoint('mobile');
      } else if (width < 1024) {
        setBreakpoint('tablet');
      } else {
        setBreakpoint('desktop');
      }
    };

    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);

  return breakpoint;
};

// Responsive luxury container
interface LuxuryResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  mobileLayout?: 'stack' | 'grid' | 'carousel';
  tabletLayout?: 'grid' | 'masonry' | 'columns';
  desktopLayout?: 'grid' | 'masonry' | 'columns' | 'sidebar';
}

export const LuxuryResponsiveContainer: React.FC<LuxuryResponsiveContainerProps> = ({
  children,
  className = '',
  mobileLayout = 'stack',
  tabletLayout = 'grid',
  desktopLayout = 'grid'
}) => {
  const breakpoint = useLuxuryBreakpoint();

  const getLayoutClasses = () => {
    const baseClasses = 'luxury-container transition-all duration-500 ease-out';
    
    switch (breakpoint) {
      case 'mobile':
        switch (mobileLayout) {
          case 'stack':
            return `${baseClasses} flex flex-col space-y-4`;
          case 'grid':
            return `${baseClasses} grid grid-cols-1 gap-4`;
          case 'carousel':
            return `${baseClasses} flex overflow-x-auto space-x-4 pb-4`;
          default:
            return `${baseClasses} flex flex-col space-y-4`;
        }
      case 'tablet':
        switch (tabletLayout) {
          case 'grid':
            return `${baseClasses} grid grid-cols-2 gap-6`;
          case 'masonry':
            return `${baseClasses} columns-2 gap-6`;
          case 'columns':
            return `${baseClasses} flex flex-wrap gap-6`;
          default:
            return `${baseClasses} grid grid-cols-2 gap-6`;
        }
      case 'desktop':
        switch (desktopLayout) {
          case 'grid':
            return `${baseClasses} grid grid-cols-3 lg:grid-cols-4 gap-8`;
          case 'masonry':
            return `${baseClasses} columns-3 lg:columns-4 gap-8`;
          case 'columns':
            return `${baseClasses} flex flex-wrap gap-8`;
          case 'sidebar':
            return `${baseClasses} grid grid-cols-4 gap-8`;
          default:
            return `${baseClasses} grid grid-cols-3 lg:grid-cols-4 gap-8`;
        }
      default:
        return baseClasses;
    }
  };

  return (
    <div className={`${getLayoutClasses()} ${className}`}>
      {children}
    </div>
  );
};

// Responsive luxury typography
interface LuxuryResponsiveTextProps {
  children: React.ReactNode;
  variant: 'hero' | 'heading' | 'subheading' | 'body' | 'caption';
  className?: string;
  gradient?: boolean;
  glow?: boolean;
}

export const LuxuryResponsiveText: React.FC<LuxuryResponsiveTextProps> = ({
  children,
  variant,
  className = '',
  gradient = false,
  glow = false
}) => {
  const breakpoint = useLuxuryBreakpoint();

  const getTypographyClasses = () => {
    const baseClasses = 'font-playfair transition-all duration-300';
    const gradientClass = gradient ? 'luxury-heading' : '';
    const glowClass = glow ? 'animate-luxury-text-glow' : '';
    
    const responsiveClasses = {
      hero: {
        mobile: 'text-3xl md:text-4xl font-bold',
        tablet: 'text-4xl md:text-5xl font-bold',
        desktop: 'text-5xl lg:text-6xl xl:text-7xl font-bold'
      },
      heading: {
        mobile: 'text-2xl md:text-3xl font-bold',
        tablet: 'text-3xl md:text-4xl font-bold',
        desktop: 'text-4xl lg:text-5xl font-bold'
      },
      subheading: {
        mobile: 'text-lg md:text-xl font-semibold',
        tablet: 'text-xl md:text-2xl font-semibold',
        desktop: 'text-2xl lg:text-3xl font-semibold'
      },
      body: {
        mobile: 'text-sm md:text-base font-montserrat',
        tablet: 'text-base md:text-lg font-montserrat',
        desktop: 'text-lg lg:text-xl font-montserrat'
      },
      caption: {
        mobile: 'text-xs md:text-sm font-montserrat',
        tablet: 'text-sm font-montserrat',
        desktop: 'text-sm lg:text-base font-montserrat'
      }
    };

    return `${baseClasses} ${responsiveClasses[variant][breakpoint]} ${gradientClass} ${glowClass}`;
  };

  return (
    <div className={`${getTypographyClasses()} ${className}`}>
      {children}
    </div>
  );
};

// Touch-friendly luxury interactions for mobile
export const useLuxuryTouchInteractions = () => {
  const [isTouchDevice, setIsTouchDevice] = useState(false);

  useEffect(() => {
    setIsTouchDevice('ontouchstart' in window || navigator.maxTouchPoints > 0);
  }, []);

  const getTouchClasses = (baseClasses: string) => {
    if (isTouchDevice) {
      return `${baseClasses} touch-manipulation select-none`;
    }
    return baseClasses;
  };

  const getTouchStyles = () => {
    if (isTouchDevice) {
      return {
        WebkitTapHighlightColor: 'rgba(255, 215, 0, 0.3)',
        WebkitTouchCallout: 'none',
        WebkitUserSelect: 'none',
        userSelect: 'none' as const
      };
    }
    return {};
  };

  return { isTouchDevice, getTouchClasses, getTouchStyles };
};

// Luxury mobile navigation
interface LuxuryMobileNavProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}

export const LuxuryMobileNav: React.FC<LuxuryMobileNavProps> = ({
  isOpen,
  onClose,
  children
}) => {
  const { theme } = useLuxuryTheme();

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 lg:hidden">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Navigation Panel */}
      <div className="absolute top-0 right-0 h-full w-80 max-w-[90vw] luxury-card border-l border-luxury-gold/20 animate-luxury-fade-in">
        <div className="p-6 space-y-6">
          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 p-2 text-white/70 hover:text-luxury-gold transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
          
          {/* Navigation Content */}
          <div className="pt-8">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

// Responsive luxury spacing
export const getLuxurySpacing = (breakpoint: 'mobile' | 'tablet' | 'desktop') => {
  const spacing = {
    mobile: {
      section: 'py-12',
      container: 'px-4',
      gap: 'gap-4',
      margin: 'mb-6'
    },
    tablet: {
      section: 'py-16',
      container: 'px-6',
      gap: 'gap-6',
      margin: 'mb-8'
    },
    desktop: {
      section: 'py-20 lg:py-24',
      container: 'px-8',
      gap: 'gap-8',
      margin: 'mb-12'
    }
  };

  return spacing[breakpoint];
};

// Responsive luxury grid system
interface LuxuryGridProps {
  children: React.ReactNode;
  cols?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  gap?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  className?: string;
}

export const LuxuryGrid: React.FC<LuxuryGridProps> = ({
  children,
  cols = { mobile: 1, tablet: 2, desktop: 3 },
  gap = { mobile: 4, tablet: 6, desktop: 8 },
  className = ''
}) => {
  const getGridClasses = () => {
    const mobileClasses = `grid-cols-${cols.mobile} gap-${gap.mobile}`;
    const tabletClasses = `md:grid-cols-${cols.tablet} md:gap-${gap.tablet}`;
    const desktopClasses = `lg:grid-cols-${cols.desktop} lg:gap-${gap.desktop}`;
    
    return `grid ${mobileClasses} ${tabletClasses} ${desktopClasses}`;
  };

  return (
    <div className={`${getGridClasses()} ${className}`}>
      {children}
    </div>
  );
};
