import React, { useRef, useState, useEffect } from 'react';
import { Star, Crown, Quote, ChevronLeft, ChevronRight, Sparkles } from 'lucide-react';
import VIPIndicator from './VIPIndicator';
import LuxuryButton from './LuxuryButton';
import { useLuxuryTheme } from '@/contexts/LuxuryThemeContext';
import { useLuxuryIntersectionObserver } from '@/utils/luxuryAnimations';

interface LuxuryTestimonial {
  id: string;
  name: string;
  title: string;
  location: string;
  rating: number;
  testimonial: string;
  avatar: string;
  vipLevel: 'standard' | 'premium' | 'vip' | 'elite';
  verified: boolean;
  children: string[];
}

const LuxuryTestimonials: React.FC = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const { theme } = useLuxuryTheme();

  useLuxuryIntersectionObserver(sectionRef, 'animate-luxury-fade-in');

  const testimonials: LuxuryTestimonial[] = [
    {
      id: '1',
      name: 'Victoria <PERSON>worth',
      title: 'CEO, Ashworth Enterprises',
      location: 'Beverly Hills, CA',
      rating: 5,
      testimonial: 'Promise Academy has exceeded every expectation. The level of personalized attention and luxury amenities creates an environment where our daughter Charlotte thrives. The VIP program is truly exceptional.',
      avatar: 'https://images.unsplash.com/photo-*************-2616b612b786',
      vipLevel: 'elite',
      verified: true,
      children: ['Charlotte, Age 4']
    },
    {
      id: '2',
      name: 'Alexander Montgomery',
      title: 'Investment Banker',
      location: 'Manhattan, NY',
      rating: 5,
      testimonial: 'The elite curriculum and world-class educators have prepared our twins for the most prestigious private schools. The attention to detail and luxury experience is unmatched.',
      avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e',
      vipLevel: 'vip',
      verified: true,
      children: ['James & Emma, Age 5']
    },
    {
      id: '3',
      name: 'Sophia Chen-Williams',
      title: 'Tech Entrepreneur',
      location: 'Palo Alto, CA',
      rating: 5,
      testimonial: 'As a working mother, I needed the absolute best care for my son. The premium services and 24/7 support give me complete peace of mind while maintaining the highest educational standards.',
      avatar: 'https://images.unsplash.com/photo-*************-6461ffad8d80',
      vipLevel: 'premium',
      verified: true,
      children: ['Marcus, Age 3']
    },
    {
      id: '4',
      name: 'Lord James Pemberton',
      title: 'British Aristocrat',
      location: 'London, UK',
      rating: 5,
      testimonial: 'Having attended the finest institutions myself, I can confidently say Promise Academy provides an education worthy of royalty. The exclusive programs are simply extraordinary.',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d',
      vipLevel: 'elite',
      verified: true,
      children: ['Lady Catherine, Age 4']
    }
  ];

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return;
    
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    
    return () => clearInterval(interval);
  }, [isAutoPlaying, testimonials.length]);

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
    setIsAutoPlaying(false);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
    setIsAutoPlaying(false);
  };

  const goToTestimonial = (index: number) => {
    setCurrentIndex(index);
    setIsAutoPlaying(false);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-5 w-5 ${
          i < rating ? 'text-luxury-gold fill-luxury-gold' : 'text-gray-300'
        }`}
      />
    ));
  };

  const getVIPBadgeColor = (level: string) => {
    switch (level) {
      case 'elite': return 'text-luxury-purple';
      case 'vip': return 'text-luxury-gold';
      case 'premium': return 'text-luxury-rose-gold';
      default: return 'text-gray-500';
    }
  };

  const currentTestimonial = testimonials[currentIndex];

  return (
    <section 
      ref={sectionRef}
      className="luxury-section relative overflow-hidden"
      style={{
        background: 'linear-gradient(135deg, #1B1B3A 0%, #0F0F23 50%, #2D1B69 100%)'
      }}
    >
      <div className="luxury-container">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-playfair font-bold luxury-heading mb-6">
            Elite Family Testimonials
          </h2>
          <p className="text-xl text-white/90 font-montserrat max-w-4xl mx-auto leading-relaxed">
            Discover why the world's most discerning families choose Promise Academy for their children's elite education.
          </p>
        </div>

        {/* Main Testimonial Display */}
        <div className="relative max-w-6xl mx-auto">
          <div className="luxury-card p-12 rounded-3xl relative overflow-hidden">
            {/* Background Effects */}
            <div className="absolute inset-0 bg-gradient-to-br from-luxury-gold/5 to-luxury-purple/5" />
            
            {/* Quote Icon */}
            <Quote className="absolute top-8 left-8 h-12 w-12 text-luxury-gold/30" />
            
            {/* Sparkle Effects */}
            {theme.effects.animations && (
              <>
                <Sparkles className="absolute top-6 right-6 h-4 w-4 text-luxury-gold/50 animate-luxury-sparkle" />
                <Sparkles className="absolute bottom-6 left-6 h-4 w-4 text-luxury-gold/50 animate-luxury-sparkle" style={{ animationDelay: '1s' }} />
              </>
            )}

            <div className="relative z-10 grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
              {/* Avatar and Info */}
              <div className="text-center lg:text-left">
                <div className="relative inline-block mb-6">
                  <img
                    src={currentTestimonial.avatar}
                    alt={currentTestimonial.name}
                    className="w-24 h-24 rounded-full object-cover border-4 border-luxury-gold shadow-luxury-gold"
                  />
                  {currentTestimonial.verified && (
                    <div className="absolute -bottom-2 -right-2 bg-luxury-emerald rounded-full p-1">
                      <Crown className="h-4 w-4 text-white" />
                    </div>
                  )}
                </div>
                
                <h3 className="text-2xl font-playfair font-bold text-white mb-2">
                  {currentTestimonial.name}
                </h3>
                
                <p className="text-luxury-gold font-montserrat font-semibold mb-1">
                  {currentTestimonial.title}
                </p>
                
                <p className="text-white/70 font-montserrat text-sm mb-4">
                  {currentTestimonial.location}
                </p>

                {/* VIP Level */}
                <VIPIndicator 
                  level={currentTestimonial.vipLevel} 
                  size="sm" 
                  animated={theme.effects.animations} 
                />

                {/* Children */}
                <div className="mt-4">
                  <p className="text-white/60 text-sm font-montserrat">
                    Parent of: {currentTestimonial.children.join(', ')}
                  </p>
                </div>
              </div>

              {/* Testimonial Content */}
              <div className="lg:col-span-2">
                {/* Rating */}
                <div className="flex justify-center lg:justify-start gap-1 mb-6">
                  {renderStars(currentTestimonial.rating)}
                </div>

                {/* Testimonial Text */}
                <blockquote className="text-xl lg:text-2xl font-montserrat text-white/95 leading-relaxed italic mb-6">
                  "{currentTestimonial.testimonial}"
                </blockquote>

                {/* Verified Badge */}
                {currentTestimonial.verified && (
                  <div className="inline-flex items-center gap-2 px-4 py-2 bg-luxury-emerald/20 rounded-full text-luxury-emerald text-sm font-semibold">
                    <Crown className="h-4 w-4" />
                    Verified Elite Family
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Navigation Arrows */}
          <button
            onClick={prevTestimonial}
            className="absolute left-4 top-1/2 -translate-y-1/2 p-3 bg-luxury-gold/20 hover:bg-luxury-gold/40 rounded-full text-luxury-gold transition-all duration-300 luxury-hover-glow"
          >
            <ChevronLeft className="h-6 w-6" />
          </button>
          
          <button
            onClick={nextTestimonial}
            className="absolute right-4 top-1/2 -translate-y-1/2 p-3 bg-luxury-gold/20 hover:bg-luxury-gold/40 rounded-full text-luxury-gold transition-all duration-300 luxury-hover-glow"
          >
            <ChevronRight className="h-6 w-6" />
          </button>
        </div>

        {/* Testimonial Indicators */}
        <div className="flex justify-center gap-3 mt-12">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => goToTestimonial(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentIndex
                  ? 'bg-luxury-gold shadow-glow-gold'
                  : 'bg-white/30 hover:bg-white/50'
              }`}
            />
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <LuxuryButton
            variant="gold"
            size="lg"
            glow={true}
            sparkles={true}
            soundEffect={theme.effects.sounds}
          >
            <Crown className="h-5 w-5" />
            Join Our Elite Community
            <Sparkles className="h-4 w-4" />
          </LuxuryButton>
        </div>
      </div>

      {/* Background Decorative Elements */}
      {theme.effects.animations && (
        <>
          <Crown className="absolute top-20 left-10 h-8 w-8 text-luxury-gold/10 animate-luxury-float" />
          <Star className="absolute bottom-20 right-10 h-6 w-6 text-luxury-platinum/10 animate-luxury-float" style={{ animationDelay: '2s' }} />
        </>
      )}
    </section>
  );
};

export default LuxuryTestimonials;
