import React, { useState, useEffect } from 'react';
import { Crown, Star, Palette, <PERSON>ting<PERSON>, <PERSON>, Moon, Snowf<PERSON>, <PERSON>, <PERSON>, Heart } from 'lucide-react';
import { useLuxuryTheme, LuxuryTheme } from '@/contexts/LuxuryThemeContext';
import LuxuryButton from './LuxuryButton';
import { AccessibleLuxuryButton } from './LuxuryAccessibility';

// Seasonal luxury themes
export const seasonalLuxuryThemes: Record<string, LuxuryTheme> = {
  spring: {
    name: 'Spring Elegance',
    colors: {
      primary: '#50C878', // Emerald green
      secondary: '#FFB6C1', // Light pink
      accent: '#FFD700', // Gold
      background: 'linear-gradient(135deg, #E8F5E8 0%, #F0FFF0 100%)',
      surface: 'rgba(80, 200, 120, 0.1)',
      text: '#2F4F2F',
    },
    effects: { glow: true, particles: true, animations: true, sounds: false },
    premiumLevel: 'premium',
  },
  summer: {
    name: 'Summer Luxury',
    colors: {
      primary: '#FFD700', // Bright gold
      secondary: '#FF7F50', // Coral
      accent: '#00BFFF', // Sky blue
      background: 'linear-gradient(135deg, #FFF8DC 0%, #FFFACD 100%)',
      surface: 'rgba(255, 215, 0, 0.1)',
      text: '#8B4513',
    },
    effects: { glow: true, particles: true, animations: true, sounds: false },
    premiumLevel: 'vip',
  },
  autumn: {
    name: 'Autumn Opulence',
    colors: {
      primary: '#CD853F', // Peru
      secondary: '#D2691E', // Chocolate
      accent: '#FFD700', // Gold
      background: 'linear-gradient(135deg, #F4A460 0%, #DEB887 100%)',
      surface: 'rgba(205, 133, 63, 0.1)',
      text: '#8B4513',
    },
    effects: { glow: true, particles: true, animations: true, sounds: false },
    premiumLevel: 'premium',
  },
  winter: {
    name: 'Winter Majesty',
    colors: {
      primary: '#E5E4E2', // Platinum
      secondary: '#4169E1', // Royal blue
      accent: '#87CEEB', // Sky blue
      background: 'linear-gradient(135deg, #F0F8FF 0%, #E6E6FA 100%)',
      surface: 'rgba(229, 228, 226, 0.1)',
      text: '#191970',
    },
    effects: { glow: true, particles: true, animations: true, sounds: false },
    premiumLevel: 'elite',
  },
};

// VIP personalization themes
export const vipPersonalizationThemes: Record<string, LuxuryTheme> = {
  princess: {
    name: 'Princess Dreams',
    colors: {
      primary: '#FF69B4', // Hot pink
      secondary: '#E8B4B8', // Rose gold
      accent: '#FFD700', // Gold
      background: 'linear-gradient(135deg, #FFE4E1 0%, #FFF0F5 100%)',
      surface: 'rgba(255, 105, 180, 0.1)',
      text: '#8B008B',
    },
    effects: { glow: true, particles: true, animations: true, sounds: false },
    premiumLevel: 'vip',
  },
  prince: {
    name: 'Royal Prince',
    colors: {
      primary: '#4169E1', // Royal blue
      secondary: '#FFD700', // Gold
      accent: '#C0C0C0', // Silver
      background: 'linear-gradient(135deg, #E6E6FA 0%, #F0F8FF 100%)',
      surface: 'rgba(65, 105, 225, 0.1)',
      text: '#191970',
    },
    effects: { glow: true, particles: true, animations: true, sounds: false },
    premiumLevel: 'vip',
  },
  rainbow: {
    name: 'Rainbow Magic',
    colors: {
      primary: '#FF69B4', // Hot pink
      secondary: '#00BFFF', // Deep sky blue
      accent: '#32CD32', // Lime green
      background: 'linear-gradient(135deg, #FFB6C1 0%, #87CEEB 50%, #98FB98 100%)',
      surface: 'rgba(255, 255, 255, 0.2)',
      text: '#4B0082',
    },
    effects: { glow: true, particles: true, animations: true, sounds: false },
    premiumLevel: 'elite',
  },
  galaxy: {
    name: 'Galaxy Explorer',
    colors: {
      primary: '#9370DB', // Medium purple
      secondary: '#4169E1', // Royal blue
      accent: '#FFD700', // Gold
      background: 'linear-gradient(135deg, #191970 0%, #4B0082 50%, #8A2BE2 100%)',
      surface: 'rgba(147, 112, 219, 0.1)',
      text: '#E6E6FA',
    },
    effects: { glow: true, particles: true, animations: true, sounds: false },
    premiumLevel: 'elite',
  },
};

interface LuxuryThemeCustomizerProps {
  isOpen: boolean;
  onClose: () => void;
}

const LuxuryThemeCustomizer: React.FC<LuxuryThemeCustomizerProps> = ({
  isOpen,
  onClose
}) => {
  const { theme, setTheme, toggleEffect, setPremiumLevel, isVIP, isElite } = useLuxuryTheme();
  const [activeTab, setActiveTab] = useState<'themes' | 'seasonal' | 'personalization' | 'effects'>('themes');
  const [customColors, setCustomColors] = useState(theme.colors);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  const handleThemeSelect = (selectedTheme: LuxuryTheme) => {
    setTheme(selectedTheme);
    setCustomColors(selectedTheme.colors);
  };

  const handleCustomColorChange = (colorKey: keyof typeof customColors, value: string) => {
    const newColors = { ...customColors, [colorKey]: value };
    setCustomColors(newColors);
    setTheme({ ...theme, colors: newColors });
  };

  const ThemePreview: React.FC<{ luxuryTheme: LuxuryTheme; onClick: () => void }> = ({ luxuryTheme, onClick }) => (
    <button
      onClick={onClick}
      className="w-full p-4 rounded-xl border-2 border-transparent hover:border-luxury-gold transition-all duration-300 group"
      style={{ background: luxuryTheme.colors.background }}
    >
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h4 className="font-playfair font-bold" style={{ color: luxuryTheme.colors.text }}>
            {luxuryTheme.name}
          </h4>
          <Crown className="h-4 w-4" style={{ color: luxuryTheme.colors.primary }} />
        </div>
        <div className="flex gap-2">
          <div className="w-4 h-4 rounded-full" style={{ backgroundColor: luxuryTheme.colors.primary }} />
          <div className="w-4 h-4 rounded-full" style={{ backgroundColor: luxuryTheme.colors.secondary }} />
          <div className="w-4 h-4 rounded-full" style={{ backgroundColor: luxuryTheme.colors.accent }} />
        </div>
      </div>
    </button>
  );

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Customizer Panel */}
      <div className="relative w-full max-w-4xl max-h-[90vh] luxury-card m-4 overflow-hidden">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-luxury-gold/20">
            <div className="flex items-center gap-3">
              <Palette className="h-6 w-6 text-luxury-gold" />
              <h2 className="text-2xl font-playfair font-bold text-white">
                Luxury Theme Customizer
              </h2>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-white/70 hover:text-luxury-gold transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Tabs */}
          <div className="flex border-b border-luxury-gold/20">
            {[
              { id: 'themes', label: 'Themes', icon: Crown },
              { id: 'seasonal', label: 'Seasonal', icon: Leaf },
              { id: 'personalization', label: 'VIP Personal', icon: Star, vipOnly: true },
              { id: 'effects', label: 'Effects', icon: Settings }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                disabled={tab.vipOnly && !isVIP}
                className={`flex items-center gap-2 px-6 py-3 font-montserrat font-semibold transition-colors ${
                  activeTab === tab.id
                    ? 'text-luxury-gold border-b-2 border-luxury-gold'
                    : 'text-white/70 hover:text-white'
                } ${tab.vipOnly && !isVIP ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                <tab.icon className="h-4 w-4" />
                {tab.label}
                {tab.vipOnly && <Crown className="h-3 w-3" />}
              </button>
            ))}
          </div>

          {/* Content */}
          <div className="flex-1 p-6 overflow-y-auto">
            {activeTab === 'themes' && (
              <div className="space-y-6">
                <h3 className="text-xl font-playfair font-bold text-white">Premium Themes</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Object.values(seasonalLuxuryThemes).map((luxuryTheme) => (
                    <ThemePreview
                      key={luxuryTheme.name}
                      luxuryTheme={luxuryTheme}
                      onClick={() => handleThemeSelect(luxuryTheme)}
                    />
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'seasonal' && (
              <div className="space-y-6">
                <h3 className="text-xl font-playfair font-bold text-white">Seasonal Collections</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {Object.entries(seasonalLuxuryThemes).map(([season, luxuryTheme]) => (
                    <div key={season} className="space-y-3">
                      <div className="flex items-center gap-2 text-luxury-gold">
                        {season === 'spring' && <Flower className="h-5 w-5" />}
                        {season === 'summer' && <Sun className="h-5 w-5" />}
                        {season === 'autumn' && <Leaf className="h-5 w-5" />}
                        {season === 'winter' && <Snowflake className="h-5 w-5" />}
                        <span className="font-montserrat font-semibold capitalize">{season}</span>
                      </div>
                      <ThemePreview
                        luxuryTheme={luxuryTheme}
                        onClick={() => handleThemeSelect(luxuryTheme)}
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'personalization' && (
              <div className="space-y-6">
                {isVIP ? (
                  <>
                    <h3 className="text-xl font-playfair font-bold text-white">VIP Personalization</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {Object.values(vipPersonalizationThemes).map((luxuryTheme) => (
                        <ThemePreview
                          key={luxuryTheme.name}
                          luxuryTheme={luxuryTheme}
                          onClick={() => handleThemeSelect(luxuryTheme)}
                        />
                      ))}
                    </div>
                  </>
                ) : (
                  <div className="text-center py-12">
                    <Crown className="h-16 w-16 text-luxury-gold/50 mx-auto mb-4" />
                    <h3 className="text-xl font-playfair font-bold text-white mb-2">
                      VIP Access Required
                    </h3>
                    <p className="text-white/70 mb-6">
                      Unlock exclusive personalization themes with VIP membership
                    </p>
                    <LuxuryButton variant="gold" size="lg">
                      Upgrade to VIP
                    </LuxuryButton>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'effects' && (
              <div className="space-y-6">
                <h3 className="text-xl font-playfair font-bold text-white">Luxury Effects</h3>
                <div className="space-y-4">
                  {Object.entries(theme.effects).map(([effect, enabled]) => (
                    <div key={effect} className="flex items-center justify-between p-4 bg-white/5 rounded-xl">
                      <div>
                        <h4 className="font-montserrat font-semibold text-white capitalize">
                          {effect.replace(/([A-Z])/g, ' $1').trim()}
                        </h4>
                        <p className="text-white/70 text-sm">
                          {effect === 'glow' && 'Elegant glow effects on interactive elements'}
                          {effect === 'particles' && 'Floating particles and decorative elements'}
                          {effect === 'animations' && 'Smooth luxury animations and transitions'}
                          {effect === 'sounds' && 'Premium sound effects for interactions'}
                        </p>
                      </div>
                      <button
                        onClick={() => toggleEffect(effect as keyof typeof theme.effects)}
                        className={`w-12 h-6 rounded-full transition-colors ${
                          enabled ? 'bg-luxury-gold' : 'bg-gray-600'
                        }`}
                      >
                        <div
                          className={`w-5 h-5 bg-white rounded-full transition-transform ${
                            enabled ? 'translate-x-6' : 'translate-x-0.5'
                          }`}
                        />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-6 border-t border-luxury-gold/20">
            <div className="text-white/70 text-sm">
              Current: {theme.name} • Level: {theme.premiumLevel.toUpperCase()}
            </div>
            <div className="flex gap-3">
              <AccessibleLuxuryButton
                variant="secondary"
                onClick={onClose}
                ariaLabel="Close theme customizer"
              >
                Close
              </AccessibleLuxuryButton>
              <LuxuryButton variant="gold" onClick={onClose}>
                Apply Theme
              </LuxuryButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LuxuryThemeCustomizer;
