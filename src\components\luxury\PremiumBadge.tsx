import React from 'react';
import { Sparkles, Crown, Star, Gem, Award, Shield } from 'lucide-react';

interface PremiumBadgeProps {
  type: 'exclusive' | 'limited' | 'vip-only' | 'elite-access' | 'premium' | 'featured';
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
  glow?: boolean;
  className?: string;
}

const PremiumBadge: React.FC<PremiumBadgeProps> = ({
  type,
  size = 'md',
  animated = true,
  glow = true,
  className = ''
}) => {
  const getIcon = () => {
    const iconSize = size === 'sm' ? 'w-3 h-3' : size === 'lg' ? 'w-5 h-5' : 'w-4 h-4';
    
    switch (type) {
      case 'exclusive':
        return <Crown className={iconSize} />;
      case 'limited':
        return <Star className={iconSize} />;
      case 'vip-only':
        return <Gem className={iconSize} />;
      case 'elite-access':
        return <Shield className={iconSize} />;
      case 'premium':
        return <Award className={iconSize} />;
      case 'featured':
        return <Sparkles className={iconSize} />;
      default:
        return <Star className={iconSize} />;
    }
  };

  const getText = () => {
    switch (type) {
      case 'exclusive':
        return 'EXCLUSIVE';
      case 'limited':
        return 'LIMITED';
      case 'vip-only':
        return 'VIP ONLY';
      case 'elite-access':
        return 'ELITE ACCESS';
      case 'premium':
        return 'PREMIUM';
      case 'featured':
        return 'FEATURED';
      default:
        return 'SPECIAL';
    }
  };

  const getBadgeClass = () => {
    const sizeClass = size === 'sm' ? 'px-2 py-1 text-xs' : 
                     size === 'lg' ? 'px-4 py-2 text-sm' : 'px-3 py-1.5 text-xs';
    
    const animationClass = animated ? 'animate-luxury-sparkle' : '';
    const glowClass = glow ? 'shadow-glow-gold' : '';
    
    const baseClass = `inline-flex items-center gap-1 rounded-full font-bold ${sizeClass} ${animationClass} ${glowClass} ${className}`;
    
    switch (type) {
      case 'exclusive':
        return `${baseClass} bg-gradient-to-r from-luxury-gold to-luxury-purple text-white`;
      case 'limited':
        return `${baseClass} bg-gradient-to-r from-luxury-purple to-luxury-emerald text-white`;
      case 'vip-only':
        return `${baseClass} bg-gradient-to-r from-luxury-gold to-luxury-rose-gold text-luxury-navy`;
      case 'elite-access':
        return `${baseClass} bg-gradient-to-r from-luxury-platinum to-luxury-gold text-luxury-navy`;
      case 'premium':
        return `${baseClass} bg-gradient-to-r from-luxury-rose-gold to-luxury-purple text-white`;
      case 'featured':
        return `${baseClass} bg-gradient-to-r from-luxury-emerald to-luxury-gold text-luxury-navy`;
      default:
        return `${baseClass} bg-gradient-to-r from-gray-400 to-gray-600 text-white`;
    }
  };

  return (
    <div className={getBadgeClass()}>
      {getIcon()}
      <span>{getText()}</span>
    </div>
  );
};

export default PremiumBadge;
