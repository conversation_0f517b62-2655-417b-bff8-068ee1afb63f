import React, { useRef, useState } from 'react';
import { Crown, Star, Phone, Mail, MapPin, Clock, Calendar, Sparkles, Send, Shield } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import LuxuryButton from './LuxuryButton';
import VIPIndicator from './VIPIndicator';
import { useLuxuryTheme } from '@/contexts/LuxuryThemeContext';
import { useLuxuryIntersectionObserver } from '@/utils/luxuryAnimations';

interface ContactMethod {
  id: string;
  type: 'phone' | 'email' | 'location' | 'hours';
  title: string;
  value: string;
  description: string;
  icon: React.ReactNode;
  priority: 'standard' | 'vip' | 'elite';
  available24h?: boolean;
}

const PremiumContact: React.FC = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    childAge: '',
    program: '',
    consultationType: '',
    message: '',
    vipMember: false
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { theme, isVIP, isElite } = useLuxuryTheme();

  useLuxuryIntersectionObserver(sectionRef, 'animate-luxury-fade-in');

  const contactMethods: ContactMethod[] = [
    {
      id: '1',
      type: 'phone',
      title: 'Elite Concierge',
      value: '+1 (555) 123-ELITE',
      description: 'Priority line for VIP families',
      icon: <Phone className="h-6 w-6" />,
      priority: 'elite',
      available24h: true
    },
    {
      id: '2',
      type: 'email',
      title: 'VIP Admissions',
      value: '<EMAIL>',
      description: 'Exclusive admissions support',
      icon: <Mail className="h-6 w-6" />,
      priority: 'vip'
    },
    {
      id: '3',
      type: 'location',
      title: 'Elite Campus',
      value: '123 Luxury Lane, Beverly Hills',
      description: 'Private tours by appointment',
      icon: <MapPin className="h-6 w-6" />,
      priority: 'standard'
    },
    {
      id: '4',
      type: 'hours',
      title: 'VIP Hours',
      value: 'Mon-Fri: 7AM-7PM',
      description: 'Extended hours for elite families',
      icon: <Clock className="h-6 w-6" />,
      priority: 'vip'
    }
  ];

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    // Handle success/error states here
  };

  const getContactMethodClass = (priority: string) => {
    const baseClass = 'luxury-card p-6 rounded-2xl luxury-hover-lift group relative overflow-hidden';
    
    switch (priority) {
      case 'elite':
        return `${baseClass} border-luxury-gold/30 hover:border-luxury-gold/60`;
      case 'vip':
        return `${baseClass} border-luxury-purple/30 hover:border-luxury-purple/60`;
      default:
        return `${baseClass} border-luxury-platinum/30 hover:border-luxury-platinum/60`;
    }
  };

  const getIconColor = (priority: string) => {
    switch (priority) {
      case 'elite':
        return 'text-luxury-gold bg-luxury-gold/20';
      case 'vip':
        return 'text-luxury-purple bg-luxury-purple/20';
      default:
        return 'text-luxury-platinum bg-luxury-platinum/20';
    }
  };

  return (
    <section 
      ref={sectionRef}
      className="luxury-section relative"
      style={{
        background: 'linear-gradient(135deg, #1B1B3A 0%, #0F0F23 50%, #2D1B69 100%)'
      }}
    >
      <div className="luxury-container">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-playfair font-bold luxury-heading mb-6">
            VIP Contact & Consultation
          </h2>
          <p className="text-xl text-white/90 font-montserrat max-w-4xl mx-auto leading-relaxed">
            Experience our white-glove service with priority access to our elite admissions team and exclusive consultation opportunities.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          {/* Contact Methods */}
          <div className="space-y-8">
            <h3 className="text-3xl font-playfair font-bold text-white mb-8">
              Elite Contact Methods
            </h3>
            
            <div className="space-y-6">
              {contactMethods.map((method, index) => (
                <div 
                  key={method.id}
                  className={getContactMethodClass(method.priority)}
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  {/* Priority Badge */}
                  {method.priority !== 'standard' && (
                    <div className="absolute top-4 right-4">
                      <VIPIndicator 
                        level={method.priority as any} 
                        size="sm" 
                        animated={theme.effects.animations} 
                      />
                    </div>
                  )}

                  {/* 24/7 Indicator */}
                  {method.available24h && (
                    <div className="absolute top-4 left-4">
                      <div className="px-2 py-1 bg-luxury-emerald/20 rounded-full text-luxury-emerald text-xs font-bold">
                        24/7
                      </div>
                    </div>
                  )}

                  <div className="flex items-start gap-4">
                    <div className={`p-3 rounded-xl ${getIconColor(method.priority)} group-hover:scale-110 transition-transform duration-300`}>
                      {method.icon}
                    </div>
                    
                    <div className="flex-1">
                      <h4 className="text-xl font-playfair font-bold text-white mb-2 group-hover:text-luxury-gold transition-colors">
                        {method.title}
                      </h4>
                      <p className="text-luxury-gold font-montserrat font-semibold mb-1">
                        {method.value}
                      </p>
                      <p className="text-white/70 font-montserrat text-sm">
                        {method.description}
                      </p>
                    </div>
                  </div>

                  {/* Sparkle Effects */}
                  {theme.effects.animations && method.priority === 'elite' && (
                    <Sparkles className="absolute bottom-4 right-4 h-3 w-3 text-luxury-gold/50 animate-luxury-sparkle" />
                  )}
                </div>
              ))}
            </div>

            {/* VIP Booking CTA */}
            <div className="luxury-card p-8 rounded-3xl text-center">
              <Crown className="h-12 w-12 text-luxury-gold mx-auto mb-4" />
              <h4 className="text-2xl font-playfair font-bold text-white mb-4">
                Schedule VIP Campus Tour
              </h4>
              <p className="text-white/80 font-montserrat mb-6">
                Experience our luxury facilities with a private, guided tour tailored to your family's needs.
              </p>
              <LuxuryButton
                variant="gold"
                size="lg"
                glow={true}
                sparkles={true}
                soundEffect={theme.effects.sounds}
              >
                <Calendar className="h-5 w-5" />
                Book Private Tour
                <Star className="h-4 w-4" />
              </LuxuryButton>
            </div>
          </div>

          {/* Premium Contact Form */}
          <div className="luxury-card p-8 rounded-3xl">
            <div className="flex items-center gap-3 mb-8">
              <Shield className="h-8 w-8 text-luxury-gold" />
              <div>
                <h3 className="text-2xl font-playfair font-bold text-white">
                  Elite Consultation Request
                </h3>
                <p className="text-luxury-gold font-montserrat text-sm">
                  Secure & Confidential
                </p>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Personal Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-white font-montserrat font-semibold mb-2">
                    Full Name *
                  </label>
                  <Input
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="bg-white/10 border-luxury-gold/30 text-white placeholder:text-white/50 focus:border-luxury-gold"
                    placeholder="Your full name"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-white font-montserrat font-semibold mb-2">
                    Email Address *
                  </label>
                  <Input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="bg-white/10 border-luxury-gold/30 text-white placeholder:text-white/50 focus:border-luxury-gold"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-white font-montserrat font-semibold mb-2">
                    Phone Number
                  </label>
                  <Input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    className="bg-white/10 border-luxury-gold/30 text-white placeholder:text-white/50 focus:border-luxury-gold"
                    placeholder="+****************"
                  />
                </div>
                
                <div>
                  <label className="block text-white font-montserrat font-semibold mb-2">
                    Child's Age
                  </label>
                  <Select onValueChange={(value) => handleInputChange('childAge', value)}>
                    <SelectTrigger className="bg-white/10 border-luxury-gold/30 text-white">
                      <SelectValue placeholder="Select age range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="6-18months">6-18 months</SelectItem>
                      <SelectItem value="18-36months">18-36 months</SelectItem>
                      <SelectItem value="3-4years">3-4 years</SelectItem>
                      <SelectItem value="4-5years">4-5 years</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-white font-montserrat font-semibold mb-2">
                    Program Interest
                  </label>
                  <Select onValueChange={(value) => handleInputChange('program', value)}>
                    <SelectTrigger className="bg-white/10 border-luxury-gold/30 text-white">
                      <SelectValue placeholder="Select program" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="royal-infants">Royal Infants</SelectItem>
                      <SelectItem value="elite-toddlers">Elite Toddlers</SelectItem>
                      <SelectItem value="platinum-preschool">Platinum Preschool</SelectItem>
                      <SelectItem value="diamond-prek">Diamond Pre-K</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <label className="block text-white font-montserrat font-semibold mb-2">
                    Consultation Type
                  </label>
                  <Select onValueChange={(value) => handleInputChange('consultationType', value)}>
                    <SelectTrigger className="bg-white/10 border-luxury-gold/30 text-white">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="virtual">Virtual Consultation</SelectItem>
                      <SelectItem value="campus-tour">Campus Tour</SelectItem>
                      <SelectItem value="vip-meeting">VIP Private Meeting</SelectItem>
                      <SelectItem value="elite-assessment">Elite Assessment</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <label className="block text-white font-montserrat font-semibold mb-2">
                  Message
                </label>
                <Textarea
                  value={formData.message}
                  onChange={(e) => handleInputChange('message', e.target.value)}
                  className="bg-white/10 border-luxury-gold/30 text-white placeholder:text-white/50 focus:border-luxury-gold min-h-[120px]"
                  placeholder="Tell us about your family's educational goals and any specific questions you have..."
                />
              </div>

              {/* VIP Member Checkbox */}
              {(isVIP || isElite) && (
                <div className="flex items-center gap-3 p-4 bg-luxury-gold/10 rounded-xl">
                  <input
                    type="checkbox"
                    id="vipMember"
                    checked={formData.vipMember}
                    onChange={(e) => handleInputChange('vipMember', e.target.checked.toString())}
                    className="w-4 h-4 text-luxury-gold"
                  />
                  <label htmlFor="vipMember" className="text-luxury-gold font-montserrat font-semibold">
                    I am a current VIP member seeking priority assistance
                  </label>
                </div>
              )}

              {/* Submit Button */}
              <LuxuryButton
                type="submit"
                variant="gold"
                size="lg"
                glow={true}
                sparkles={true}
                soundEffect={theme.effects.sounds}
                disabled={isSubmitting}
                className="w-full justify-center"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-luxury-navy" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Send className="h-5 w-5" />
                    Send Elite Consultation Request
                    <Crown className="h-4 w-4" />
                  </>
                )}
              </LuxuryButton>
            </form>

            {/* Security Notice */}
            <div className="mt-6 p-4 bg-luxury-emerald/10 rounded-xl">
              <div className="flex items-center gap-2 text-luxury-emerald text-sm font-montserrat">
                <Shield className="h-4 w-4" />
                <span>Your information is protected with bank-level security and will never be shared.</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Background Decorative Elements */}
      {theme.effects.animations && (
        <>
          <Crown className="absolute top-20 right-20 h-10 w-10 text-luxury-gold/10 animate-luxury-float" />
          <Star className="absolute bottom-20 left-20 h-8 w-8 text-luxury-platinum/10 animate-luxury-float" style={{ animationDelay: '2s' }} />
        </>
      )}
    </section>
  );
};

export default PremiumContact;
