import React from 'react';
import { Crown, Star, Gem } from 'lucide-react';
import { useLuxuryTheme } from '@/contexts/LuxuryThemeContext';

interface VIPIndicatorProps {
  level?: 'standard' | 'premium' | 'vip' | 'elite';
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  animated?: boolean;
  className?: string;
}

const VIPIndicator: React.FC<VIPIndicatorProps> = ({
  level,
  size = 'md',
  showText = true,
  animated = true,
  className = ''
}) => {
  const { theme, isVIP, isElite } = useLuxuryTheme();
  const currentLevel = level || theme.premiumLevel;

  const getIcon = () => {
    switch (currentLevel) {
      case 'elite':
        return <Crown className={`${getSizeClass()} text-luxury-gold`} />;
      case 'vip':
        return <Star className={`${getSizeClass()} text-luxury-gold`} />;
      case 'premium':
        return <Gem className={`${getSizeClass()} text-luxury-platinum`} />;
      default:
        return null;
    }
  };

  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'w-3 h-3';
      case 'lg':
        return 'w-6 h-6';
      default:
        return 'w-4 h-4';
    }
  };

  const getBadgeClass = () => {
    const baseClass = `inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-bold ${className}`;
    const animationClass = animated ? 'animate-luxury-glow' : '';
    
    switch (currentLevel) {
      case 'elite':
        return `${baseClass} elite-badge ${animationClass}`;
      case 'vip':
        return `${baseClass} vip-badge ${animationClass}`;
      case 'premium':
        return `${baseClass} premium-badge ${animationClass}`;
      default:
        return `${baseClass} bg-gray-200 text-gray-700`;
    }
  };

  const getText = () => {
    switch (currentLevel) {
      case 'elite':
        return 'ELITE';
      case 'vip':
        return 'VIP';
      case 'premium':
        return 'PREMIUM';
      default:
        return 'MEMBER';
    }
  };

  if (currentLevel === 'standard') {
    return null;
  }

  return (
    <div className={getBadgeClass()}>
      {getIcon()}
      {showText && <span>{getText()}</span>}
    </div>
  );
};

export default VIPIndicator;
