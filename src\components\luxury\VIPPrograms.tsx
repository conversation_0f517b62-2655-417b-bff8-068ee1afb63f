import React, { useRef, useState } from 'react';
import { Crown, Star, Gem, Baby, Users, GraduationCap, Award, Sparkles, Check } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import PremiumBadge from './PremiumBadge';
import LuxuryButton from './LuxuryButton';
import { useLuxuryTheme } from '@/contexts/LuxuryThemeContext';
import { useLuxuryIntersectionObserver, useLuxuryHover } from '@/utils/luxuryAnimations';

interface VIPProgram {
  id: string;
  title: string;
  ageRange: string;
  description: string;
  features: string[];
  vipFeatures: string[];
  pricing: {
    standard: number;
    vip: number;
    elite: number;
  };
  icon: React.ReactNode;
  color: 'gold' | 'platinum' | 'purple' | 'emerald' | 'rose-gold';
  badge: 'exclusive' | 'limited' | 'vip-only' | 'elite-access' | 'premium' | 'featured';
  capacity: string;
  waitlist?: boolean;
}

const VIPPrograms: React.FC = () => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const [selectedProgram, setSelectedProgram] = useState<string | null>(null);
  const { theme, isVIP, isElite } = useLuxuryTheme();

  useLuxuryIntersectionObserver(sectionRef, 'animate-luxury-fade-in');

  const programs: VIPProgram[] = [
    {
      id: '1',
      title: 'Royal Infants',
      ageRange: '6-18 months',
      description: 'Ultra-premium infant care in an exclusive environment with 2:1 caregiver ratio and luxury amenities.',
      features: ['Personalized care plans', 'Luxury nursery suites', 'Organic gourmet meals', 'Daily development reports'],
      vipFeatures: ['Private nanny service', '24/7 pediatric support', 'Custom sleep programs', 'Elite milestone tracking'],
      pricing: { standard: 2500, vip: 4000, elite: 6500 },
      icon: <Baby className="h-8 w-8" />,
      color: 'rose-gold',
      badge: 'exclusive',
      capacity: 'Limited to 8 infants',
      waitlist: true
    },
    {
      id: '2',
      title: 'Elite Toddlers',
      ageRange: '18-36 months',
      description: 'Sophisticated toddler program combining luxury care with advanced early learning methodologies.',
      features: ['Montessori-inspired curriculum', 'Bilingual immersion', 'Creative arts studio', 'Outdoor exploration'],
      vipFeatures: ['Personal learning coach', 'Private music lessons', 'Gourmet cooking classes', 'Elite social skills'],
      pricing: { standard: 2200, vip: 3500, elite: 5500 },
      icon: <Users className="h-8 w-8" />,
      color: 'emerald',
      badge: 'vip-only',
      capacity: 'Maximum 12 children'
    },
    {
      id: '3',
      title: 'Platinum Preschool',
      ageRange: '3-4 years',
      description: 'Premium preschool experience featuring advanced academics and luxury enrichment programs.',
      features: ['Advanced literacy program', 'STEM exploration', 'Fine arts curriculum', 'Character development'],
      vipFeatures: ['Private tutoring', 'Elite field trips', 'Foreign language mastery', 'Leadership training'],
      pricing: { standard: 2000, vip: 3200, elite: 5000 },
      icon: <Star className="h-8 w-8" />,
      color: 'platinum',
      badge: 'premium',
      capacity: 'Exclusive 15 students'
    },
    {
      id: '4',
      title: 'Diamond Pre-K',
      ageRange: '4-5 years',
      description: 'The ultimate kindergarten preparation program for elite families seeking academic excellence.',
      features: ['Advanced mathematics', 'Reading mastery', 'Scientific inquiry', 'Social leadership'],
      vipFeatures: ['Private school prep', 'Elite testing prep', 'Scholarship guidance', 'Alumni network access'],
      pricing: { standard: 1800, vip: 3000, elite: 4800 },
      icon: <GraduationCap className="h-8 w-8" />,
      color: 'gold',
      badge: 'elite-access',
      capacity: 'Select 18 students'
    }
  ];

  const ProgramCard: React.FC<{ program: VIPProgram; index: number }> = ({ program, index }) => {
    const cardRef = useRef<HTMLDivElement>(null);
    useLuxuryHover(cardRef);

    const getCardClass = () => {
      const baseClass = 'luxury-card overflow-hidden luxury-hover-lift transition-all duration-500 group relative';
      const colorClasses = {
        gold: 'border-luxury-gold/30 hover:border-luxury-gold/60',
        platinum: 'border-luxury-platinum/30 hover:border-luxury-platinum/60',
        purple: 'border-luxury-purple/30 hover:border-luxury-purple/60',
        emerald: 'border-luxury-emerald/30 hover:border-luxury-emerald/60',
        'rose-gold': 'border-luxury-rose-gold/30 hover:border-luxury-rose-gold/60'
      };
      return `${baseClass} ${colorClasses[program.color]}`;
    };

    const getAccentColor = () => {
      const colors = {
        gold: 'bg-luxury-gold',
        platinum: 'bg-luxury-platinum',
        purple: 'bg-luxury-purple',
        emerald: 'bg-luxury-emerald',
        'rose-gold': 'bg-luxury-rose-gold'
      };
      return colors[program.color];
    };

    const getPricing = () => {
      if (isElite) return program.pricing.elite;
      if (isVIP) return program.pricing.vip;
      return program.pricing.standard;
    };

    const getPricingLevel = () => {
      if (isElite) return 'Elite';
      if (isVIP) return 'VIP';
      return 'Standard';
    };

    return (
      <Card 
        ref={cardRef}
        className={getCardClass()}
        style={{ animationDelay: `${index * 0.15}s` }}
      >
        {/* Accent Bar */}
        <div className={`h-3 ${getAccentColor()}`} />
        
        {/* Premium Badge */}
        <div className="absolute top-6 right-6 z-10">
          <PremiumBadge type={program.badge} size="sm" animated={theme.effects.animations} />
        </div>

        {/* Waitlist Indicator */}
        {program.waitlist && (
          <div className="absolute top-6 left-6 z-10">
            <div className="px-3 py-1 bg-luxury-gold/20 rounded-full text-luxury-gold text-xs font-bold">
              WAITLIST ONLY
            </div>
          </div>
        )}

        <CardHeader className="pb-4">
          <div className="flex items-center gap-4 mb-4">
            <div className={`p-3 rounded-2xl ${getAccentColor()}/20 group-hover:scale-110 transition-transform duration-300`}>
              <div className={`${program.color === 'gold' || program.color === 'rose-gold' ? 'text-luxury-navy' : 'text-white'}`}>
                {program.icon}
              </div>
            </div>
            <div>
              <CardTitle className="text-2xl font-playfair text-white group-hover:text-luxury-gold transition-colors">
                {program.title}
              </CardTitle>
              <CardDescription className="text-luxury-gold font-montserrat font-semibold">
                {program.ageRange}
              </CardDescription>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Description */}
          <p className="text-white/90 font-montserrat leading-relaxed">
            {program.description}
          </p>

          {/* Capacity */}
          <div className="flex items-center gap-2 text-luxury-gold text-sm font-semibold">
            <Crown className="h-4 w-4" />
            {program.capacity}
          </div>

          {/* Features */}
          <div className="space-y-3">
            <h4 className="font-montserrat font-bold text-white">Included Features:</h4>
            <ul className="space-y-2">
              {program.features.map((feature, idx) => (
                <li key={idx} className="flex items-start gap-2 text-white/80 text-sm">
                  <Check className="h-4 w-4 text-luxury-emerald mt-0.5 flex-shrink-0" />
                  <span>{feature}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* VIP Features */}
          {(isVIP || isElite) && (
            <div className="space-y-3 border-t border-luxury-gold/20 pt-4">
              <h4 className="font-montserrat font-bold text-luxury-gold">VIP Exclusive Features:</h4>
              <ul className="space-y-2">
                {program.vipFeatures.map((feature, idx) => (
                  <li key={idx} className="flex items-start gap-2 text-luxury-gold/90 text-sm">
                    <Star className="h-4 w-4 text-luxury-gold mt-0.5 flex-shrink-0" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Pricing */}
          <div className="border-t border-white/10 pt-4">
            <div className="flex items-center justify-between mb-4">
              <span className="font-montserrat font-semibold text-white">
                {getPricingLevel()} Monthly Rate:
              </span>
              <span className="text-2xl font-playfair font-bold text-luxury-gold">
                ${getPricing().toLocaleString()}
              </span>
            </div>

            {/* Enrollment Button */}
            <LuxuryButton
              variant={program.color}
              size="md"
              glow={true}
              sparkles={true}
              soundEffect={theme.effects.sounds}
              className="w-full justify-center"
              disabled={program.waitlist && !isElite}
            >
              {program.waitlist && !isElite ? (
                <>
                  <Crown className="h-4 w-4" />
                  Join Waitlist
                </>
              ) : (
                <>
                  <Gem className="h-4 w-4" />
                  Enroll Now
                </>
              )}
            </LuxuryButton>
          </div>
        </CardContent>

        {/* Sparkle Effects */}
        {theme.effects.animations && (
          <>
            <Sparkles className="absolute top-4 left-4 h-3 w-3 text-luxury-gold/30 animate-luxury-sparkle" />
            <Sparkles className="absolute bottom-4 right-4 h-3 w-3 text-luxury-gold/30 animate-luxury-sparkle" style={{ animationDelay: '1.5s' }} />
          </>
        )}

        {/* Shimmer Effect */}
        <div className="absolute inset-0 bg-shimmer opacity-0 group-hover:opacity-10 transition-opacity duration-500" />
      </Card>
    );
  };

  return (
    <section 
      ref={sectionRef}
      id="programs" 
      className="luxury-section bg-white"
    >
      <div className="luxury-container">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-playfair font-bold text-luxury-navy mb-6">
            VIP Elite Programs
          </h2>
          <p className="text-xl text-gray-700 font-montserrat max-w-4xl mx-auto leading-relaxed">
            Exclusive educational experiences designed for the most discerning families, featuring luxury amenities and world-class instruction.
          </p>
        </div>
        
        {/* Programs Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-8">
          {programs.map((program, index) => (
            <ProgramCard key={program.id} program={program} index={index} />
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="inline-flex items-center gap-3 px-8 py-4 bg-luxury-gold/10 rounded-2xl">
            <Award className="h-6 w-6 text-luxury-gold" />
            <span className="font-montserrat font-bold text-luxury-navy">
              Schedule Your Private Tour Today
            </span>
            <Sparkles className="h-5 w-5 text-luxury-gold animate-luxury-sparkle" />
          </div>
        </div>
      </div>
    </section>
  );
};

export default VIPPrograms;
