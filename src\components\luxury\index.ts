// Luxury Components Export Index
export { default as LuxuryHeader } from './LuxuryHeader';
export { default as LuxuryHero } from './LuxuryHero';
export { default as ExclusiveFeatures } from './ExclusiveFeatures';
export { default as VIPPrograms } from './VIPPrograms';
export { default as LuxuryTestimonials } from './LuxuryTestimonials';
export { default as EliteAbout } from './EliteAbout';
export { default as PremiumContact } from './PremiumContact';
export { default as LuxuryFooter } from './LuxuryFooter';
export { default as LuxuryButton } from './LuxuryButton';
export { default as VIPIndicator } from './VIPIndicator';
export { default as PremiumBadge } from './PremiumBadge';
export { default as LuxuryLoader } from './LuxuryLoader';
export { default as LuxuryThemeCustomizer } from './LuxuryThemeCustomizer';

// Advanced Components
export { LuxuryInteractiveElement, LuxuryFormField } from './LuxuryMicroInteractions';
export {
  LuxuryResponsiveContainer,
  LuxuryResponsiveText,
  LuxuryMobileNav,
  LuxuryGrid,
  useLuxuryBreakpoint,
  useLuxuryTouchInteractions,
  getLuxurySpacing
} from './LuxuryResponsiveSystem';
export {
  AccessibleLuxuryButton,
  AccessibleLuxuryNav,
  AccessibleLuxuryFormField,
  LuxurySkipLink,
  useLuxuryAccessibility,
  useLuxuryScreenReader
} from './LuxuryAccessibility';

// Context and Utilities
export { LuxuryThemeProvider, useLuxuryTheme, luxuryThemes } from '@/contexts/LuxuryThemeContext';
export * from '@/utils/luxuryAnimations';
export * from '@/utils/luxuryPerformance';
