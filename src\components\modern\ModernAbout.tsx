import React, { useRef } from 'react';
import { Button } from '@/components/ui/button';
import { CheckCircle, ArrowRight } from 'lucide-react';
import { 
  useModernFadeIn, 
  useModernParallax,
  useModernFloat 
} from '@/utils/modernGSAPAnimations';
import { cn } from '@/lib/utils';

interface ModernAboutProps {
  title?: string;
  subtitle?: string;
  description?: string;
  highlights?: string[];
  ctaText?: string;
  onCTAClick?: () => void;
  className?: string;
}

const defaultHighlights = [
  "Personalized learning paths for every student",
  "Real-time progress tracking and analytics",
  "Interactive content and multimedia resources",
  "24/7 support from qualified educators",
  "Collaborative learning environment",
  "Mobile-friendly platform for learning anywhere"
];

const ModernAbout: React.FC<ModernAboutProps> = ({
  title = "Transforming Education for the Digital Age",
  subtitle = "About Promise Academy",
  description = "We believe that every student deserves access to quality education that adapts to their unique learning style. Our platform combines cutting-edge technology with proven pedagogical methods to create an engaging and effective learning experience.",
  highlights = defaultHighlights,
  ctaText = "Learn More About Us",
  onCTAClick,
  className
}) => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const visualRef = useRef<HTMLDivElement>(null);
  const shape1Ref = useRef<HTMLDivElement>(null);
  const shape2Ref = useRef<HTMLDivElement>(null);
  const shape3Ref = useRef<HTMLDivElement>(null);

  // Animate content sections
  useModernFadeIn(contentRef, { direction: 'left', delay: 0.2 });
  useModernFadeIn(visualRef, { direction: 'right', delay: 0.4 });

  // Parallax effects for visual elements
  useModernParallax(shape1Ref, { yPercent: -20, speed: 0.5 });
  useModernParallax(shape2Ref, { yPercent: 15, speed: 0.3 });

  // Floating animations
  useModernFloat(shape3Ref, { amplitude: 15, duration: 8, delay: 2 });

  const handleCTAClick = () => {
    if (onCTAClick) {
      onCTAClick();
    } else {
      // Default behavior - scroll to contact or navigate
      const contactSection = document.getElementById('contact');
      if (contactSection) {
        contactSection.scrollIntoView({ behavior: 'smooth' });
      }
    }
  };

  return (
    <section
      ref={sectionRef}
      id="about"
      className={cn(
        "py-grid-16 lg:py-grid-24 bg-white relative overflow-hidden",
        className
      )}
    >
      <div className="container mx-auto px-grid-4 lg:px-grid-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-grid-12 lg:gap-grid-16 items-center">
          {/* Content */}
          <div
            ref={contentRef}
            className="opacity-0"
          >
            <p className="font-body text-modern-purple font-medium text-lg mb-grid-3">
              {subtitle}
            </p>
            
            <h2 className="font-heading font-bold text-3xl md:text-4xl lg:text-5xl text-modern-neutral-900 mb-grid-6 leading-tight">
              {title}
            </h2>
            
            <p className="font-body text-modern-neutral-600 text-lg mb-grid-8 leading-relaxed">
              {description}
            </p>

            {/* Highlights */}
            <div className="space-y-grid-3 mb-grid-8">
              {highlights.map((highlight, index) => (
                <div
                  key={index}
                  className="flex items-start space-x-grid-3 group"
                >
                  <CheckCircle className="w-6 h-6 text-modern-gold mt-0.5 flex-shrink-0 transition-transform group-hover:scale-110" />
                  <span className="font-body text-modern-neutral-700 leading-relaxed">
                    {highlight}
                  </span>
                </div>
              ))}
            </div>

            {/* CTA Button */}
            <Button
              variant="secondary"
              size="lg"
              onClick={handleCTAClick}
              className="group"
            >
              {ctaText}
              <ArrowRight className="ml-2 w-5 h-5 transition-transform group-hover:translate-x-1" />
            </Button>
          </div>

          {/* Visual Section */}
          <div
            ref={visualRef}
            className="relative opacity-0"
          >
            {/* Main Visual Container */}
            <div className="relative bg-gradient-to-br from-modern-purple/5 to-modern-gold/5 rounded-3xl p-grid-8 lg:p-grid-10">
              {/* Floating Background Shapes */}
              <div
                ref={shape1Ref}
                className="absolute top-grid-4 right-grid-4 w-24 h-24 bg-modern-purple/20 rounded-full blur-xl"
              />
              <div
                ref={shape2Ref}
                className="absolute bottom-grid-6 left-grid-6 w-32 h-32 bg-modern-gold/20 rounded-full blur-2xl"
              />
              <div
                ref={shape3Ref}
                className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20 bg-modern-red/20 rounded-full blur-lg"
              />

              {/* Stats Grid */}
              <div className="relative z-10 grid grid-cols-2 gap-grid-6">
                <div className="bg-white rounded-2xl p-grid-6 shadow-modern text-center">
                  <div className="font-heading font-bold text-3xl text-modern-purple mb-2">
                    10K+
                  </div>
                  <div className="font-body text-modern-neutral-600 text-sm">
                    Happy Students
                  </div>
                </div>
                
                <div className="bg-white rounded-2xl p-grid-6 shadow-modern text-center">
                  <div className="font-heading font-bold text-3xl text-modern-gold mb-2">
                    98%
                  </div>
                  <div className="font-body text-modern-neutral-600 text-sm">
                    Satisfaction Rate
                  </div>
                </div>
                
                <div className="bg-white rounded-2xl p-grid-6 shadow-modern text-center">
                  <div className="font-heading font-bold text-3xl text-modern-red mb-2">
                    5+
                  </div>
                  <div className="font-body text-modern-neutral-600 text-sm">
                    Years Experience
                  </div>
                </div>
                
                <div className="bg-white rounded-2xl p-grid-6 shadow-modern text-center">
                  <div className="font-heading font-bold text-3xl text-modern-purple mb-2">
                    24/7
                  </div>
                  <div className="font-body text-modern-neutral-600 text-sm">
                    Support Available
                  </div>
                </div>
              </div>

              {/* Decorative Elements */}
              <div className="absolute -top-4 -left-4 w-8 h-8 bg-modern-gold rounded-full opacity-60"></div>
              <div className="absolute -bottom-2 -right-2 w-6 h-6 bg-modern-red rounded-full opacity-60"></div>
              <div className="absolute top-1/3 -right-3 w-4 h-4 bg-modern-purple rounded-full opacity-60"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ModernAbout;
