import React, { useRef, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Mail, Phone, MapPin, Send } from 'lucide-react';
import { 
  useModernFadeIn, 
  useModernParallax 
} from '@/utils/modernGSAPAnimations';
import { cn } from '@/lib/utils';

interface ContactInfo {
  icon: React.ReactNode;
  title: string;
  value: string;
  color: 'purple' | 'gold' | 'red';
}

interface ModernContactProps {
  title?: string;
  subtitle?: string;
  description?: string;
  contactInfo?: ContactInfo[];
  className?: string;
}

const defaultContactInfo: ContactInfo[] = [
  {
    icon: <Mail className="w-6 h-6" />,
    title: "Email Us",
    value: "<EMAIL>",
    color: 'purple'
  },
  {
    icon: <Phone className="w-6 h-6" />,
    title: "Call Us",
    value: "+****************",
    color: 'gold'
  },
  {
    icon: <MapPin className="w-6 h-6" />,
    title: "Visit Us",
    value: "123 Education St, Learning City",
    color: 'red'
  }
];

const ModernContact: React.FC<ModernContactProps> = ({
  title = "Get in Touch",
  subtitle = "Contact Us",
  description = "Have questions about our platform? We'd love to hear from you. Send us a message and we'll respond as soon as possible.",
  contactInfo = defaultContactInfo,
  className
}) => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLDivElement>(null);
  const formRef = useRef<HTMLDivElement>(null);
  const infoRef = useRef<HTMLDivElement>(null);
  const backgroundRef = useRef<HTMLDivElement>(null);

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  // Animate sections
  useModernFadeIn(titleRef, { direction: 'up', delay: 0.2 });
  useModernFadeIn(formRef, { direction: 'left', delay: 0.4 });
  useModernFadeIn(infoRef, { direction: 'right', delay: 0.6 });
  useModernParallax(backgroundRef, { yPercent: -10, speed: 0.3 });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // Simulate form submission
      await new Promise(resolve => setTimeout(resolve, 2000));
      setSubmitStatus('success');
      setFormData({ name: '', email: '', subject: '', message: '' });
    } catch (error) {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
      setTimeout(() => setSubmitStatus('idle'), 3000);
    }
  };

  const getColorClasses = (color: 'purple' | 'gold' | 'red') => {
    switch (color) {
      case 'purple':
        return 'text-modern-purple bg-modern-purple/10';
      case 'gold':
        return 'text-modern-gold bg-modern-gold/10';
      case 'red':
        return 'text-modern-red bg-modern-red/10';
    }
  };

  return (
    <section
      ref={sectionRef}
      id="contact"
      className={cn(
        "py-grid-16 lg:py-grid-24 bg-modern-neutral-50 relative overflow-hidden",
        className
      )}
    >
      {/* Background Gradient */}
      <div
        ref={backgroundRef}
        className="absolute inset-0 bg-modern-card opacity-50"
      />

      <div className="container mx-auto px-grid-4 lg:px-grid-6 relative z-10">
        {/* Section Header */}
        <div
          ref={titleRef}
          className="text-center mb-grid-12 lg:mb-grid-16 opacity-0"
        >
          <p className="font-body text-modern-purple font-medium text-lg mb-grid-3">
            {subtitle}
          </p>
          <h2 className="font-heading font-bold text-3xl md:text-4xl lg:text-5xl text-modern-neutral-900 mb-grid-4">
            {title}
          </h2>
          <p className="font-body text-modern-neutral-600 text-lg max-w-2xl mx-auto leading-relaxed">
            {description}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-grid-12 lg:gap-grid-16">
          {/* Contact Form */}
          <div
            ref={formRef}
            className="opacity-0"
          >
            <Card variant="elevated" className="p-grid-2">
              <CardHeader>
                <CardTitle className="text-2xl">Send us a Message</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-grid-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-grid-4">
                    <Input
                      label="Your Name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                    />
                    <Input
                      label="Email Address"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <Input
                    label="Subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    required
                  />
                  
                  <div className="relative">
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      placeholder="Your message..."
                      rows={6}
                      required
                      className="w-full rounded-lg border border-modern-neutral-300 bg-white font-body text-modern-neutral-900 px-grid-4 py-3 transition-all duration-300 focus:border-modern-purple focus:ring-2 focus:ring-modern-purple/20 focus:outline-none resize-none"
                    />
                  </div>

                  <Button
                    type="submit"
                    variant="primary"
                    size="lg"
                    loading={isSubmitting}
                    loadingText="Sending..."
                    className="w-full group"
                  >
                    Send Message
                    <Send className="ml-2 w-5 h-5 transition-transform group-hover:translate-x-1" />
                  </Button>

                  {submitStatus === 'success' && (
                    <p className="text-modern-gold font-body text-center">
                      Message sent successfully! We'll get back to you soon.
                    </p>
                  )}
                  
                  {submitStatus === 'error' && (
                    <p className="text-modern-red font-body text-center">
                      Something went wrong. Please try again.
                    </p>
                  )}
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Contact Information */}
          <div
            ref={infoRef}
            className="opacity-0 space-y-grid-6"
          >
            <div>
              <h3 className="font-heading font-bold text-2xl text-modern-neutral-900 mb-grid-4">
                Other Ways to Reach Us
              </h3>
              <p className="font-body text-modern-neutral-600 leading-relaxed">
                Prefer to contact us directly? Here are all the ways you can reach our team.
              </p>
            </div>

            <div className="space-y-grid-4">
              {contactInfo.map((info, index) => (
                <Card
                  key={index}
                  variant="default"
                  className="p-grid-4 hover:shadow-modern-md transition-all duration-300"
                >
                  <div className="flex items-center space-x-grid-4">
                    <div className={cn(
                      "w-12 h-12 rounded-xl flex items-center justify-center",
                      getColorClasses(info.color)
                    )}>
                      {info.icon}
                    </div>
                    <div>
                      <h4 className="font-heading font-semibold text-modern-neutral-900 mb-1">
                        {info.title}
                      </h4>
                      <p className="font-body text-modern-neutral-600">
                        {info.value}
                      </p>
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {/* Additional Info */}
            <Card variant="gradient" className="p-grid-6">
              <h4 className="font-heading font-semibold text-modern-neutral-900 mb-grid-3">
                Office Hours
              </h4>
              <div className="space-y-2 font-body text-modern-neutral-600">
                <p>Monday - Friday: 9:00 AM - 6:00 PM</p>
                <p>Saturday: 10:00 AM - 4:00 PM</p>
                <p>Sunday: Closed</p>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ModernContact;
