import React, { useRef } from 'react';
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { BookO<PERSON>, Users, Award, Zap, Shield, Globe } from 'lucide-react';
import {
  useModernFadeIn,
  useModernStagger,
  useModernHover
} from '@/utils/modernGSAPAnimations';
import { useIsMobile, useIsTablet, usePrefersReducedMotion } from '@/utils/modernResponsive';
import { cn } from '@/lib/utils';

interface Feature {
  icon: React.ReactNode;
  title: string;
  description: string;
  color: 'purple' | 'gold' | 'red';
}

interface ModernFeaturesProps {
  title?: string;
  subtitle?: string;
  features?: Feature[];
  className?: string;
}

const defaultFeatures: Feature[] = [
  {
    icon: <BookOpen className="w-8 h-8" />,
    title: "Interactive Learning",
    description: "Engage with dynamic content and interactive lessons designed to make learning enjoyable and effective.",
    color: 'purple'
  },
  {
    icon: <Users className="w-8 h-8" />,
    title: "Expert Teachers",
    description: "Learn from qualified educators who are passionate about helping students reach their full potential.",
    color: 'gold'
  },
  {
    icon: <Award className="w-8 h-8" />,
    title: "Proven Results",
    description: "Track your progress with detailed analytics and celebrate achievements with our recognition system.",
    color: 'red'
  },
  {
    icon: <Zap className="w-8 h-8" />,
    title: "Fast & Efficient",
    description: "Optimized learning paths that adapt to your pace and help you achieve your goals faster.",
    color: 'purple'
  },
  {
    icon: <Shield className="w-8 h-8" />,
    title: "Safe Environment",
    description: "A secure and supportive learning environment where students can explore and grow confidently.",
    color: 'gold'
  },
  {
    icon: <Globe className="w-8 h-8" />,
    title: "Global Community",
    description: "Connect with students and educators from around the world in our inclusive learning community.",
    color: 'red'
  }
];

const ModernFeatures: React.FC<ModernFeaturesProps> = ({
  title = "Why Choose Promise Academy",
  subtitle = "Discover what makes our platform special",
  features = defaultFeatures,
  className
}) => {
  const sectionRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLDivElement>(null);
  const gridRef = useRef<HTMLDivElement>(null);

  // Responsive hooks
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  const prefersReducedMotion = usePrefersReducedMotion();

  // Animate section elements (respect reduced motion)
  if (!prefersReducedMotion) {
    useModernFadeIn(titleRef, { direction: 'up', delay: 0.2 });
    useModernStagger(gridRef, {
      stagger: isMobile ? 0.1 : 0.15,
      direction: 'up',
      delay: 0.4,
      distance: isMobile ? 20 : 30
    });
  }

  const getColorClasses = (color: 'purple' | 'gold' | 'red') => {
    switch (color) {
      case 'purple':
        return {
          icon: 'text-modern-purple bg-modern-purple/10',
          border: 'hover:border-modern-purple/30',
          shadow: 'hover:shadow-modern-glow-purple'
        };
      case 'gold':
        return {
          icon: 'text-modern-gold bg-modern-gold/10',
          border: 'hover:border-modern-gold/30',
          shadow: 'hover:shadow-modern-glow-gold'
        };
      case 'red':
        return {
          icon: 'text-modern-red bg-modern-red/10',
          border: 'hover:border-modern-red/30',
          shadow: 'hover:shadow-modern-glow-red'
        };
    }
  };

  return (
    <section
      ref={sectionRef}
      id="features"
      className={cn(
        "py-grid-16 lg:py-grid-24 bg-modern-neutral-50",
        className
      )}
    >
      <div className="container mx-auto px-grid-4 lg:px-grid-6">
        {/* Section Header */}
        <div
          ref={titleRef}
          className="text-center mb-grid-12 lg:mb-grid-16 opacity-0"
        >
          <p className="font-body text-modern-purple font-medium text-lg mb-grid-3">
            {subtitle}
          </p>
          <h2 className="font-heading font-bold text-3xl md:text-4xl lg:text-5xl text-modern-neutral-900 mb-grid-4">
            {title}
          </h2>
          <p className="font-body text-modern-neutral-600 text-lg max-w-2xl mx-auto leading-relaxed">
            Experience the future of education with our innovative features designed to enhance learning outcomes.
          </p>
        </div>

        {/* Features Grid */}
        <div
          ref={gridRef}
          className={cn(
            "grid gap-grid-6",
            isMobile ? "grid-cols-1 gap-grid-4" :
            isTablet ? "grid-cols-2 gap-grid-6" :
            "grid-cols-3 gap-grid-8"
          )}
        >
          {features.map((feature, index) => {
            const colorClasses = getColorClasses(feature.color);
            
            return (
              <FeatureCard
                key={index}
                feature={feature}
                colorClasses={colorClasses}
                index={index}
              />
            );
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-grid-12 lg:mt-grid-16">
          <p className="font-body text-modern-neutral-600 mb-grid-4">
            Ready to experience the difference?
          </p>
          <button className="font-body text-modern-purple hover:text-modern-gold transition-colors duration-300 font-medium">
            Explore All Features →
          </button>
        </div>
      </div>
    </section>
  );
};

interface FeatureCardProps {
  feature: Feature;
  colorClasses: {
    icon: string;
    border: string;
    shadow: string;
  };
  index: number;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ feature, colorClasses, index }) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const isMobile = useIsMobile();
  const prefersReducedMotion = usePrefersReducedMotion();

  // Add hover animation to each card (disabled on mobile and for reduced motion)
  if (!isMobile && !prefersReducedMotion) {
    useModernHover(cardRef, { scale: 1.03, y: -8, shadow: true });
  }

  return (
    <Card
      ref={cardRef}
      variant="feature"
      className={cn(
        "group cursor-pointer transition-all duration-300 border-2 border-transparent",
        colorClasses.border,
        colorClasses.shadow
      )}
    >
      <CardHeader className="text-center">
        <div className={cn(
          "w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-grid-4 transition-all duration-300 group-hover:scale-110",
          colorClasses.icon
        )}>
          {feature.icon}
        </div>
        <CardTitle className="text-xl lg:text-2xl">
          {feature.title}
        </CardTitle>
      </CardHeader>
      <CardContent className="text-center">
        <CardDescription className="text-base leading-relaxed">
          {feature.description}
        </CardDescription>
      </CardContent>
    </Card>
  );
};

export default ModernFeatures;
