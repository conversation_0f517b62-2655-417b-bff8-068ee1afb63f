import React, { useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Mail, Phone, MapPin, Facebook, Twitter, Instagram, Linkedin } from 'lucide-react';
import { useModernFadeIn } from '@/utils/modernGSAPAnimations';
import { useIsMobile } from '@/utils/modernResponsive';
import { cn } from '@/lib/utils';

interface ModernFooterProps {
  className?: string;
}

const ModernFooter: React.FC<ModernFooterProps> = ({ className }) => {
  const footerRef = useRef<HTMLDivElement>(null);
  const isMobile = useIsMobile();

  // Animate footer on scroll
  useModernFadeIn(footerRef, { direction: 'up', delay: 0.2 });

  const currentYear = new Date().getFullYear();

  const socialLinks = [
    { icon: <Facebook className="w-5 h-5" />, href: '#', label: 'Facebook' },
    { icon: <Twitter className="w-5 h-5" />, href: '#', label: 'Twitter' },
    { icon: <Instagram className="w-5 h-5" />, href: '#', label: 'Instagram' },
    { icon: <Linkedin className="w-5 h-5" />, href: '#', label: 'LinkedIn' },
  ];

  const quickLinks = [
    { label: 'About Us', href: '#about' },
    { label: 'Features', href: '#features' },
    { label: 'Contact', href: '#contact' },
    { label: 'Privacy Policy', href: '#' },
    { label: 'Terms of Service', href: '#' },
    { label: 'Support', href: '#' },
  ];

  const contactInfo = [
    { icon: <Mail className="w-4 h-4" />, text: '<EMAIL>' },
    { icon: <Phone className="w-4 h-4" />, text: '+****************' },
    { icon: <MapPin className="w-4 h-4" />, text: '123 Education St, Learning City' },
  ];

  return (
    <footer
      ref={footerRef}
      className={cn(
        "bg-modern-neutral-900 text-modern-neutral-100 pt-grid-16 pb-grid-8 opacity-0",
        className
      )}
    >
      <div className="container mx-auto px-grid-4 lg:px-grid-6">
        {/* Main Footer Content */}
        <div className={cn(
          "grid gap-grid-8 mb-grid-12",
          isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
        )}>
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-grid-2 mb-grid-4">
              <div className="w-10 h-10 bg-modern-purple rounded-lg flex items-center justify-center">
                <span className="text-white font-heading font-bold text-xl">P</span>
              </div>
              <span className="font-heading font-bold text-xl text-white">
                Promise Academy
              </span>
            </div>
            <p className="font-body text-modern-neutral-300 mb-grid-6 max-w-md leading-relaxed">
              Transforming education through innovative technology and personalized learning experiences. 
              Join thousands of students on their journey to success.
            </p>
            
            {/* Newsletter Signup */}
            <div className="max-w-md">
              <h4 className="font-heading font-semibold text-white mb-grid-3">
                Stay Updated
              </h4>
              <div className="flex gap-grid-2">
                <Input
                  placeholder="Enter your email"
                  variant="glass"
                  className="flex-1"
                />
                <Button variant="primary" size="default">
                  Subscribe
                </Button>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-heading font-semibold text-white mb-grid-4">
              Quick Links
            </h4>
            <ul className="space-y-grid-2">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <a
                    href={link.href}
                    className="font-body text-modern-neutral-300 hover:text-modern-gold transition-colors duration-300"
                  >
                    {link.label}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="font-heading font-semibold text-white mb-grid-4">
              Contact Info
            </h4>
            <ul className="space-y-grid-3">
              {contactInfo.map((info, index) => (
                <li key={index} className="flex items-center space-x-grid-2">
                  <div className="text-modern-purple">
                    {info.icon}
                  </div>
                  <span className="font-body text-modern-neutral-300 text-sm">
                    {info.text}
                  </span>
                </li>
              ))}
            </ul>

            {/* Social Links */}
            <div className="mt-grid-6">
              <h5 className="font-heading font-medium text-white mb-grid-3">
                Follow Us
              </h5>
              <div className="flex space-x-grid-3">
                {socialLinks.map((social, index) => (
                  <a
                    key={index}
                    href={social.href}
                    aria-label={social.label}
                    className="w-10 h-10 bg-modern-neutral-800 rounded-lg flex items-center justify-center text-modern-neutral-400 hover:text-modern-purple hover:bg-modern-neutral-700 transition-all duration-300"
                  >
                    {social.icon}
                  </a>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className={cn(
          "pt-grid-6 border-t border-modern-neutral-800 flex items-center justify-between",
          isMobile ? "flex-col space-y-grid-3 text-center" : "flex-row"
        )}>
          <p className="font-body text-modern-neutral-400 text-sm">
            © {currentYear} Promise Academy. All rights reserved.
          </p>
          <div className={cn(
            "flex space-x-grid-4",
            isMobile ? "flex-col space-y-grid-2 space-x-0" : "flex-row"
          )}>
            <a
              href="#"
              className="font-body text-modern-neutral-400 hover:text-modern-gold transition-colors duration-300 text-sm"
            >
              Privacy Policy
            </a>
            <a
              href="#"
              className="font-body text-modern-neutral-400 hover:text-modern-gold transition-colors duration-300 text-sm"
            >
              Terms of Service
            </a>
            <a
              href="#"
              className="font-body text-modern-neutral-400 hover:text-modern-gold transition-colors duration-300 text-sm"
            >
              Cookie Policy
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default ModernFooter;
