import React, { useState, useEffect, useRef } from 'react';
import { Menu, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useModernSmoothScroll, useModernFadeIn } from '@/utils/modernGSAPAnimations';
import { useKeyboardNavigation, useFocusManagement } from '@/utils/modernAccessibility';
import { cn } from '@/lib/utils';

interface ModernHeaderProps {
  onAboutClick?: () => void;
  onFeaturesClick?: () => void;
  onContactClick?: () => void;
  className?: string;
}

const ModernHeader: React.FC<ModernHeaderProps> = ({
  onAboutClick,
  onFeaturesClick,
  onContactClick,
  className
}) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const headerRef = useRef<HTMLHeaderElement>(null);
  const logoRef = useRef<HTMLDivElement>(null);
  const navRef = useRef<HTMLNavElement>(null);
  const mobileMenuRef = useRef<HTMLDivElement>(null);
  const { scrollToSection } = useModernSmoothScroll();
  const { trapFocus, releaseFocusTrap } = useFocusManagement();

  // Animate header on mount
  useModernFadeIn(headerRef, { direction: 'down', duration: 0.8 });

  // Keyboard navigation for mobile menu
  useKeyboardNavigation(
    () => {
      if (isMobileMenuOpen) {
        setIsMobileMenuOpen(false);
      }
    }
  );

  // Handle scroll behavior
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu when clicking outside and manage focus
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (headerRef.current && !headerRef.current.contains(event.target as Node)) {
        setIsMobileMenuOpen(false);
      }
    };

    if (isMobileMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      // Trap focus in mobile menu
      if (mobileMenuRef.current) {
        trapFocus(mobileMenuRef.current);
      }
    } else {
      releaseFocusTrap();
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      releaseFocusTrap();
    };
  }, [isMobileMenuOpen, trapFocus, releaseFocusTrap]);

  const handleNavClick = (callback?: () => void) => {
    setIsMobileMenuOpen(false);
    callback?.();
  };

  return (
    <header
      ref={headerRef}
      role="banner"
      className={cn(
        "fixed top-0 left-0 right-0 z-50 transition-all duration-500",
        isScrolled
          ? "bg-white/80 backdrop-blur-md shadow-modern border-b border-modern-neutral-200"
          : "bg-transparent",
        className
      )}
    >
      <div className="container mx-auto px-grid-4 lg:px-grid-6">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo */}
          <div
            ref={logoRef}
            className="flex items-center space-x-grid-2 cursor-pointer"
            onClick={() => scrollToSection('body', 0)}
          >
            <div className="w-8 h-8 lg:w-10 lg:h-10 bg-modern-purple rounded-lg flex items-center justify-center">
              <span className="text-white font-heading font-bold text-lg lg:text-xl">P</span>
            </div>
            <span className="font-heading font-bold text-lg lg:text-xl text-modern-purple">
              Promise Academy
            </span>
          </div>

          {/* Desktop Navigation */}
          <nav
            ref={navRef}
            className="hidden md:flex items-center space-x-grid-6 lg:space-x-grid-8"
          >
            <button
              onClick={() => handleNavClick(onAboutClick)}
              className="font-body text-modern-neutral-700 hover:text-modern-purple transition-colors duration-300 relative group"
            >
              About
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-modern-purple transition-all duration-300 group-hover:w-full"></span>
            </button>
            <button
              onClick={() => handleNavClick(onFeaturesClick)}
              className="font-body text-modern-neutral-700 hover:text-modern-purple transition-colors duration-300 relative group"
            >
              Features
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-modern-purple transition-all duration-300 group-hover:w-full"></span>
            </button>
            <button
              onClick={() => handleNavClick(onContactClick)}
              className="font-body text-modern-neutral-700 hover:text-modern-purple transition-colors duration-300 relative group"
            >
              Contact
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-modern-purple transition-all duration-300 group-hover:w-full"></span>
            </button>
          </nav>

          {/* CTA Button & Mobile Menu Toggle */}
          <div className="flex items-center space-x-grid-3">
            <Button
              variant="primary"
              size="default"
              className="hidden sm:inline-flex"
              onClick={() => scrollToSection('/login')}
            >
              Get Started
            </Button>

            {/* Mobile Menu Toggle */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-expanded={isMobileMenuOpen}
              aria-controls="mobile-menu"
              aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
              className="md:hidden p-2 text-modern-neutral-700 hover:text-modern-purple transition-colors duration-300"
            >
              {isMobileMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        <div
          ref={mobileMenuRef}
          id="mobile-menu"
          className={cn(
            "md:hidden overflow-hidden transition-all duration-500 ease-out",
            isMobileMenuOpen
              ? "max-h-96 opacity-100 pb-grid-4"
              : "max-h-0 opacity-0 pb-0"
          )}
          aria-hidden={!isMobileMenuOpen}
        >
          <nav
            className="flex flex-col space-y-grid-4 pt-grid-4 border-t border-modern-neutral-200"
            role="navigation"
            aria-label="Mobile navigation"
          >
            <button
              onClick={() => handleNavClick(onAboutClick)}
              className="text-left font-body text-modern-neutral-700 hover:text-modern-purple transition-colors duration-300 py-2"
            >
              About
            </button>
            <button
              onClick={() => handleNavClick(onFeaturesClick)}
              className="text-left font-body text-modern-neutral-700 hover:text-modern-purple transition-colors duration-300 py-2"
            >
              Features
            </button>
            <button
              onClick={() => handleNavClick(onContactClick)}
              className="text-left font-body text-modern-neutral-700 hover:text-modern-purple transition-colors duration-300 py-2"
            >
              Contact
            </button>
            <div className="pt-grid-2">
              <Button
                variant="primary"
                size="default"
                className="w-full"
                onClick={() => {
                  setIsMobileMenuOpen(false);
                  scrollToSection('/login');
                }}
              >
                Get Started
              </Button>
            </div>
          </nav>
        </div>
      </div>
    </header>
  );
};

export default ModernHeader;
