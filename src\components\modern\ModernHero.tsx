import React, { useRef, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowRight, Play } from 'lucide-react';
import {
  useModernFadeIn,
  useModernParallax,
  useModernFloat,
  useModernTextReveal,
  useModernSmoothScroll
} from '@/utils/modernGSAPAnimations';
import { useIsMobile, useViewportHeight, usePrefersReducedMotion } from '@/utils/modernResponsive';
import { cn } from '@/lib/utils';

interface ModernHeroProps {
  title?: string;
  subtitle?: string;
  description?: string;
  primaryCTA?: string;
  secondaryCTA?: string;
  onPrimaryCTA?: () => void;
  onSecondaryCTA?: () => void;
  className?: string;
}

const ModernHero: React.FC<ModernHeroProps> = ({
  title = "Modern Learning Experience",
  subtitle = "Promise Academy",
  description = "Discover a new way of learning with our innovative platform designed for the next generation of students.",
  primaryCTA = "Get Started",
  secondaryCTA = "Watch Demo",
  onPrimaryCTA,
  onSecondaryCTA,
  className
}) => {
  const heroRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const descriptionRef = useRef<HTMLParagraphElement>(null);
  const ctaRef = useRef<HTMLDivElement>(null);
  const backgroundRef = useRef<HTMLDivElement>(null);
  const shape1Ref = useRef<HTMLDivElement>(null);
  const shape2Ref = useRef<HTMLDivElement>(null);
  const shape3Ref = useRef<HTMLDivElement>(null);
  const { scrollToSection } = useModernSmoothScroll();

  // Responsive hooks
  const isMobile = useIsMobile();
  const viewportHeight = useViewportHeight();
  const prefersReducedMotion = usePrefersReducedMotion();

  // Animate elements on mount (respect reduced motion)
  if (!prefersReducedMotion) {
    useModernFadeIn(subtitleRef, { direction: 'up', delay: 0.2 });
    useModernTextReveal(titleRef, { delay: 0.4, stagger: isMobile ? 0.02 : 0.05 });
    useModernFadeIn(descriptionRef, { direction: 'up', delay: 0.8 });
    useModernFadeIn(ctaRef, { direction: 'up', delay: 1.0 });

    // Parallax effects for background elements (disabled on mobile for performance)
    if (!isMobile) {
      useModernParallax(backgroundRef, { yPercent: -20, speed: 0.5 });
    }

    // Floating animations for shapes (reduced on mobile)
    const amplitude = isMobile ? 10 : 20;
    const duration = isMobile ? 8 : 6;
    useModernFloat(shape1Ref, { amplitude, duration, delay: 0 });
    useModernFloat(shape2Ref, { amplitude: amplitude * 0.75, duration: duration + 2, delay: 2 });
    useModernFloat(shape3Ref, { amplitude: amplitude * 1.25, duration: duration + 1, delay: 4 });
  }

  const handlePrimaryCTA = () => {
    if (onPrimaryCTA) {
      onPrimaryCTA();
    } else {
      scrollToSection('/login');
    }
  };

  const handleSecondaryCTA = () => {
    if (onSecondaryCTA) {
      onSecondaryCTA();
    } else {
      // Default behavior - scroll to features or open demo
      scrollToSection('#features');
    }
  };

  return (
    <section
      ref={heroRef}
      className={cn(
        "relative flex items-center justify-center overflow-hidden bg-gradient-to-br from-modern-neutral-50 to-white",
        isMobile ? "min-h-screen" : "min-h-screen",
        className
      )}
      style={{
        minHeight: isMobile ? `${viewportHeight}px` : '100vh'
      }}
    >
      {/* Animated Background */}
      <div
        ref={backgroundRef}
        className="absolute inset-0 bg-modern-hero opacity-5"
      />

      {/* Floating Background Shapes */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div
          ref={shape1Ref}
          className="absolute top-1/4 left-1/4 w-64 h-64 bg-modern-purple/10 rounded-full blur-3xl"
        />
        <div
          ref={shape2Ref}
          className="absolute bottom-1/3 right-1/4 w-96 h-96 bg-modern-gold/10 rounded-full blur-2xl"
        />
        <div
          ref={shape3Ref}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-modern-red/10 rounded-full blur-3xl"
        />
      </div>

      {/* Content */}
      <div className="relative z-10 container mx-auto px-grid-4 lg:px-grid-6 text-center">
        <div className="max-w-4xl mx-auto">
          {/* Subtitle */}
          <p
            ref={subtitleRef}
            className="font-body text-modern-purple font-medium text-lg lg:text-xl mb-grid-3 opacity-0"
          >
            {subtitle}
          </p>

          {/* Main Title */}
          <h1
            ref={titleRef}
            className="font-heading font-bold text-4xl md:text-6xl lg:text-7xl text-modern-neutral-900 mb-grid-6 leading-tight"
          >
            {title}
          </h1>

          {/* Description */}
          <p
            ref={descriptionRef}
            className="font-body text-modern-neutral-600 text-lg lg:text-xl mb-grid-8 max-w-2xl mx-auto leading-relaxed opacity-0"
          >
            {description}
          </p>

          {/* CTA Buttons */}
          <div
            ref={ctaRef}
            className="flex flex-col sm:flex-row items-center justify-center gap-grid-4 opacity-0"
          >
            <Button
              variant="primary"
              size="lg"
              onClick={handlePrimaryCTA}
              className="group"
            >
              {primaryCTA}
              <ArrowRight className="ml-2 w-5 h-5 transition-transform group-hover:translate-x-1" />
            </Button>
            
            <Button
              variant="outline"
              size="lg"
              onClick={handleSecondaryCTA}
              className="group"
            >
              <Play className="mr-2 w-5 h-5 transition-transform group-hover:scale-110" />
              {secondaryCTA}
            </Button>
          </div>

          {/* Stats or Features Preview */}
          <div className="mt-grid-12 grid grid-cols-1 md:grid-cols-3 gap-grid-6 max-w-3xl mx-auto">
            <div className="text-center">
              <div className="font-heading font-bold text-2xl lg:text-3xl text-modern-purple mb-2">
                1000+
              </div>
              <div className="font-body text-modern-neutral-600">
                Students Enrolled
              </div>
            </div>
            <div className="text-center">
              <div className="font-heading font-bold text-2xl lg:text-3xl text-modern-gold mb-2">
                50+
              </div>
              <div className="font-body text-modern-neutral-600">
                Expert Teachers
              </div>
            </div>
            <div className="text-center">
              <div className="font-heading font-bold text-2xl lg:text-3xl text-modern-red mb-2">
                95%
              </div>
              <div className="font-body text-modern-neutral-600">
                Success Rate
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-grid-6 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-modern-purple rounded-full flex justify-center">
          <div className="w-1 h-3 bg-modern-purple rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
};

export default ModernHero;
