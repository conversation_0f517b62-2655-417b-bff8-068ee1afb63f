import React, { useRef, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Eye, EyeOff, ArrowLeft } from 'lucide-react';
import { 
  useModernFadeIn, 
  useModernFloat 
} from '@/utils/modernGSAPAnimations';
import { cn } from '@/lib/utils';

interface ModernLoginProps {
  onLogin?: (email: string, password: string) => Promise<void>;
  onBackToHome?: () => void;
  className?: string;
}

const ModernLogin: React.FC<ModernLoginProps> = ({
  onLogin,
  onBackToHome,
  className
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const cardRef = useRef<HTMLDivElement>(null);
  const shape1Ref = useRef<HTMLDivElement>(null);
  const shape2Ref = useRef<HTMLDivElement>(null);
  const shape3Ref = useRef<HTMLDivElement>(null);

  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Animate elements
  useModernFadeIn(cardRef, { direction: 'scale', delay: 0.3 });
  
  // Floating background animations
  useModernFloat(shape1Ref, { amplitude: 20, duration: 8, delay: 0 });
  useModernFloat(shape2Ref, { amplitude: 15, duration: 6, delay: 2 });
  useModernFloat(shape3Ref, { amplitude: 25, duration: 10, delay: 4 });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    if (error) setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      if (onLogin) {
        await onLogin(formData.email, formData.password);
      } else {
        // Default behavior - simulate login
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log('Login attempt:', formData);
      }
    } catch (err) {
      setError('Invalid email or password. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToHome = () => {
    if (onBackToHome) {
      onBackToHome();
    } else {
      window.location.href = '/';
    }
  };

  return (
    <div
      ref={containerRef}
      className={cn(
        "min-h-screen flex items-center justify-center relative overflow-hidden",
        "bg-gradient-to-br from-modern-purple via-modern-neutral-100 to-modern-gold",
        className
      )}
    >
      {/* Animated Background Shapes */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div
          ref={shape1Ref}
          className="absolute top-1/4 left-1/4 w-64 h-64 bg-modern-purple/10 rounded-full blur-3xl"
        />
        <div
          ref={shape2Ref}
          className="absolute bottom-1/3 right-1/4 w-96 h-96 bg-modern-gold/10 rounded-full blur-2xl"
        />
        <div
          ref={shape3Ref}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-modern-red/10 rounded-full blur-3xl"
        />
      </div>

      {/* Back to Home Button */}
      <button
        onClick={handleBackToHome}
        className="absolute top-grid-6 left-grid-6 flex items-center space-x-2 text-modern-neutral-700 hover:text-modern-purple transition-colors duration-300 z-20"
      >
        <ArrowLeft className="w-5 h-5" />
        <span className="font-body">Back to Home</span>
      </button>

      {/* Login Card */}
      <Card
        ref={cardRef}
        variant="glass"
        className="w-full max-w-md mx-grid-4 backdrop-blur-xl border border-white/20 shadow-modern-xl opacity-0"
      >
        <CardHeader className="text-center pb-grid-6">
          {/* Logo */}
          <div className="flex items-center justify-center space-x-grid-2 mb-grid-4">
            <div className="w-10 h-10 bg-modern-purple rounded-lg flex items-center justify-center">
              <span className="text-white font-heading font-bold text-xl">P</span>
            </div>
            <span className="font-heading font-bold text-xl text-modern-purple">
              Promise Academy
            </span>
          </div>
          
          <CardTitle className="text-2xl lg:text-3xl text-modern-neutral-900">
            Welcome Back
          </CardTitle>
          <p className="font-body text-modern-neutral-600 mt-2">
            Sign in to continue your learning journey
          </p>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-grid-5">
            {/* Email Input */}
            <Input
              label="Email Address"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleInputChange}
              variant="default"
              size="lg"
              required
            />

            {/* Password Input */}
            <div className="relative">
              <Input
                label="Password"
                name="password"
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={handleInputChange}
                variant="default"
                size="lg"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-grid-3 top-1/2 transform -translate-y-1/2 text-modern-neutral-400 hover:text-modern-purple transition-colors duration-300"
              >
                {showPassword ? (
                  <EyeOff className="w-5 h-5" />
                ) : (
                  <Eye className="w-5 h-5" />
                )}
              </button>
            </div>

            {/* Error Message */}
            {error && (
              <p className="text-modern-red font-body text-sm text-center bg-modern-red/10 rounded-lg p-grid-3">
                {error}
              </p>
            )}

            {/* Login Button */}
            <Button
              type="submit"
              variant="primary"
              size="lg"
              loading={isLoading}
              loadingText="Signing in..."
              className="w-full"
            >
              Sign In
            </Button>

            {/* Divider */}
            <div className="relative my-grid-6">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-modern-neutral-300"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-modern-neutral-500 font-body">
                  Or continue with
                </span>
              </div>
            </div>

            {/* Social Login Buttons */}
            <div className="grid grid-cols-2 gap-grid-3">
              <Button
                type="button"
                variant="outline"
                size="default"
                className="w-full"
              >
                Google
              </Button>
              <Button
                type="button"
                variant="outline"
                size="default"
                className="w-full"
              >
                Microsoft
              </Button>
            </div>

            {/* Footer Links */}
            <div className="text-center space-y-grid-3 pt-grid-4">
              <button
                type="button"
                className="font-body text-modern-purple hover:text-modern-gold transition-colors duration-300 text-sm"
              >
                Forgot your password?
              </button>
              <p className="font-body text-modern-neutral-600 text-sm">
                Don't have an account?{' '}
                <button
                  type="button"
                  className="text-modern-purple hover:text-modern-gold transition-colors duration-300 font-medium"
                >
                  Sign up here
                </button>
              </p>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default ModernLogin;
