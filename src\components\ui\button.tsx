import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg font-medium font-heading transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 transform hover:scale-105 active:scale-95",
  {
    variants: {
      variant: {
        // Modern Design System Variants
        primary: "bg-modern-gold text-white shadow-modern-button hover:shadow-modern-glow-gold focus-visible:ring-modern-gold/50 bg-modern-button-primary",
        secondary: "bg-modern-purple text-white shadow-modern-button hover:shadow-modern-glow-purple focus-visible:ring-modern-purple/50 bg-modern-button-secondary",
        accent: "bg-modern-red text-white shadow-modern-button hover:shadow-modern-glow-red focus-visible:ring-modern-red/50 bg-modern-button-accent",
        outline: "border-2 border-modern-purple text-modern-purple bg-transparent hover:bg-modern-purple hover:text-white focus-visible:ring-modern-purple/50",
        ghost: "text-modern-purple hover:bg-modern-purple/10 focus-visible:ring-modern-purple/50",
        glass: "bg-modern-glass backdrop-blur-md border border-white/20 text-modern-purple hover:bg-white/20 focus-visible:ring-modern-purple/50",
        // Legacy variants (keeping for compatibility)
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        sm: "h-9 px-grid-3 text-sm rounded-md",
        default: "h-11 px-grid-4 text-base rounded-lg",
        lg: "h-13 px-grid-6 text-lg rounded-xl",
        xl: "h-16 px-grid-8 text-xl rounded-2xl",
        icon: "h-10 w-10 rounded-lg",
      },
      loading: {
        true: "cursor-not-allowed",
        false: "",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "default",
      loading: false,
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  loading?: boolean
  loadingText?: string
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, loading = false, loadingText, children, disabled, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"

    return (
      <Comp
        className={cn(buttonVariants({ variant, size, loading, className }))}
        ref={ref}
        disabled={disabled || loading}
        {...props}
      >
        {loading && (
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        )}
        {loading ? loadingText || "Loading..." : children}
      </Comp>
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
