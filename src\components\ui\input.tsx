import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const inputVariants = cva(
  "flex w-full rounded-lg border bg-white font-body text-modern-neutral-900 transition-all duration-300 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-modern-neutral-400 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "border-modern-neutral-300 focus-visible:border-modern-purple focus-visible:ring-2 focus-visible:ring-modern-purple/20",
        error: "border-modern-red focus-visible:border-modern-red focus-visible:ring-2 focus-visible:ring-modern-red/20",
        success: "border-modern-gold focus-visible:border-modern-gold focus-visible:ring-2 focus-visible:ring-modern-gold/20",
        glass: "bg-modern-glass backdrop-blur-md border-white/20 text-white placeholder:text-white/60",
      },
      size: {
        sm: "h-9 px-grid-3 py-2 text-sm",
        default: "h-11 px-grid-4 py-3 text-base",
        lg: "h-13 px-grid-5 py-4 text-lg",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface InputProps
  extends React.ComponentProps<"input">,
    VariantProps<typeof inputVariants> {
  label?: string
  error?: string
  success?: string
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, variant, size, label, error, success, ...props }, ref) => {
    const [focused, setFocused] = React.useState(false)
    const [hasValue, setHasValue] = React.useState(false)

    const handleFocus = () => setFocused(true)
    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setFocused(false)
      setHasValue(e.target.value !== '')
    }

    const inputVariant = error ? 'error' : success ? 'success' : variant

    return (
      <div className="relative">
        <input
          type={type}
          className={cn(inputVariants({ variant: inputVariant, size, className }))}
          ref={ref}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onChange={(e) => {
            setHasValue(e.target.value !== '')
            props.onChange?.(e)
          }}
          {...props}
        />
        {label && (
          <label
            className={cn(
              "absolute left-grid-4 transition-all duration-300 pointer-events-none font-body",
              focused || hasValue || props.value
                ? "top-2 text-xs text-modern-purple"
                : "top-1/2 -translate-y-1/2 text-modern-neutral-400",
              size === "sm" && "left-grid-3",
              size === "lg" && "left-grid-5"
            )}
          >
            {label}
          </label>
        )}
        {error && (
          <p className="mt-1 text-sm text-modern-red font-body">{error}</p>
        )}
        {success && (
          <p className="mt-1 text-sm text-modern-gold font-body">{success}</p>
        )}
      </div>
    )
  }
)
Input.displayName = "Input"

export { Input, inputVariants }
