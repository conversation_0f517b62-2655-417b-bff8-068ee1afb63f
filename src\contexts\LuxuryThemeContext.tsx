import React, { createContext, useContext, useState, useEffect } from 'react';

export interface LuxuryTheme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
  };
  effects: {
    glow: boolean;
    particles: boolean;
    animations: boolean;
    sounds: boolean;
  };
  premiumLevel: 'standard' | 'premium' | 'vip' | 'elite';
}

export interface LuxuryThemeContextType {
  theme: LuxuryTheme;
  setTheme: (theme: LuxuryTheme) => void;
  toggleEffect: (effect: keyof LuxuryTheme['effects']) => void;
  setPremiumLevel: (level: LuxuryTheme['premiumLevel']) => void;
  isVIP: boolean;
  isElite: boolean;
}

const defaultTheme: LuxuryTheme = {
  name: 'Royal Gold',
  colors: {
    primary: '#FFD700',
    secondary: '#663399',
    accent: '#50C878',
    background: 'linear-gradient(135deg, #1B1B3A 0%, #0F0F23 100%)',
    surface: 'rgba(255, 255, 255, 0.1)',
    text: '#FFFFFF',
  },
  effects: {
    glow: true,
    particles: true,
    animations: true,
    sounds: false,
  },
  premiumLevel: 'vip',
};

const LuxuryThemeContext = createContext<LuxuryThemeContextType | undefined>(undefined);

export const LuxuryThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [theme, setTheme] = useState<LuxuryTheme>(defaultTheme);

  const toggleEffect = (effect: keyof LuxuryTheme['effects']) => {
    setTheme(prev => ({
      ...prev,
      effects: {
        ...prev.effects,
        [effect]: !prev.effects[effect],
      },
    }));
  };

  const setPremiumLevel = (level: LuxuryTheme['premiumLevel']) => {
    setTheme(prev => ({
      ...prev,
      premiumLevel: level,
    }));
  };

  const isVIP = theme.premiumLevel === 'vip' || theme.premiumLevel === 'elite';
  const isElite = theme.premiumLevel === 'elite';

  // Apply theme to CSS custom properties
  useEffect(() => {
    const root = document.documentElement;
    root.style.setProperty('--luxury-primary', theme.colors.primary);
    root.style.setProperty('--luxury-secondary', theme.colors.secondary);
    root.style.setProperty('--luxury-accent', theme.colors.accent);
    root.style.setProperty('--luxury-background', theme.colors.background);
    root.style.setProperty('--luxury-surface', theme.colors.surface);
    root.style.setProperty('--luxury-text', theme.colors.text);
  }, [theme]);

  const value: LuxuryThemeContextType = {
    theme,
    setTheme,
    toggleEffect,
    setPremiumLevel,
    isVIP,
    isElite,
  };

  return (
    <LuxuryThemeContext.Provider value={value}>
      {children}
    </LuxuryThemeContext.Provider>
  );
};

export const useLuxuryTheme = (): LuxuryThemeContextType => {
  const context = useContext(LuxuryThemeContext);
  if (context === undefined) {
    throw new Error('useLuxuryTheme must be used within a LuxuryThemeProvider');
  }
  return context;
};

// Predefined luxury themes
export const luxuryThemes: LuxuryTheme[] = [
  {
    name: 'Royal Gold',
    colors: {
      primary: '#FFD700',
      secondary: '#663399',
      accent: '#50C878',
      background: 'linear-gradient(135deg, #1B1B3A 0%, #0F0F23 100%)',
      surface: 'rgba(255, 255, 255, 0.1)',
      text: '#FFFFFF',
    },
    effects: { glow: true, particles: true, animations: true, sounds: false },
    premiumLevel: 'vip',
  },
  {
    name: 'Platinum Dreams',
    colors: {
      primary: '#E5E4E2',
      secondary: '#C0C0C0',
      accent: '#FF69B4',
      background: 'linear-gradient(135deg, #2C2C54 0%, #1A1A2E 100%)',
      surface: 'rgba(229, 228, 226, 0.1)',
      text: '#FFFFFF',
    },
    effects: { glow: true, particles: true, animations: true, sounds: false },
    premiumLevel: 'premium',
  },
  {
    name: 'Emerald Elite',
    colors: {
      primary: '#50C878',
      secondary: '#228B22',
      accent: '#FFD700',
      background: 'linear-gradient(135deg, #0F3460 0%, #16213E 100%)',
      surface: 'rgba(80, 200, 120, 0.1)',
      text: '#FFFFFF',
    },
    effects: { glow: true, particles: true, animations: true, sounds: false },
    premiumLevel: 'elite',
  },
  {
    name: 'Rose Gold Royalty',
    colors: {
      primary: '#E8B4B8',
      secondary: '#B76E79',
      accent: '#00BFFF',
      background: 'linear-gradient(135deg, #4A154B 0%, #2D1B69 100%)',
      surface: 'rgba(232, 180, 184, 0.1)',
      text: '#FFFFFF',
    },
    effects: { glow: true, particles: true, animations: true, sounds: false },
    premiumLevel: 'vip',
  },
];
