
import { useRef, useEffect } from 'react';
import {
  ModernHeader,
  ModernHero,
  ModernFeatures,
  ModernAbout,
  ModernContact,
  ModernFooter
} from '@/components/modern';
import { initModernSmoothScroll } from '@/utils/modernGSAPAnimations';

const Index = () => {
  // Create refs for scroll functionality
  const aboutRef = useRef<HTMLDivElement>(null);
  const featuresRef = useRef<HTMLDivElement>(null);
  const contactRef = useRef<HTMLDivElement>(null);

  // Initialize GSAP animations
  useEffect(() => {
    initModernSmoothScroll();
  }, []);

  // Scroll functions
  const scrollToSection = (ref: React.RefObject<HTMLDivElement>) => {
    ref.current?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen flex flex-col bg-white">
      <ModernHeader
        onAboutClick={() => scrollToSection(aboutRef)}
        onFeaturesClick={() => scrollToSection(featuresRef)}
        onContactClick={() => scrollToSection(contactRef)}
      />
      <main className="flex-1">
        <ModernHero
          title="Modern Learning Experience"
          subtitle="Promise Academy"
          description="Discover a new way of learning with our innovative platform designed for the next generation of students."
          primaryCTA="Get Started"
          secondaryCTA="Watch Demo"
        />
        <div ref={featuresRef}>
          <ModernFeatures />
        </div>
        <div ref={aboutRef}>
          <ModernAbout />
        </div>
        <div ref={contactRef}>
          <ModernContact />
        </div>
      </main>
      <ModernFooter />
    </div>
  );
};

export default Index;
