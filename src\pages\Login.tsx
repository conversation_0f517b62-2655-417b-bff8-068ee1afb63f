
import { useEffect } from 'react';
import { ModernLogin } from '@/components/modern';
import { initModernSmoothScroll } from '@/utils/modernGSAPAnimations';

const Login = () => {
  // Initialize GSAP animations
  useEffect(() => {
    initModernSmoothScroll();
  }, []);

  const handleLogin = async (email: string, password: string) => {
    // TODO: Implement actual login logic
    console.log('Login attempt:', { email, password });
    // Simulate login process
    await new Promise(resolve => setTimeout(resolve, 2000));
  };

  const handleBackToHome = () => {
    window.location.href = '/';
  };

  return (
    <ModernLogin
      onLogin={handleLogin}
      onBackToHome={handleBackToHome}
    />
  );
};

export default Login;
