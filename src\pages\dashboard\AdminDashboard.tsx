
import AdminDashboardComponent from '@/components/dashboard/admin/AdminDashboard';
import { Routes, Route } from "react-router-dom";
import StudentsManagement from '@/components/dashboard/admin/StudentsManagement';
import ReportsManagement from '@/components/dashboard/admin/ReportsManagement';
import CurriculumManagement from '@/components/dashboard/admin/CurriculumManagement';
import AnalyticsManagement from '@/components/dashboard/admin/AnalyticsManagement';
import ClassroomManagement from '@/components/dashboard/admin/ClassroomManagement';
import GradingManagement from '@/components/dashboard/admin/GradingManagement';
import InventoryManagement from '@/components/dashboard/admin/InventoryManagement';
import FinancialManagement from '@/components/dashboard/admin/FinancialManagement';
import TimetableManagement from '@/components/dashboard/admin/TimetableManagement';
import HealthManagement from '@/components/dashboard/admin/HealthManagement';
import UserRegistration from '@/components/dashboard/admin/UserRegistration';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Loader2 } from 'lucide-react';

const AdminDashboard = () => {
  const navigate = useNavigate();
  const { user, profile, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);

  // Check if user is authenticated and has admin role
  useEffect(() => {
    const checkAuth = async () => {
      try {
        if (authLoading) {
          return; // Wait for auth to load
        }

        if (!user) {
          // User is not logged in, redirect to login
          navigate('/login');
          return;
        }

        if (!profile) {
          console.error('No profile found');
          navigate('/login');
          return;
        }

        if (profile.role !== 'admin') {
          // User is not an admin, redirect to appropriate dashboard
          navigate(`/dashboard/${profile.role}`);
          return;
        }

        setLoading(false);
      } catch (error) {
        console.error('Authentication error:', error);
        navigate('/login');
      }
    };

    checkAuth();
  }, [navigate, user, profile, authLoading]);

  if (loading || authLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-12 w-12 animate-spin text-promise-500" />
      </div>
    );
  }

  // Using exact path to prevent route conflicts
  return (
    <Routes>
      <Route path="/" element={<AdminDashboardComponent />} />
      <Route path="/students" element={<StudentsManagement />} />
      <Route path="/reports" element={<ReportsManagement />} />
      <Route path="/curriculum" element={<CurriculumManagement />} />
      <Route path="/analytics" element={<AnalyticsManagement />} />
      <Route path="/classroom" element={<ClassroomManagement />} />
      <Route path="/grading" element={<GradingManagement />} />
      <Route path="/inventory" element={<InventoryManagement />} />
      <Route path="/financial" element={<FinancialManagement />} />
      <Route path="/timetable" element={<TimetableManagement />} />
      <Route path="/health" element={<HealthManagement />} />
      <Route path="/registration" element={<UserRegistration />} />
    </Routes>
  );
};

export default AdminDashboard;
