
import ParentDashboardComponent from '@/components/dashboard/parent/ParentDashboard';
import { Routes, Route } from "react-router-dom";
import ParentSchedule from '@/components/dashboard/parent/ParentSchedule';
import ParentReports from '@/components/dashboard/parent/ParentReports';
import ParentHealth from '@/components/dashboard/parent/ParentHealth';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Loader2 } from 'lucide-react';

const ParentDashboard = () => {
  const navigate = useNavigate();
  const { user, profile, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);

  // Check if user is authenticated and has parent role
  useEffect(() => {
    const checkAuth = async () => {
      try {
        if (authLoading) {
          return; // Wait for auth to load
        }

        if (!user) {
          // User is not logged in, redirect to login
          navigate('/login');
          return;
        }

        if (!profile) {
          console.error('No profile found');
          navigate('/login');
          return;
        }

        if (profile.role !== 'parent') {
          // User is not a parent, redirect to appropriate dashboard
          navigate(`/dashboard/${profile.role}`);
          return;
        }

        setLoading(false);
      } catch (error) {
        console.error('Authentication error:', error);
        navigate('/login');
      }
    };

    checkAuth();
  }, [navigate, user, profile, authLoading]);

  if (loading || authLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-12 w-12 animate-spin text-promise-500" />
      </div>
    );
  }

  return (
    <Routes>
      <Route path="/" element={<ParentDashboardComponent />} />
      <Route path="/timetable" element={<ParentSchedule />} />
      <Route path="/reports" element={<ParentReports />} />
      <Route path="/health" element={<ParentHealth />} />
    </Routes>
  );
};

export default ParentDashboard;
