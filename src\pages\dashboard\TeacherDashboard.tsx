
import { Routes, Route } from "react-router-dom";
import TeacherDashboardComponent from '@/components/dashboard/teacher/TeacherDashboard';
import ClassroomManagement from '@/components/dashboard/teacher/ClassroomManagement';
import GradingManagement from '@/components/dashboard/teacher/GradingManagement';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Loader2 } from 'lucide-react';

const TeacherDashboard = () => {
  const navigate = useNavigate();
  const { user, profile, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(true);

  // Check if user is authenticated and has teacher role
  useEffect(() => {
    const checkAuth = async () => {
      try {
        if (authLoading) {
          return; // Wait for auth to load
        }

        if (!user) {
          // User is not logged in, redirect to login
          navigate('/login');
          return;
        }

        if (!profile) {
          console.error('No profile found');
          navigate('/login');
          return;
        }

        if (profile.role !== 'teacher') {
          // User is not a teacher, redirect to appropriate dashboard
          navigate(`/dashboard/${profile.role}`);
          return;
        }

        setLoading(false);
      } catch (error) {
        console.error('Authentication error:', error);
        navigate('/login');
      }
    };

    checkAuth();
  }, [navigate, user, profile, authLoading]);

  if (loading || authLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-12 w-12 animate-spin text-promise-500" />
      </div>
    );
  }

  return (
    <Routes>
      <Route path="/" element={<TeacherDashboardComponent />} />
      <Route path="/classroom" element={<ClassroomManagement />} />
      <Route path="/grading" element={<GradingManagement />} />
    </Routes>
  );
};

export default TeacherDashboard;
