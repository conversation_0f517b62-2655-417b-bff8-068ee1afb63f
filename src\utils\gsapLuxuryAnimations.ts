import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { ScrollToPlugin } from 'gsap/ScrollToPlugin';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, ScrollToPlugin);

// Optimized GSAP Luxury Animation Configuration for 60fps
export const luxuryGSAPConfig = {
  duration: 1.2,
  ease: "power3.out",
  stagger: 0.1,
  parallaxStrength: 0.5,
  performance: {
    force3D: true,
    willChange: true,
    autoAlpha: true, // Use autoAlpha instead of opacity for better performance
    transformOrigin: "center center",
  }
};

// Optimized smooth scroll configuration for 60fps performance
export const initLuxurySmoothScroll = () => {
  gsap.registerPlugin(ScrollTrigger);

  // Configure smooth scrolling with hardware acceleration
  gsap.config({
    force3D: true,
    nullTargetWarn: false,
    autoSleep: 60, // Automatically pause animations after 60 seconds of inactivity
    units: { left: "px", top: "px" }, // Specify units for better performance
  });

  // Set up performance optimizations for smooth 60fps
  ScrollTrigger.config({
    limitCallbacks: true,
    syncInterval: 120, // Reduced for smoother performance
    autoRefreshEvents: "visibilitychange,DOMContentLoaded,load,resize",
  });

  // Enable hardware acceleration for smooth scrolling
  ScrollTrigger.defaults({
    toggleActions: "play pause resume reverse",
    scroller: window,
  });
};

// GSAP Parallax Hook
export const useGSAPParallax = (
  elementRef: React.RefObject<HTMLElement>,
  options: {
    yPercent?: number;
    scale?: number;
    rotation?: number;
    opacity?: number;
    trigger?: string;
  } = {}
) => {
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const {
      yPercent = -50,
      scale = 1.1,
      rotation = 0,
      opacity = 1,
      trigger = element
    } = options;

    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: trigger,
        start: "top bottom",
        end: "bottom top",
        scrub: 1,
        invalidateOnRefresh: true,
      }
    });

    tl.fromTo(element, 
      {
        yPercent: 0,
        scale: 1,
        rotation: 0,
        opacity: opacity,
        force3D: true,
      },
      {
        yPercent: yPercent,
        scale: scale,
        rotation: rotation,
        opacity: opacity * 0.8,
        ease: "none",
        force3D: true,
      }
    );

    return () => {
      tl.kill();
    };
  }, [elementRef, options]);
};

// GSAP Smooth Entrance Animation
export const useGSAPEntrance = (
  elementRef: React.RefObject<HTMLElement>,
  options: {
    delay?: number;
    duration?: number;
    from?: 'bottom' | 'top' | 'left' | 'right' | 'scale' | 'fade';
    stagger?: number;
  } = {}
) => {
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const {
      delay = 0,
      duration = luxuryGSAPConfig.duration,
      from = 'bottom',
      stagger = 0
    } = options;

    const fromProps: any = { opacity: 0, force3D: true };
    const toProps: any = { opacity: 1, force3D: true };

    switch (from) {
      case 'bottom':
        fromProps.y = 60;
        toProps.y = 0;
        break;
      case 'top':
        fromProps.y = -60;
        toProps.y = 0;
        break;
      case 'left':
        fromProps.x = -60;
        toProps.x = 0;
        break;
      case 'right':
        fromProps.x = 60;
        toProps.x = 0;
        break;
      case 'scale':
        fromProps.scale = 0.8;
        toProps.scale = 1;
        break;
      case 'fade':
        // Only opacity animation
        break;
    }

    gsap.set(element, fromProps);

    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: element,
        start: "top 85%",
        toggleActions: "play none none reverse",
      }
    });

    if (element.children.length > 1 && stagger > 0) {
      tl.to(element.children, {
        ...toProps,
        duration: duration,
        ease: luxuryGSAPConfig.ease,
        stagger: stagger,
        delay: delay,
      });
    } else {
      tl.to(element, {
        ...toProps,
        duration: duration,
        ease: luxuryGSAPConfig.ease,
        delay: delay,
      });
    }

    return () => {
      tl.kill();
    };
  }, [elementRef, options]);
};

// GSAP Hover Animation
export const useGSAPHover = (
  elementRef: React.RefObject<HTMLElement>,
  options: {
    scale?: number;
    rotation?: number;
    duration?: number;
    glow?: boolean;
  } = {}
) => {
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const {
      scale = 1.05,
      rotation = 0,
      duration = 0.3,
      glow = false
    } = options;

    const hoverProps: any = {
      scale: scale,
      rotation: rotation,
      duration: duration,
      ease: "power2.out",
      force3D: true,
    };

    if (glow) {
      hoverProps.filter = "drop-shadow(0 0 20px rgba(255, 215, 0, 0.5))";
    }

    const handleMouseEnter = () => {
      gsap.to(element, hoverProps);
    };

    const handleMouseLeave = () => {
      gsap.to(element, {
        scale: 1,
        rotation: 0,
        duration: duration,
        ease: "power2.out",
        filter: "none",
        force3D: true,
      });
    };

    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      element.removeEventListener('mouseenter', handleMouseEnter);
      element.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [elementRef, options]);
};

// GSAP Smooth Navigation Scroll
export const useGSAPSmoothScroll = () => {
  const scrollToSection = (target: string | HTMLElement, offset: number = 0) => {
    gsap.to(window, {
      duration: 1.5,
      scrollTo: {
        y: target,
        offsetY: offset,
      },
      ease: "power3.inOut",
    });
  };

  return { scrollToSection };
};

// GSAP Text Reveal Animation
export const useGSAPTextReveal = (
  elementRef: React.RefObject<HTMLElement>,
  options: {
    delay?: number;
    stagger?: number;
    from?: 'bottom' | 'top' | 'left' | 'right';
  } = {}
) => {
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const {
      delay = 0,
      stagger = 0.05,
      from = 'bottom'
    } = options;

    // Split text into spans
    const text = element.textContent || '';
    element.innerHTML = '';
    
    const chars = text.split('').map(char => {
      const span = document.createElement('span');
      span.textContent = char === ' ' ? '\u00A0' : char;
      span.style.display = 'inline-block';
      element.appendChild(span);
      return span;
    });

    const fromProps: any = { opacity: 0, force3D: true };
    const toProps: any = { opacity: 1, force3D: true };

    switch (from) {
      case 'bottom':
        fromProps.y = 30;
        toProps.y = 0;
        break;
      case 'top':
        fromProps.y = -30;
        toProps.y = 0;
        break;
      case 'left':
        fromProps.x = -30;
        toProps.x = 0;
        break;
      case 'right':
        fromProps.x = 30;
        toProps.x = 0;
        break;
    }

    gsap.set(chars, fromProps);

    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: element,
        start: "top 85%",
        toggleActions: "play none none reverse",
      }
    });

    tl.to(chars, {
      ...toProps,
      duration: 0.6,
      ease: "power3.out",
      stagger: stagger,
      delay: delay,
    });

    return () => {
      tl.kill();
    };
  }, [elementRef, options]);
};

// GSAP Floating Elements Animation
export const useGSAPFloating = (
  elementRef: React.RefObject<HTMLElement>,
  options: {
    amplitude?: number;
    duration?: number;
    delay?: number;
  } = {}
) => {
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const {
      amplitude = 20,
      duration = 4,
      delay = 0
    } = options;

    const tl = gsap.timeline({ repeat: -1, yoyo: true });

    tl.to(element, {
      y: -amplitude,
      rotation: 2,
      duration: duration,
      ease: "power1.inOut",
      delay: delay,
      force3D: true,
    })
    .to(element, {
      y: amplitude,
      rotation: -2,
      duration: duration,
      ease: "power1.inOut",
      force3D: true,
    });

    return () => {
      tl.kill();
    };
  }, [elementRef, options]);
};
