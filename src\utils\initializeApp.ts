
import { initLuxurySmoothScroll } from './gsapLuxuryAnimations';

// This function initializes the application with luxury enhancements
// It runs once when the application starts
export async function initializeApp() {
  const isInitialized = localStorage.getItem("app_initialized");

  // Always initialize GSAP for luxury animations
  initLuxurySmoothScroll();

  // Only run one-time initialization once
  if (!isInitialized) {
    console.log("Initializing Promise Academy luxury experience...");

    try {
      // Initialize luxury theme preferences
      const luxuryPreferences = {
        animations: true,
        smoothScroll: true,
        highPerformance: true,
        theme: 'gold-purple-red'
      };
      localStorage.setItem("luxury_preferences", JSON.stringify(luxuryPreferences));

      // Preload Cormorant Garamond font for better performance
      const fontLink = document.createElement('link');
      fontLink.rel = 'preload';
      fontLink.as = 'font';
      fontLink.type = 'font/woff2';
      fontLink.crossOrigin = 'anonymous';
      document.head.appendChild(fontLink);

      // Mark as initialized
      localStorage.setItem("app_initialized", "true");
      console.log("Luxury experience initialized successfully!");
    } catch (error) {
      console.error("Error during luxury app initialization:", error);
    }
  }
}
