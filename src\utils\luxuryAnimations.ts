import { useEffect, useRef } from 'react';

// Luxury animation utilities
export const createSparkleEffect = (element: HTMLElement) => {
  const sparkle = document.createElement('div');
  sparkle.className = 'absolute w-2 h-2 bg-luxury-gold rounded-full animate-luxury-sparkle pointer-events-none';
  sparkle.style.left = Math.random() * 100 + '%';
  sparkle.style.top = Math.random() * 100 + '%';
  
  element.appendChild(sparkle);
  
  setTimeout(() => {
    sparkle.remove();
  }, 2000);
};

export const createFloatingParticle = (container: HTMLElement, type: 'star' | 'crown' | 'gem' = 'star') => {
  const particle = document.createElement('div');
  particle.className = `absolute pointer-events-none animate-luxury-float`;
  
  const icons = {
    star: '⭐',
    crown: '👑',
    gem: '💎'
  };
  
  particle.innerHTML = icons[type];
  particle.style.fontSize = Math.random() * 20 + 10 + 'px';
  particle.style.left = Math.random() * 100 + '%';
  particle.style.top = Math.random() * 100 + '%';
  particle.style.animationDelay = Math.random() * 2 + 's';
  particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
  
  container.appendChild(particle);
  
  setTimeout(() => {
    particle.remove();
  }, 8000);
};

export const useLuxuryParticles = (containerRef: React.RefObject<HTMLElement>, enabled: boolean = true) => {
  useEffect(() => {
    if (!enabled || !containerRef.current) return;
    
    const container = containerRef.current;
    const particleTypes: ('star' | 'crown' | 'gem')[] = ['star', 'crown', 'gem'];
    
    const createParticle = () => {
      const type = particleTypes[Math.floor(Math.random() * particleTypes.length)];
      createFloatingParticle(container, type);
    };
    
    // Create initial particles
    for (let i = 0; i < 5; i++) {
      setTimeout(createParticle, i * 1000);
    }
    
    // Continue creating particles
    const interval = setInterval(createParticle, 3000);
    
    return () => clearInterval(interval);
  }, [containerRef, enabled]);
};

export const useLuxurySparkles = (elementRef: React.RefObject<HTMLElement>, enabled: boolean = true) => {
  useEffect(() => {
    if (!enabled || !elementRef.current) return;
    
    const element = elementRef.current;
    
    const createSparkle = () => {
      createSparkleEffect(element);
    };
    
    const interval = setInterval(createSparkle, 1500);
    
    return () => clearInterval(interval);
  }, [elementRef, enabled]);
};

export const useLuxuryHover = (elementRef: React.RefObject<HTMLElement>) => {
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;
    
    const handleMouseEnter = () => {
      element.classList.add('animate-luxury-glow');
      createSparkleEffect(element);
    };
    
    const handleMouseLeave = () => {
      element.classList.remove('animate-luxury-glow');
    };
    
    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);
    
    return () => {
      element.removeEventListener('mouseenter', handleMouseEnter);
      element.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [elementRef]);
};

// Intersection Observer for luxury animations
export const useLuxuryIntersectionObserver = (
  elementRef: React.RefObject<HTMLElement>,
  animationClass: string = 'animate-luxury-fade-in'
) => {
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;
    
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add(animationClass);
          }
        });
      },
      { threshold: 0.1 }
    );
    
    observer.observe(element);
    
    return () => observer.disconnect();
  }, [elementRef, animationClass]);
};

// Luxury gradient utilities
export const getLuxuryGradient = (type: 'gold' | 'platinum' | 'purple' | 'emerald' | 'rose-gold' | 'rainbow') => {
  const gradients = {
    gold: 'linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%)',
    platinum: 'linear-gradient(135deg, #E5E4E2 0%, #C0C0C0 50%, #A8A8A8 100%)',
    purple: 'linear-gradient(135deg, #663399 0%, #4B0082 50%, #8A2BE2 100%)',
    emerald: 'linear-gradient(135deg, #50C878 0%, #228B22 50%, #32CD32 100%)',
    'rose-gold': 'linear-gradient(135deg, #E8B4B8 0%, #B76E79 50%, #CD919E 100%)',
    rainbow: 'linear-gradient(135deg, #FFD700 0%, #FF69B4 25%, #00BFFF 50%, #50C878 75%, #E8B4B8 100%)'
  };
  
  return gradients[type];
};

// Luxury text gradient utility
export const getLuxuryTextGradient = (type: 'gold-purple' | 'platinum-blue' | 'emerald-gold' | 'rose-purple') => {
  const gradients = {
    'gold-purple': 'linear-gradient(135deg, #FFD700 0%, #663399 100%)',
    'platinum-blue': 'linear-gradient(135deg, #E5E4E2 0%, #00BFFF 100%)',
    'emerald-gold': 'linear-gradient(135deg, #50C878 0%, #FFD700 100%)',
    'rose-purple': 'linear-gradient(135deg, #E8B4B8 0%, #663399 100%)'
  };
  
  return gradients[type];
};

// Luxury timing functions
export const luxuryEasing = {
  gentle: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  elegant: 'cubic-bezier(0.23, 1, 0.32, 1)',
  premium: 'cubic-bezier(0.165, 0.84, 0.44, 1)',
  royal: 'cubic-bezier(0.19, 1, 0.22, 1)'
};

// Advanced 3D hover effects
export const useLuxury3DHover = (elementRef: React.RefObject<HTMLElement>) => {
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const handleMouseMove = (e: MouseEvent) => {
      const rect = element.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;

      const rotateX = (y - centerY) / centerY * -10;
      const rotateY = (x - centerX) / centerX * 10;

      element.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(20px)`;
    };

    const handleMouseLeave = () => {
      element.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0px)';
    };

    element.addEventListener('mousemove', handleMouseMove);
    element.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      element.removeEventListener('mousemove', handleMouseMove);
      element.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [elementRef]);
};

// Advanced particle system
export const createAdvancedParticleSystem = (container: HTMLElement, config: {
  particleCount?: number;
  particleTypes?: ('star' | 'crown' | 'gem' | 'sparkle' | 'heart')[];
  speed?: number;
  size?: { min: number; max: number };
  colors?: string[];
}) => {
  const {
    particleCount = 20,
    particleTypes = ['star', 'crown', 'gem', 'sparkle'],
    speed = 1,
    size = { min: 10, max: 30 },
    colors = ['#FFD700', '#E5E4E2', '#663399', '#50C878', '#E8B4B8']
  } = config;

  const particles: HTMLElement[] = [];

  for (let i = 0; i < particleCount; i++) {
    const particle = document.createElement('div');
    const type = particleTypes[Math.floor(Math.random() * particleTypes.length)];
    const color = colors[Math.floor(Math.random() * colors.length)];
    const particleSize = Math.random() * (size.max - size.min) + size.min;

    const icons = {
      star: '⭐',
      crown: '👑',
      gem: '💎',
      sparkle: '✨',
      heart: '💖'
    };

    particle.innerHTML = icons[type];
    particle.className = 'absolute pointer-events-none animate-luxury-float';
    particle.style.fontSize = particleSize + 'px';
    particle.style.color = color;
    particle.style.left = Math.random() * 100 + '%';
    particle.style.top = Math.random() * 100 + '%';
    particle.style.animationDelay = Math.random() * 4 + 's';
    particle.style.animationDuration = (Math.random() * 6 + 4) / speed + 's';
    particle.style.opacity = (Math.random() * 0.5 + 0.3).toString();

    container.appendChild(particle);
    particles.push(particle);
  }

  return () => {
    particles.forEach(particle => particle.remove());
  };
};

// Luxury ripple effect
export const createLuxuryRipple = (element: HTMLElement, x: number, y: number) => {
  const ripple = document.createElement('div');
  ripple.className = 'absolute rounded-full pointer-events-none';
  ripple.style.background = 'radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, transparent 70%)';
  ripple.style.transform = 'scale(0)';
  ripple.style.left = x - 50 + 'px';
  ripple.style.top = y - 50 + 'px';
  ripple.style.width = '100px';
  ripple.style.height = '100px';
  ripple.style.animation = 'luxury-ripple 0.6s ease-out';

  element.appendChild(ripple);

  setTimeout(() => {
    ripple.remove();
  }, 600);
};

// Advanced glow effects
export const createAdvancedGlow = (element: HTMLElement, intensity: 'subtle' | 'medium' | 'intense' = 'medium') => {
  const glowIntensities = {
    subtle: '0 0 20px rgba(255, 215, 0, 0.3)',
    medium: '0 0 40px rgba(255, 215, 0, 0.5)',
    intense: '0 0 60px rgba(255, 215, 0, 0.8)'
  };

  element.style.boxShadow = glowIntensities[intensity];
  element.style.transition = 'box-shadow 0.3s ease-out';
};

// Luxury text reveal animation
export const useLuxuryTextReveal = (elementRef: React.RefObject<HTMLElement>, delay: number = 0) => {
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const text = element.textContent || '';
    element.innerHTML = '';

    text.split('').forEach((char, index) => {
      const span = document.createElement('span');
      span.textContent = char === ' ' ? '\u00A0' : char;
      span.style.opacity = '0';
      span.style.transform = 'translateY(20px)';
      span.style.transition = `opacity 0.3s ease-out ${delay + index * 0.05}s, transform 0.3s ease-out ${delay + index * 0.05}s`;
      element.appendChild(span);
    });

    setTimeout(() => {
      element.querySelectorAll('span').forEach(span => {
        span.style.opacity = '1';
        span.style.transform = 'translateY(0)';
      });
    }, 100);
  }, [elementRef, delay]);
};

// Sound effects (enhanced)
export const playLuxurySound = (type: 'click' | 'hover' | 'success' | 'sparkle' | 'chime' | 'whoosh') => {
  if ('AudioContext' in window) {
    const audioContext = new AudioContext();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    const soundConfigs = {
      click: { frequency: 800, duration: 0.1, type: 'sine' as OscillatorType },
      hover: { frequency: 600, duration: 0.05, type: 'sine' as OscillatorType },
      success: { frequency: 1000, duration: 0.2, type: 'triangle' as OscillatorType },
      sparkle: { frequency: 1200, duration: 0.15, type: 'square' as OscillatorType },
      chime: { frequency: 880, duration: 0.3, type: 'sine' as OscillatorType },
      whoosh: { frequency: 200, duration: 0.4, type: 'sawtooth' as OscillatorType }
    };

    const config = soundConfigs[type];
    oscillator.frequency.setValueAtTime(config.frequency, audioContext.currentTime);
    oscillator.type = config.type;

    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + config.duration);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + config.duration);
  }
};
