import { useEffect, useRef, useState, useCallback } from 'react';

// Lazy loading hook for luxury components
export const useLuxuryLazyLoad = (threshold: number = 0.1) => {
  const [isVisible, setIsVisible] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasLoaded) {
          setIsVisible(true);
          setHasLoaded(true);
        }
      },
      { threshold }
    );

    if (elementRef.current) {
      observer.observe(elementRef.current);
    }

    return () => observer.disconnect();
  }, [threshold, hasLoaded]);

  return { elementRef, isVisible, hasLoaded };
};

// Performance-optimized animation system
export const useLuxuryAnimationFrame = (callback: () => void, deps: any[] = []) => {
  const requestRef = useRef<number>();
  const previousTimeRef = useRef<number>();

  const animate = useCallback((time: number) => {
    if (previousTimeRef.current !== undefined) {
      callback();
    }
    previousTimeRef.current = time;
    requestRef.current = requestAnimationFrame(animate);
  }, deps);

  useEffect(() => {
    requestRef.current = requestAnimationFrame(animate);
    return () => {
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
    };
  }, [animate]);
};

// Debounced luxury effects
export const useLuxuryDebounce = <T extends (...args: any[]) => void>(
  callback: T,
  delay: number
): T => {
  const timeoutRef = useRef<NodeJS.Timeout>();

  return useCallback((...args: Parameters<T>) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => callback(...args), delay);
  }, [callback, delay]) as T;
};

// Throttled luxury interactions
export const useLuxuryThrottle = <T extends (...args: any[]) => void>(
  callback: T,
  limit: number
): T => {
  const inThrottle = useRef(false);

  return useCallback((...args: Parameters<T>) => {
    if (!inThrottle.current) {
      callback(...args);
      inThrottle.current = true;
      setTimeout(() => {
        inThrottle.current = false;
      }, limit);
    }
  }, [callback, limit]) as T;
};

// Image optimization utilities for luxury assets
export const getOptimizedImageSrc = (originalSrc: string, quality: 'low' | 'medium' | 'high' = 'high') => {
  // In a real implementation, this would integrate with an image optimization service
  // For now, we'll return the original src
  return originalSrc;
};

export const createPlaceholderDataUrl = (width: number = 100, height: number = 100) => {
  return `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iJHt3aWR0aH0iIGhlaWdodD0iJHtoZWlnaHR9IiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjMzMzMzMzIi8+PHRleHQgeD0iNTAiIHk9IjUwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiM2NjY2NjYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5Mb2FkaW5nLi4uPC90ZXh0Pjwvc3ZnPg==`;
};

// Performance monitoring for luxury effects
export const useLuxuryPerformanceMonitor = () => {
  const [metrics, setMetrics] = useState({
    fps: 60,
    memoryUsage: 0,
    renderTime: 0
  });

  useEffect(() => {
    let frameCount = 0;
    let lastTime = performance.now();
    let animationId: number;

    const measurePerformance = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        
        // Memory usage (if available)
        const memoryInfo = (performance as any).memory;
        const memoryUsage = memoryInfo ? memoryInfo.usedJSHeapSize / 1048576 : 0; // MB
        
        setMetrics({
          fps,
          memoryUsage: Math.round(memoryUsage),
          renderTime: currentTime - lastTime
        });
        
        frameCount = 0;
        lastTime = currentTime;
      }
      
      animationId = requestAnimationFrame(measurePerformance);
    };

    animationId = requestAnimationFrame(measurePerformance);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, []);

  return metrics;
};

// Optimized particle system with performance controls
export const createOptimizedParticleSystem = (
  container: HTMLElement,
  config: {
    maxParticles?: number;
    enablePooling?: boolean;
    performanceMode?: 'high' | 'medium' | 'low';
  } = {}
) => {
  const {
    maxParticles = 50,
    enablePooling = true,
    performanceMode = 'medium'
  } = config;

  const performanceSettings = {
    high: { updateInterval: 16, maxParticles: 50 },
    medium: { updateInterval: 32, maxParticles: 30 },
    low: { updateInterval: 64, maxParticles: 15 }
  };

  const settings = performanceSettings[performanceMode];
  const particles: HTMLElement[] = [];
  const particlePool: HTMLElement[] = [];
  let lastUpdate = 0;

  const createParticle = () => {
    let particle: HTMLElement;

    if (enablePooling && particlePool.length > 0) {
      particle = particlePool.pop()!;
    } else {
      particle = document.createElement('div');
      particle.className = 'absolute pointer-events-none';
      particle.innerHTML = '✨';
    }
    
    particle.style.left = Math.random() * 100 + '%';
    particle.style.top = Math.random() * 100 + '%';
    particle.style.fontSize = (Math.random() * 20 + 10) + 'px';
    particle.style.opacity = (Math.random() * 0.5 + 0.3).toString();
    
    container.appendChild(particle);
    particles.push(particle);
    
    setTimeout(() => {
      const index = particles.indexOf(particle);
      if (index > -1) {
        particles.splice(index, 1);
        container.removeChild(particle);
        
        if (enablePooling && particlePool.length < maxParticles / 2) {
          particlePool.push(particle);
        }
      }
    }, 3000);
  };

  const update = (currentTime: number) => {
    if (currentTime - lastUpdate >= settings.updateInterval) {
      if (particles.length < settings.maxParticles) {
        createParticle();
      }
      lastUpdate = currentTime;
    }
    
    requestAnimationFrame(update);
  };

  requestAnimationFrame(update);

  return () => {
    particles.forEach(particle => {
      if (particle.parentNode) {
        particle.parentNode.removeChild(particle);
      }
    });
    particles.length = 0;
    particlePool.length = 0;
  };
};

// Luxury asset preloader
export const useLuxuryAssetPreloader = (assets: string[]) => {
  const [loadedAssets, setLoadedAssets] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const preloadAsset = (src: string): Promise<void> => {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve();
        img.onerror = () => reject();
        img.src = src;
      });
    };

    const preloadAll = async () => {
      const promises = assets.map(async (asset) => {
        try {
          await preloadAsset(asset);
          setLoadedAssets(prev => new Set([...prev, asset]));
        } catch (error) {
          console.warn(`Failed to preload asset: ${asset}`);
        }
      });

      await Promise.allSettled(promises);
      setIsLoading(false);
    };

    if (assets.length > 0) {
      preloadAll();
    } else {
      setIsLoading(false);
    }
  }, [assets]);

  return { loadedAssets, isLoading, progress: loadedAssets.size / assets.length };
};
