import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { ScrollToPlugin } from 'gsap/ScrollToPlugin';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger, ScrollToPlugin);

// Modern GSAP Animation Configuration for 60fps
export const modernGSAPConfig = {
  duration: 0.8,
  ease: "power2.out",
  stagger: 0.1,
  parallaxStrength: 0.3,
  performance: {
    force3D: true,
    willChange: true,
    autoAlpha: true,
    transformOrigin: "center center",
  }
};

// Initialize modern smooth scroll with performance optimizations
export const initModernSmoothScroll = () => {
  gsap.registerPlugin(ScrollTrigger);

  // Configure GSAP for optimal performance
  gsap.config({
    force3D: true,
    nullTargetWarn: false,
    autoSleep: 60,
    units: { left: "px", top: "px" },
  });

  // ScrollTrigger performance optimizations
  ScrollTrigger.config({
    limitCallbacks: true,
    syncInterval: 120,
    autoRefreshEvents: "visibilitychange,DOMContentLoaded,load,resize",
  });

  ScrollTrigger.defaults({
    toggleActions: "play pause resume reverse",
    scroller: window,
  });

  // Respect reduced motion preferences
  if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
    gsap.globalTimeline.timeScale(0);
    ScrollTrigger.disable();
  }
};

// Modern parallax effect hook
export const useModernParallax = (
  elementRef: React.RefObject<HTMLElement>,
  options: {
    yPercent?: number;
    scale?: number;
    opacity?: number;
    trigger?: string;
    speed?: number;
  } = {}
) => {
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const {
      yPercent = -30,
      scale = 1.05,
      opacity = 1,
      trigger = element,
      speed = 1
    } = options;

    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: trigger,
        start: "top bottom",
        end: "bottom top",
        scrub: speed,
        invalidateOnRefresh: true,
      }
    });

    tl.fromTo(element, 
      {
        yPercent: 0,
        scale: 1,
        opacity: opacity,
        force3D: true,
      },
      {
        yPercent: yPercent,
        scale: scale,
        opacity: opacity * 0.9,
        ease: "none",
        force3D: true,
      }
    );

    return () => {
      tl.kill();
    };
  }, [elementRef, options]);
};

// Modern fade-in animation hook
export const useModernFadeIn = (
  elementRef: React.RefObject<HTMLElement>,
  options: {
    delay?: number;
    duration?: number;
    direction?: 'up' | 'down' | 'left' | 'right' | 'scale' | 'fade';
    distance?: number;
    stagger?: number;
  } = {}
) => {
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const {
      delay = 0,
      duration = modernGSAPConfig.duration,
      direction = 'up',
      distance = 40,
      stagger = 0
    } = options;

    const fromProps: any = { opacity: 0, force3D: true };
    const toProps: any = { opacity: 1, force3D: true };

    switch (direction) {
      case 'up':
        fromProps.y = distance;
        toProps.y = 0;
        break;
      case 'down':
        fromProps.y = -distance;
        toProps.y = 0;
        break;
      case 'left':
        fromProps.x = distance;
        toProps.x = 0;
        break;
      case 'right':
        fromProps.x = -distance;
        toProps.x = 0;
        break;
      case 'scale':
        fromProps.scale = 0.9;
        toProps.scale = 1;
        break;
      case 'fade':
        // Only opacity animation
        break;
    }

    gsap.set(element, fromProps);

    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: element,
        start: "top 80%",
        toggleActions: "play none none reverse",
      }
    });

    if (element.children.length > 1 && stagger > 0) {
      tl.to(element.children, {
        ...toProps,
        duration: duration,
        ease: modernGSAPConfig.ease,
        stagger: stagger,
        delay: delay,
      });
    } else {
      tl.to(element, {
        ...toProps,
        duration: duration,
        ease: modernGSAPConfig.ease,
        delay: delay,
      });
    }

    return () => {
      tl.kill();
    };
  }, [elementRef, options]);
};

// Modern hover animation hook
export const useModernHover = (
  elementRef: React.RefObject<HTMLElement>,
  options: {
    scale?: number;
    y?: number;
    duration?: number;
    shadow?: boolean;
  } = {}
) => {
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const {
      scale = 1.02,
      y = -4,
      duration = 0.3,
      shadow = true
    } = options;

    const hoverProps: any = {
      scale: scale,
      y: y,
      duration: duration,
      ease: "power2.out",
      force3D: true,
    };

    if (shadow) {
      hoverProps.boxShadow = "0 10px 30px rgba(102, 51, 153, 0.2)";
    }

    const handleMouseEnter = () => {
      gsap.to(element, hoverProps);
    };

    const handleMouseLeave = () => {
      gsap.to(element, {
        scale: 1,
        y: 0,
        duration: duration,
        ease: "power2.out",
        boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
        force3D: true,
      });
    };

    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      element.removeEventListener('mouseenter', handleMouseEnter);
      element.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [elementRef, options]);
};

// Modern smooth scroll navigation
export const useModernSmoothScroll = () => {
  const scrollToSection = (target: string | HTMLElement, offset: number = 80) => {
    gsap.to(window, {
      duration: 1.2,
      scrollTo: {
        y: target,
        offsetY: offset,
      },
      ease: "power2.inOut",
    });
  };

  return { scrollToSection };
};

// Modern staggered animation for lists/grids
export const useModernStagger = (
  containerRef: React.RefObject<HTMLElement>,
  options: {
    delay?: number;
    stagger?: number;
    direction?: 'up' | 'down' | 'left' | 'right';
    distance?: number;
  } = {}
) => {
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const {
      delay = 0,
      stagger = 0.1,
      direction = 'up',
      distance = 30
    } = options;

    const children = container.children;
    if (children.length === 0) return;

    const fromProps: any = { opacity: 0, force3D: true };
    const toProps: any = { opacity: 1, force3D: true };

    switch (direction) {
      case 'up':
        fromProps.y = distance;
        toProps.y = 0;
        break;
      case 'down':
        fromProps.y = -distance;
        toProps.y = 0;
        break;
      case 'left':
        fromProps.x = distance;
        toProps.x = 0;
        break;
      case 'right':
        fromProps.x = -distance;
        toProps.x = 0;
        break;
    }

    gsap.set(children, fromProps);

    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: container,
        start: "top 80%",
        toggleActions: "play none none reverse",
      }
    });

    tl.to(children, {
      ...toProps,
      duration: modernGSAPConfig.duration,
      ease: modernGSAPConfig.ease,
      stagger: stagger,
      delay: delay,
    });

    return () => {
      tl.kill();
    };
  }, [containerRef, options]);
};

// Modern floating animation for background elements
export const useModernFloat = (
  elementRef: React.RefObject<HTMLElement>,
  options: {
    amplitude?: number;
    duration?: number;
    delay?: number;
    rotation?: number;
  } = {}
) => {
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const {
      amplitude = 15,
      duration = 6,
      delay = 0,
      rotation = 3
    } = options;

    const tl = gsap.timeline({ repeat: -1, yoyo: true });

    tl.to(element, {
      y: -amplitude,
      rotation: rotation,
      duration: duration,
      ease: "power1.inOut",
      delay: delay,
      force3D: true,
    })
    .to(element, {
      y: amplitude,
      rotation: -rotation,
      duration: duration,
      ease: "power1.inOut",
      force3D: true,
    });

    return () => {
      tl.kill();
    };
  }, [elementRef, options]);
};

// Modern text reveal animation
export const useModernTextReveal = (
  elementRef: React.RefObject<HTMLElement>,
  options: {
    delay?: number;
    stagger?: number;
    direction?: 'up' | 'down';
    splitBy?: 'chars' | 'words' | 'lines';
  } = {}
) => {
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const {
      delay = 0,
      stagger = 0.03,
      direction = 'up',
      splitBy = 'chars'
    } = options;

    const text = element.textContent || '';
    element.innerHTML = '';

    let splits: HTMLElement[] = [];

    if (splitBy === 'chars') {
      splits = text.split('').map(char => {
        const span = document.createElement('span');
        span.textContent = char === ' ' ? '\u00A0' : char;
        span.style.display = 'inline-block';
        element.appendChild(span);
        return span;
      });
    } else if (splitBy === 'words') {
      splits = text.split(' ').map(word => {
        const span = document.createElement('span');
        span.textContent = word;
        span.style.display = 'inline-block';
        span.style.marginRight = '0.25em';
        element.appendChild(span);
        return span;
      });
    }

    const fromProps: any = { opacity: 0, force3D: true };
    const toProps: any = { opacity: 1, force3D: true };

    if (direction === 'up') {
      fromProps.y = 20;
      toProps.y = 0;
    } else {
      fromProps.y = -20;
      toProps.y = 0;
    }

    gsap.set(splits, fromProps);

    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: element,
        start: "top 85%",
        toggleActions: "play none none reverse",
      }
    });

    tl.to(splits, {
      ...toProps,
      duration: 0.6,
      ease: "power2.out",
      stagger: stagger,
      delay: delay,
    });

    return () => {
      tl.kill();
    };
  }, [elementRef, options]);
};

// Modern scroll progress indicator
export const useModernScrollProgress = (
  elementRef: React.RefObject<HTMLElement>
) => {
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    gsap.set(element, { scaleX: 0, transformOrigin: "left center" });

    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: "body",
        start: "top top",
        end: "bottom bottom",
        scrub: 1,
      }
    });

    tl.to(element, {
      scaleX: 1,
      ease: "none",
    });

    return () => {
      tl.kill();
    };
  }, [elementRef]);
};

// Modern magnetic effect for buttons
export const useModernMagnetic = (
  elementRef: React.RefObject<HTMLElement>,
  options: {
    strength?: number;
    speed?: number;
  } = {}
) => {
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const { strength = 0.3, speed = 0.3 } = options;

    const handleMouseMove = (e: MouseEvent) => {
      const rect = element.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;

      const deltaX = (e.clientX - centerX) * strength;
      const deltaY = (e.clientY - centerY) * strength;

      gsap.to(element, {
        x: deltaX,
        y: deltaY,
        duration: speed,
        ease: "power2.out",
      });
    };

    const handleMouseLeave = () => {
      gsap.to(element, {
        x: 0,
        y: 0,
        duration: speed,
        ease: "power2.out",
      });
    };

    element.addEventListener('mousemove', handleMouseMove);
    element.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      element.removeEventListener('mousemove', handleMouseMove);
      element.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [elementRef, options]);
};
