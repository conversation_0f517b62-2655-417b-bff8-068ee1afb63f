
import { toast } from "@/hooks/use-toast";

/**
 * Display a toast notification for database operations
 */
export const showDatabaseStatus = (
  success: boolean, 
  operation: string, 
  error?: any
) => {
  if (success) {
    toast({
      title: "Success",
      description: `${operation} completed successfully.`,
    });
  } else {
    console.error(`Error during ${operation}:`, error);
    toast({
      title: "Error",
      description: error?.message || `Failed to ${operation}.`,
      variant: "destructive",
    });
  }
};

/**
 * Log operation status with detailed information
 */
export const logOperationStatus = (
  operation: string, 
  status: string, 
  details?: any
) => {
  console.log(`[${operation}] ${status}`, details || '');
};
