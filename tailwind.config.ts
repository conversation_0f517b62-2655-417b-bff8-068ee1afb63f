
import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				// Modern Design System Colors - Purple, Gold, Red Theme
				'modern': {
					'purple': {
						'50': '#faf5ff',
						'100': '#f3e8ff',
						'200': '#e9d5ff',
						'300': '#d8b4fe',
						'400': '#c084fc',
						'500': '#a855f7',
						'600': '#9333ea',
						'700': '#7c3aed',
						'800': '#6b21a8',
						'900': '#581c87',
						'950': '#3b0764',
						DEFAULT: '#663399'
					},
					'gold': {
						'50': '#fffef7',
						'100': '#fffbeb',
						'200': '#fef3c7',
						'300': '#fde68a',
						'400': '#fcd34d',
						'500': '#f59e0b',
						'600': '#d97706',
						'700': '#b45309',
						'800': '#92400e',
						'900': '#78350f',
						'950': '#451a03',
						DEFAULT: '#FFD700'
					},
					'red': {
						'50': '#fef2f2',
						'100': '#fee2e2',
						'200': '#fecaca',
						'300': '#fca5a5',
						'400': '#f87171',
						'500': '#ef4444',
						'600': '#dc2626',
						'700': '#b91c1c',
						'800': '#991b1b',
						'900': '#7f1d1d',
						'950': '#450a0a',
						DEFAULT: '#DC143C'
					},
					'neutral': {
						'50': '#f9fafb',
						'100': '#f3f4f6',
						'200': '#e5e7eb',
						'300': '#d1d5db',
						'400': '#9ca3af',
						'500': '#6b7280',
						'600': '#4b5563',
						'700': '#374151',
						'800': '#1f2937',
						'900': '#111827',
						'950': '#030712',
						white: '#ffffff',
						black: '#000000'
					}
				},
				// Luxury Elite Color Palette
				'luxury-gold': {
					'50': '#fffef7',
					'100': '#fffbeb',
					'200': '#fef3c7',
					'300': '#fde68a',
					'400': '#fcd34d',
					'500': '#f59e0b',
					'600': '#d97706',
					'700': '#b45309',
					'800': '#92400e',
					'900': '#78350f',
					'950': '#451a03',
					DEFAULT: '#FFD700',
					dark: '#B8860B'
				},
				'luxury-platinum': {
					'50': '#f8f9fa',
					'100': '#f1f3f4',
					'200': '#e8eaed',
					'300': '#dadce0',
					'400': '#bdc1c6',
					'500': '#9aa0a6',
					'600': '#80868b',
					'700': '#5f6368',
					'800': '#3c4043',
					'900': '#202124',
					DEFAULT: '#E5E4E2',
					dark: '#C0C0C0'
				},
				'luxury-purple': {
					'50': '#faf5ff',
					'100': '#f3e8ff',
					'200': '#e9d5ff',
					'300': '#d8b4fe',
					'400': '#c084fc',
					'500': '#a855f7',
					'600': '#9333ea',
					'700': '#7c3aed',
					'800': '#6b21a8',
					'900': '#581c87',
					'950': '#3b0764',
					DEFAULT: '#663399',
					dark: '#4B0082'
				},
				'luxury-emerald': {
					'50': '#ecfdf5',
					'100': '#d1fae5',
					'200': '#a7f3d0',
					'300': '#6ee7b7',
					'400': '#34d399',
					'500': '#10b981',
					'600': '#059669',
					'700': '#047857',
					'800': '#065f46',
					'900': '#064e3b',
					'950': '#022c22',
					DEFAULT: '#50C878',
					dark: '#228B22'
				},
				'luxury-rose-gold': {
					'50': '#fdf2f8',
					'100': '#fce7f3',
					'200': '#fbcfe8',
					'300': '#f9a8d4',
					'400': '#f472b6',
					'500': '#ec4899',
					'600': '#db2777',
					'700': '#be185d',
					'800': '#9d174d',
					'900': '#831843',
					'950': '#500724',
					DEFAULT: '#E8B4B8',
					dark: '#B76E79'
				},
				'luxury-red': {
					'50': '#fef2f2',
					'100': '#fee2e2',
					'200': '#fecaca',
					'300': '#fca5a5',
					'400': '#f87171',
					'500': '#ef4444',
					'600': '#dc2626',
					'700': '#b91c1c',
					'800': '#991b1b',
					'900': '#7f1d1d',
					'950': '#450a0a',
					DEFAULT: '#DC143C',
					dark: '#B22222'
				},
				'luxury-navy': {
					'50': '#f0f4ff',
					'100': '#e0e7ff',
					'200': '#c7d2fe',
					'300': '#a5b4fc',
					'400': '#818cf8',
					'500': '#6366f1',
					'600': '#4f46e5',
					'700': '#4338ca',
					'800': '#3730a3',
					'900': '#312e81',
					'950': '#1e1b4b',
					DEFAULT: '#1B1B3A',
					dark: '#0F0F23'
				},
				// Playful Accent Colors for Children
				'playful-pink': {
					DEFAULT: '#FF69B4',
					light: '#FFB6C1',
					dark: '#C71585'
				},
				'playful-blue': {
					DEFAULT: '#00BFFF',
					light: '#87CEEB',
					dark: '#0080FF'
				},
				'playful-yellow': {
					DEFAULT: '#FFD700',
					light: '#FFFF99',
					dark: '#DAA520'
				},
				'playful-coral': {
					DEFAULT: '#FF7F50',
					light: '#FFA07A',
					dark: '#FF6347'
				},
				'playful-mint': {
					DEFAULT: '#98FB98',
					light: '#AFEEEE',
					dark: '#00FA9A'
				},
				// Promise Academy custom colors
				'promise': {
					'50': '#eefaff',
					'100': '#dcf5ff',
					'200': '#b2eeff',
					'300': '#6ce2ff',
					'400': '#21d0fc',
					'500': '#03b8e5',
					'600': '#0094c1',
					'700': '#00769c',
					'800': '#015f7d',
					'900': '#063c4f',
					'950': '#042a38',
					'foreground': '#ffffff'
				},
				'meadow': {
					'50': '#eafce8',
					'100': '#d1f7d1',
					'200': '#a4eca5',
					'300': '#6ed971',
					'400': '#42bf48',
					'500': '#28a12f',
					'600': '#1b8424',
					'700': '#186922',
					'800': '#17531f',
					'900': '#15451c',
					'950': '#06260c',
					'foreground': '#ffffff'
				},
				'sunlight': {
					'50': '#fdffe7',
					'100': '#f8ffc1',
					'200': '#f1fd86',
					'300': '#e9f842',
					'400': '#daed0f',
					'500': '#bed406',
					'600': '#9ba705',
					'700': '#767f0a',
					'800': '#636810',
					'900': '#545911',
					'950': '#2c2f03',
					'foreground': '#000000'
				},
				'peach': {
					'50': '#fff4e8',
					'100': '#ffe5ca',
					'200': '#ffc999',
					'300': '#fea35b',
					'400': '#fc7f2c',
					'500': '#fb5f0d',
					'600': '#e84a06',
					'700': '#c13508',
					'800': '#992c0e',
					'900': '#7c280f',
					'950': '#431204',
					'foreground': '#ffffff'
				},
				'sidebar': {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				}
			},
			fontFamily: {
				// Modern Design System Fonts
				'heading': ['Montserrat', 'sans-serif'],
				'body': ['Inter', 'sans-serif'],
				// Legacy fonts (keeping for compatibility)
				'cormorant': ['"Cormorant Garamond"', 'serif'],
				'playfair': ['"Playfair Display"', 'serif'],
				'montserrat': ['Montserrat', 'sans-serif'],
				'inter': ['Inter', 'sans-serif'],
				'fredoka': ['"Fredoka One"', 'cursive'],
				'quicksand': ['Quicksand', 'sans-serif'],
				'nunito': ['Nunito', 'sans-serif'],
				'comic-neue': ['"Comic Neue"', 'cursive']
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			spacing: {
				// 8px grid system for modern design
				'grid-1': '8px',
				'grid-2': '16px',
				'grid-3': '24px',
				'grid-4': '32px',
				'grid-5': '40px',
				'grid-6': '48px',
				'grid-8': '64px',
				'grid-10': '80px',
				'grid-12': '96px',
				'grid-16': '128px',
				'grid-20': '160px',
				'grid-24': '192px',
				'grid-32': '256px'
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				},
				// Modern Animation Keyframes
				'fade-in': {
					'0%': { opacity: '0' },
					'100%': { opacity: '1' }
				},
				'fade-in-up': {
					'0%': { opacity: '0', transform: 'translateY(20px)' },
					'100%': { opacity: '1', transform: 'translateY(0)' }
				},
				'fade-in-down': {
					'0%': { opacity: '0', transform: 'translateY(-20px)' },
					'100%': { opacity: '1', transform: 'translateY(0)' }
				},
				'slide-in-left': {
					'0%': { opacity: '0', transform: 'translateX(-20px)' },
					'100%': { opacity: '1', transform: 'translateX(0)' }
				},
				'slide-in-right': {
					'0%': { opacity: '0', transform: 'translateX(20px)' },
					'100%': { opacity: '1', transform: 'translateX(0)' }
				},
				'scale-in': {
					'0%': { opacity: '0', transform: 'scale(0.95)' },
					'100%': { opacity: '1', transform: 'scale(1)' }
				},
				'float-gentle': {
					'0%, 100%': { transform: 'translateY(0)' },
					'50%': { transform: 'translateY(-8px)' }
				},
				'pulse-glow': {
					'0%, 100%': { boxShadow: '0 0 5px rgba(102, 51, 153, 0.3)' },
					'50%': { boxShadow: '0 0 20px rgba(102, 51, 153, 0.6)' }
				},
				// Luxury Animation Keyframes
				'sparkle': {
					'0%, 100%': { opacity: '0', transform: 'scale(0.8)' },
					'50%': { opacity: '1', transform: 'scale(1.2)' }
				},
				'glow': {
					'0%, 100%': { boxShadow: '0 0 5px rgba(255, 215, 0, 0.3)' },
					'50%': { boxShadow: '0 0 20px rgba(255, 215, 0, 0.8)' }
				},
				'luxury-float': {
					'0%, 100%': { transform: 'translateY(0) rotate(0deg)' },
					'33%': { transform: 'translateY(-8px) rotate(1deg)' },
					'66%': { transform: 'translateY(-4px) rotate(-1deg)' }
				},
				'fade-in-up': {
					'0%': { opacity: '0', transform: 'translateY(30px)' },
					'100%': { opacity: '1', transform: 'translateY(0)' }
				},
				'slide-in-right': {
					'0%': { opacity: '0', transform: 'translateX(30px)' },
					'100%': { opacity: '1', transform: 'translateX(0)' }
				},
				'scale-in': {
					'0%': { opacity: '0', transform: 'scale(0.9)' },
					'100%': { opacity: '1', transform: 'scale(1)' }
				},
				'shimmer': {
					'0%': { backgroundPosition: '-200% 0' },
					'100%': { backgroundPosition: '200% 0' }
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				// Modern Animations
				'fade-in': 'fade-in 0.5s ease-out',
				'fade-in-up': 'fade-in-up 0.6s ease-out',
				'fade-in-down': 'fade-in-down 0.6s ease-out',
				'slide-in-left': 'slide-in-left 0.6s ease-out',
				'slide-in-right': 'slide-in-right 0.6s ease-out',
				'scale-in': 'scale-in 0.4s ease-out',
				'float-gentle': 'float-gentle 4s ease-in-out infinite',
				'pulse-glow': 'pulse-glow 2s ease-in-out infinite',
				// Legacy animations (keeping for compatibility)
				'float': 'float 6s ease-in-out infinite',
				'bounce-subtle': 'bounce-subtle 2s ease-in-out infinite',
				'sparkle': 'sparkle 2s ease-in-out infinite',
				'glow': 'glow 3s ease-in-out infinite',
				'luxury-float': 'luxury-float 8s ease-in-out infinite',
				'shimmer': 'shimmer 2s linear infinite'
			},
			boxShadow: {
				// Modern Design System Shadows
				'modern-sm': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
				'modern': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
				'modern-md': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
				'modern-lg': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
				'modern-xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
				'modern-card': '0 4px 6px -1px rgba(102, 51, 153, 0.1), 0 2px 4px -1px rgba(102, 51, 153, 0.06)',
				'modern-button': '0 4px 14px 0 rgba(102, 51, 153, 0.15)',
				'modern-glow-purple': '0 0 20px rgba(102, 51, 153, 0.3)',
				'modern-glow-gold': '0 0 20px rgba(255, 215, 0, 0.3)',
				'modern-glow-red': '0 0 20px rgba(220, 20, 60, 0.3)',
				'modern-glass': '0 8px 32px rgba(31, 38, 135, 0.37)',
				// Legacy shadows (keeping for compatibility)
				'soft': '0 4px 15px rgba(0, 0, 0, 0.05)',
				'card': '0 10px 25px -5px rgba(0, 0, 0, 0.05)',
				'button': '0 4px 10px rgba(0, 0, 0, 0.1)',
				'luxury': '0 8px 32px rgba(255, 215, 0, 0.15)',
				'luxury-gold': '0 8px 32px rgba(255, 215, 0, 0.25)',
				'luxury-purple': '0 8px 32px rgba(102, 51, 153, 0.25)',
				'luxury-red': '0 8px 32px rgba(220, 20, 60, 0.25)',
				'luxury-emerald': '0 4px 20px rgba(80, 200, 120, 0.3)',
				'luxury-rose': '0 4px 20px rgba(232, 180, 184, 0.3)',
				'glass': '0 8px 32px rgba(31, 38, 135, 0.37)',
				'glow-gold': '0 0 20px rgba(255, 215, 0, 0.5)',
				'glow-purple': '0 0 20px rgba(102, 51, 153, 0.5)',
				'glow-red': '0 0 20px rgba(220, 20, 60, 0.5)'
			},
			backgroundImage: {
				// Modern Design System Gradients
				'modern-hero': 'linear-gradient(135deg, #663399 0%, #FFD700 50%, #DC143C 100%)',
				'modern-purple': 'linear-gradient(135deg, #663399 0%, #9333ea 100%)',
				'modern-gold': 'linear-gradient(135deg, #FFD700 0%, #f59e0b 100%)',
				'modern-red': 'linear-gradient(135deg, #DC143C 0%, #ef4444 100%)',
				'modern-card': 'linear-gradient(145deg, rgba(102,51,153,0.05) 0%, rgba(255,215,0,0.05) 100%)',
				'modern-button-primary': 'linear-gradient(90deg, #FFD700 0%, #f59e0b 100%)',
				'modern-button-secondary': 'linear-gradient(90deg, #663399 0%, #9333ea 100%)',
				'modern-button-accent': 'linear-gradient(90deg, #DC143C 0%, #ef4444 100%)',
				'modern-glass': 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
				// Legacy gradients (keeping for compatibility)
				'luxury-gold': 'linear-gradient(135deg, #FFD700 0%, #FFA500 50%, #FF8C00 100%)',
				'luxury-platinum': 'linear-gradient(135deg, #E5E4E2 0%, #C0C0C0 50%, #A8A8A8 100%)',
				'luxury-purple': 'linear-gradient(135deg, #663399 0%, #4B0082 50%, #8A2BE2 100%)',
				'luxury-red': 'linear-gradient(135deg, #DC143C 0%, #B22222 50%, #8B0000 100%)',
				'luxury-emerald': 'linear-gradient(135deg, #50C878 0%, #228B22 50%, #32CD32 100%)',
				'luxury-rose-gold': 'linear-gradient(135deg, #E8B4B8 0%, #B76E79 50%, #CD919E 100%)',
				'hero-luxury': 'linear-gradient(135deg, #FFD700 0%, #663399 50%, #DC143C 100%)',
				'card-luxury': 'linear-gradient(145deg, rgba(255,215,0,0.1) 0%, rgba(102,51,153,0.1) 100%)',
				'button-luxury': 'linear-gradient(90deg, #FFD700 0%, #DC143C 100%)',
				'luxury-rainbow': 'linear-gradient(135deg, #FFD700 0%, #FF69B4 25%, #00BFFF 50%, #50C878 75%, #E8B4B8 100%)',
				'shimmer': 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent)'
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
