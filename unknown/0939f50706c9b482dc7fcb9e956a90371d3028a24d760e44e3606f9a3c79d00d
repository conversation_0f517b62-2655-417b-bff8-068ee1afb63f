
import { Check<PERSON>ir<PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

const About = () => {
  return (
    <section id="about" className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800">About Promise Academy</h2>
          <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
            Where young minds flourish in a nurturing environment.
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="order-2 lg:order-1">
            <h3 className="text-2xl font-bold text-promise-700 mb-4">Our Philosophy</h3>
            <p className="text-gray-600 mb-6">
              At Promise Academy, we believe that every child deserves a strong foundation for lifelong learning. Our approach combines progressive educational methods with traditional values, creating an environment where children feel safe to explore, create, and grow.
            </p>
            
            <h3 className="text-2xl font-bold text-promise-700 mb-4">Our History</h3>
            <p className="text-gray-600 mb-6">
              Founded in 2010 by a team of passionate educators, Promise Academy has grown from a small preschool serving 15 children to a comprehensive early childhood center with multiple age-appropriate programs serving over 120 families in our community.
            </p>
            
            <div className="space-y-3 mb-6">
              <div className="flex items-start">
                <CheckCircle className="h-5 w-5 text-promise-500 mr-3 mt-1 flex-shrink-0" />
                <p className="text-gray-600">Experienced, qualified teachers with early childhood education certifications</p>
              </div>
              <div className="flex items-start">
                <CheckCircle className="h-5 w-5 text-promise-500 mr-3 mt-1 flex-shrink-0" />
                <p className="text-gray-600">Low student-to-teacher ratios ensuring personalized attention</p>
              </div>
              <div className="flex items-start">
                <CheckCircle className="h-5 w-5 text-promise-500 mr-3 mt-1 flex-shrink-0" />
                <p className="text-gray-600">Purpose-built facilities designed with children's safety and development in mind</p>
              </div>
            </div>
            
            <Button className="bg-meadow-500 hover:bg-meadow-600">
              Learn More About Our Approach
            </Button>
          </div>
          
          <div className="order-1 lg:order-2">
            <div className="relative rounded-xl overflow-hidden aspect-square max-w-md mx-auto">
              <img 
                src="https://images.unsplash.com/photo-1505377059067-e285a7bac49b?ixlib=rb-1.2.1&auto=format&fit=crop&q=80&w=1000&h=1000" 
                alt="Children learning in classroom" 
                className="object-cover w-full h-full rounded-xl shadow-soft"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
