
import { useState } from "react";
import { <PERSON><PERSON>, Ava<PERSON><PERSON><PERSON>back, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar, Check, Clock, Search, UserCheck, UserX } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import DashboardLayout from "../DashboardLayout";

// Sample data for classroom students
const classroomStudents = [
  {
    id: "STU001",
    name: "<PERSON>",
    avatar: "ET",
    age: "4",
    attendance: {
      "2025-05-17": "present",
      "2025-05-16": "present",
      "2025-05-15": "present",
      "2025-05-14": "absent",
      "2025-05-13": "present",
    }
  },
  {
    id: "STU002",
    name: "Liam Johnson",
    avatar: "LJ",
    age: "4",
    attendance: {
      "2025-05-17": "present",
      "2025-05-16": "absent",
      "2025-05-15": "present",
      "2025-05-14": "present",
      "2025-05-13": "present",
    }
  },
  {
    id: "STU003",
    name: "Olivia Davis",
    avatar: "OD",
    age: "4",
    attendance: {
      "2025-05-17": "late",
      "2025-05-16": "present",
      "2025-05-15": "present",
      "2025-05-14": "present",
      "2025-05-13": "present",
    }
  },
  {
    id: "STU004",
    name: "Noah Brown",
    avatar: "NB",
    age: "4",
    attendance: {
      "2025-05-17": "absent",
      "2025-05-16": "present",
      "2025-05-15": "absent",
      "2025-05-14": "present",
      "2025-05-13": "present",
    }
  },
  {
    id: "STU005",
    name: "Ava Martinez",
    avatar: "AM",
    age: "4",
    attendance: {
      "2025-05-17": "present",
      "2025-05-16": "present",
      "2025-05-15": "present",
      "2025-05-14": "present",
      "2025-05-13": "present",
    }
  },
];

// Sample classroom data
const classroomData = {
  id: "CLASS001",
  name: "Pre-K (4 years)",
  teacher: "Ms. Davis",
  students: classroomStudents,
  totalStudents: classroomStudents.length,
};

const ClassroomManagement = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const { toast } = useToast();
  const [attendanceData, setAttendanceData] = useState(
    classroomStudents.map(student => ({
      id: student.id,
      name: student.name,
      avatar: student.avatar,
      status: student.attendance[selectedDate] || "unknown"
    }))
  );

  // Filter students based on search term
  const filteredStudents = attendanceData.filter(student =>
    student.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAttendanceChange = (studentId, status) => {
    setAttendanceData(prev => prev.map(student => 
      student.id === studentId ? { ...student, status } : student
    ));
  };

  const submitAttendance = () => {
    // Here you would typically send this data to the backend
    toast({
      title: "Attendance Submitted",
      description: `Attendance for ${selectedDate} has been recorded.`,
    });
  };

  const handleDateChange = (e) => {
    const newDate = e.target.value;
    setSelectedDate(newDate);
    
    // Update attendance data for the new date
    setAttendanceData(classroomStudents.map(student => ({
      id: student.id,
      name: student.name,
      avatar: student.avatar,
      status: student.attendance[newDate] || "unknown"
    })));
  };

  return (
    <DashboardLayout role="teacher" title="Classroom Management">
      <div className="space-y-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div>
              <CardTitle className="text-2xl font-bold">{classroomData.name}</CardTitle>
              <CardDescription>Teacher: {classroomData.teacher}</CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <div className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-semibold">
                {classroomData.totalStudents} Students
              </div>
            </div>
          </CardHeader>
        </Card>

        <Tabs defaultValue="attendance">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="attendance">Daily Attendance</TabsTrigger>
            <TabsTrigger value="students">Student List</TabsTrigger>
          </TabsList>
          
          <TabsContent value="attendance" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Daily Attendance</CardTitle>
                <CardDescription>Record attendance for your class</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="attendance-date">Select Date</Label>
                    <Input 
                      id="attendance-date" 
                      type="date" 
                      value={selectedDate} 
                      onChange={handleDateChange} 
                      className="w-[180px]"
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                      <span className="text-xs">Present</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                      <span className="text-xs">Late</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      <span className="text-xs">Absent</span>
                    </div>
                  </div>
                </div>

                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search students..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Student</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredStudents.map((student) => (
                        <TableRow key={student.id}>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Avatar className="h-8 w-8">
                                <AvatarImage src={`https://ui-avatars.com/api/?name=${student.avatar}&background=random`} />
                                <AvatarFallback>{student.avatar}</AvatarFallback>
                              </Avatar>
                              <span className="font-medium">{student.name}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Select 
                              value={student.status} 
                              onValueChange={(value) => handleAttendanceChange(student.id, value)}
                            >
                              <SelectTrigger className="w-[130px]">
                                <SelectValue>
                                  {student.status === "present" && "Present"}
                                  {student.status === "late" && "Late"}
                                  {student.status === "absent" && "Absent"}
                                  {student.status === "unknown" && "Not Recorded"}
                                </SelectValue>
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="present">Present</SelectItem>
                                <SelectItem value="late">Late</SelectItem>
                                <SelectItem value="absent">Absent</SelectItem>
                              </SelectContent>
                            </Select>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end space-x-2">
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => handleAttendanceChange(student.id, "present")}
                                className="h-8 w-8 bg-green-50 hover:bg-green-100 border-green-200"
                              >
                                <UserCheck className="h-4 w-4 text-green-600" />
                              </Button>
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => handleAttendanceChange(student.id, "absent")}
                                className="h-8 w-8 bg-red-50 hover:bg-red-100 border-red-200"
                              >
                                <UserX className="h-4 w-4 text-red-600" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline">Reset</Button>
                <Button onClick={submitAttendance}>Submit Attendance</Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="students" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Student List</CardTitle>
                <CardDescription>View all students in your class</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search students..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Student Name</TableHead>
                        <TableHead>ID</TableHead>
                        <TableHead>Age</TableHead>
                        <TableHead>Attendance Rate</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {classroomStudents
                        .filter(student => student.name.toLowerCase().includes(searchTerm.toLowerCase()))
                        .map((student) => {
                          // Calculate attendance rate
                          const attendanceEntries = Object.values(student.attendance);
                          const presentCount = attendanceEntries.filter(status => status === "present" || status === "late").length;
                          const attendanceRate = (presentCount / attendanceEntries.length) * 100;
                          
                          return (
                            <TableRow key={student.id}>
                              <TableCell>
                                <div className="flex items-center space-x-2">
                                  <Avatar className="h-8 w-8">
                                    <AvatarImage src={`https://ui-avatars.com/api/?name=${student.avatar}&background=random`} />
                                    <AvatarFallback>{student.avatar}</AvatarFallback>
                                  </Avatar>
                                  <span className="font-medium">{student.name}</span>
                                </div>
                              </TableCell>
                              <TableCell>{student.id}</TableCell>
                              <TableCell>{student.age} years</TableCell>
                              <TableCell>
                                <div className="flex items-center space-x-2">
                                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                                    <div 
                                      className="bg-promise-600 h-2.5 rounded-full" 
                                      style={{ width: `${attendanceRate}%` }}
                                    ></div>
                                  </div>
                                  <span className="text-xs font-medium">{attendanceRate.toFixed(0)}%</span>
                                </div>
                              </TableCell>
                              <TableCell className="text-right">
                                <Button variant="ghost" size="sm">
                                  View Details
                                </Button>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default ClassroomManagement;
