
import { useState } from "react";
import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Book, Check, FileText, Pencil, Search, Save } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import DashboardLayout from "../DashboardLayout";

// Sample subjects
const subjects = [
  { id: "sub1", name: "Language & Literacy" },
  { id: "sub2", name: "Mathematics" },
  { id: "sub3", name: "Science & Discovery" },
  { id: "sub4", name: "Social Development" },
  { id: "sub5", name: "Fine Motor Skills" },
];

// Sample grading periods
const gradingPeriods = [
  { id: "q1", name: "Q1 (Aug-Oct)" },
  { id: "q2", name: "Q2 (Nov-Jan)" },
  { id: "q3", name: "Q3 (Feb-Apr)" },
  { id: "q4", name: "Q4 (May-Jul)" },
];

// Sample students with grades
const studentsWithGrades = [
  {
    id: "STU001",
    name: "Emma Thompson",
    avatar: "ET",
    grades: {
      "sub1": { q1: 92, q2: 88, q3: 90, q4: null },
      "sub2": { q1: 85, q2: 87, q3: 89, q4: null },
      "sub3": { q1: 90, q2: 92, q3: 94, q4: null },
      "sub4": { q1: 95, q2: 93, q3: 96, q4: null },
      "sub5": { q1: 88, q2: 89, q3: 91, q4: null },
    }
  },
  {
    id: "STU002",
    name: "Liam Johnson",
    avatar: "LJ",
    grades: {
      "sub1": { q1: 87, q2: 85, q3: 86, q4: null },
      "sub2": { q1: 90, q2: 92, q3: 91, q4: null },
      "sub3": { q1: 84, q2: 86, q3: 88, q4: null },
      "sub4": { q1: 92, q2: 90, q3: 93, q4: null },
      "sub5": { q1: 89, q2: 88, q3: 90, q4: null },
    }
  },
  {
    id: "STU003",
    name: "Olivia Davis",
    avatar: "OD",
    grades: {
      "sub1": { q1: 94, q2: 93, q3: 95, q4: null },
      "sub2": { q1: 88, q2: 90, q3: 92, q4: null },
      "sub3": { q1: 91, q2: 93, q3: 95, q4: null },
      "sub4": { q1: 89, q2: 91, q3: 93, q4: null },
      "sub5": { q1: 92, q2: 94, q3: 96, q4: null },
    }
  },
  {
    id: "STU004",
    name: "Noah Brown",
    avatar: "NB",
    grades: {
      "sub1": { q1: 83, q2: 85, q3: 87, q4: null },
      "sub2": { q1: 81, q2: 83, q3: 85, q4: null },
      "sub3": { q1: 79, q2: 82, q3: 84, q4: null },
      "sub4": { q1: 85, q2: 87, q3: 89, q4: null },
      "sub5": { q1: 80, q2: 82, q3: 84, q4: null },
    }
  },
  {
    id: "STU005",
    name: "Ava Martinez",
    avatar: "AM",
    grades: {
      "sub1": { q1: 96, q2: 97, q3: 98, q4: null },
      "sub2": { q1: 95, q2: 96, q3: 97, q4: null },
      "sub3": { q1: 93, q2: 94, q3: 95, q4: null },
      "sub4": { q1: 97, q2: 98, q3: 99, q4: null },
      "sub5": { q1: 94, q2: 95, q3: 96, q4: null },
    }
  },
];

// Classroom information
const classroomInfo = {
  id: "CLASS001",
  name: "Pre-K (4 years)",
  teacher: "Ms. Davis",
  students: studentsWithGrades,
};

const GradingManagement = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSubject, setSelectedSubject] = useState(subjects[0].id);
  const [selectedPeriod, setSelectedPeriod] = useState(gradingPeriods[2].id);
  const [grades, setGrades] = useState(
    studentsWithGrades.map(student => ({
      id: student.id,
      name: student.name,
      avatar: student.avatar,
      grade: student.grades[selectedSubject][selectedPeriod]
    }))
  );
  const { toast } = useToast();

  // Filter students based on search term
  const filteredStudents = studentsWithGrades.filter(student =>
    student.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleGradeChange = (studentId, newGrade) => {
    // Ensure grade is between 0 and 100
    const validGrade = Math.min(Math.max(parseInt(newGrade) || 0, 0), 100);
    
    setGrades(prev => prev.map(student => 
      student.id === studentId ? { ...student, grade: validGrade } : student
    ));
  };

  const handleSubjectChange = (newSubject) => {
    setSelectedSubject(newSubject);
    setGrades(studentsWithGrades.map(student => ({
      id: student.id,
      name: student.name,
      avatar: student.avatar,
      grade: student.grades[newSubject][selectedPeriod]
    })));
  };

  const handlePeriodChange = (newPeriod) => {
    setSelectedPeriod(newPeriod);
    setGrades(studentsWithGrades.map(student => ({
      id: student.id,
      name: student.name,
      avatar: student.avatar,
      grade: student.grades[selectedSubject][newPeriod]
    })));
  };

  const submitGrades = () => {
    toast({
      title: "Grades Submitted",
      description: `Grades for ${subjects.find(s => s.id === selectedSubject).name} - ${gradingPeriods.find(p => p.id === selectedPeriod).name} have been recorded.`,
    });
  };

  // Calculate class average for selected subject and period
  const classAverage = () => {
    const validGrades = grades.filter(s => s.grade !== null).map(s => s.grade);
    if (validGrades.length === 0) return 0;
    return validGrades.reduce((sum, grade) => sum + grade, 0) / validGrades.length;
  };

  // Helper function to determine grade letter and color
  const getGradeInfo = (score) => {
    if (score === null) return { letter: 'N/A', color: 'text-gray-500' };
    if (score >= 90) return { letter: 'A', color: 'text-green-600' };
    if (score >= 80) return { letter: 'B', color: 'text-blue-600' };
    if (score >= 70) return { letter: 'C', color: 'text-yellow-600' };
    if (score >= 60) return { letter: 'D', color: 'text-orange-600' };
    return { letter: 'F', color: 'text-red-600' };
  };

  return (
    <DashboardLayout role="teacher" title="Grade Management">
      <div className="space-y-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div>
              <CardTitle className="text-2xl font-bold">{classroomInfo.name}</CardTitle>
              <CardDescription>Teacher: {classroomInfo.teacher}</CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-1 bg-promise-100 text-promise-800 px-3 py-1 rounded-full">
                <Book className="h-4 w-4" />
                <span className="text-xs font-semibold">Grade Management</span>
              </div>
            </div>
          </CardHeader>
        </Card>

        <Tabs defaultValue="gradeEntry">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="gradeEntry">Grade Entry</TabsTrigger>
            <TabsTrigger value="reports">Grade Reports</TabsTrigger>
          </TabsList>
          
          <TabsContent value="gradeEntry" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Student Grade Entry</CardTitle>
                <CardDescription>Record grades for your class</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="space-y-1">
                      <Label htmlFor="subject-select">Subject</Label>
                      <Select 
                        value={selectedSubject} 
                        onValueChange={handleSubjectChange}
                      >
                        <SelectTrigger className="w-[200px]">
                          <SelectValue>
                            {subjects.find(s => s.id === selectedSubject)?.name}
                          </SelectValue>
                        </SelectTrigger>
                        <SelectContent>
                          {subjects.map((subject) => (
                            <SelectItem key={subject.id} value={subject.id}>
                              {subject.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-1">
                      <Label htmlFor="period-select">Grading Period</Label>
                      <Select 
                        value={selectedPeriod} 
                        onValueChange={handlePeriodChange}
                      >
                        <SelectTrigger className="w-[200px]">
                          <SelectValue>
                            {gradingPeriods.find(p => p.id === selectedPeriod)?.name}
                          </SelectValue>
                        </SelectTrigger>
                        <SelectContent>
                          {gradingPeriods.map((period) => (
                            <SelectItem key={period.id} value={period.id}>
                              {period.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 bg-muted p-2 rounded-md">
                    <div>
                      <span className="text-xs text-muted-foreground">Class Average:</span>
                      <div className="text-xl font-bold">
                        {classAverage().toFixed(1)}
                        <span className="text-xs ml-1">/ 100</span>
                      </div>
                    </div>
                    <div className="h-8 w-8 rounded-full bg-promise-100 text-promise-800 flex items-center justify-center font-bold">
                      {getGradeInfo(classAverage()).letter}
                    </div>
                  </div>
                </div>

                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search students..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Student</TableHead>
                        <TableHead>Grade (%)</TableHead>
                        <TableHead>Letter Grade</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredStudents.map((student) => {
                        const currentGrade = student.grades[selectedSubject][selectedPeriod];
                        const gradeInfo = getGradeInfo(currentGrade);
                        
                        return (
                          <TableRow key={student.id}>
                            <TableCell>
                              <div className="flex items-center space-x-2">
                                <Avatar className="h-8 w-8">
                                  <AvatarImage src={`https://ui-avatars.com/api/?name=${student.avatar}&background=random`} />
                                  <AvatarFallback>{student.avatar}</AvatarFallback>
                                </Avatar>
                                <span className="font-medium">{student.name}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Input
                                type="number"
                                value={currentGrade === null ? "" : currentGrade}
                                onChange={(e) => handleGradeChange(student.id, e.target.value)}
                                min="0"
                                max="100"
                                className="w-20"
                              />
                            </TableCell>
                            <TableCell>
                              <div className={`text-center w-10 h-10 rounded-full flex items-center justify-center font-bold ${gradeInfo.color} bg-muted`}>
                                {gradeInfo.letter}
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="flex items-center gap-1"
                              >
                                <FileText className="h-4 w-4" />
                                Notes
                              </Button>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline">Reset</Button>
                <Button onClick={submitGrades}>
                  <Save className="mr-2 h-4 w-4" />
                  Submit Grades
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="reports" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Grade Reports</CardTitle>
                <CardDescription>View and analyze class performance</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-4">
                  <div className="space-y-1">
                    <Label htmlFor="report-subject">Subject</Label>
                    <Select value={selectedSubject} onValueChange={handleSubjectChange}>
                      <SelectTrigger className="w-[200px]">
                        <SelectValue>
                          {subjects.find(s => s.id === selectedSubject)?.name}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        {subjects.map((subject) => (
                          <SelectItem key={subject.id} value={subject.id}>
                            {subject.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  {/* Performance Card */}
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Class Performance</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {gradingPeriods.map((period) => {
                          // Calculate average for this period
                          const periodGrades = studentsWithGrades
                            .map(s => s.grades[selectedSubject][period.id])
                            .filter(g => g !== null);
                          
                          const avgGrade = periodGrades.length 
                            ? periodGrades.reduce((sum, g) => sum + g, 0) / periodGrades.length 
                            : 0;
                          
                          return (
                            <div key={period.id} className="space-y-1">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium">{period.name}</span>
                                <span className="text-sm font-medium">{avgGrade.toFixed(1)}</span>
                              </div>
                              <Progress value={avgGrade} className="h-2" />
                            </div>
                          );
                        })}
                      </div>
                    </CardContent>
                  </Card>
                  
                  {/* Distribution Card */}
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Grade Distribution</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {['A', 'B', 'C', 'D', 'F'].map((letter) => {
                          // Count students with this grade letter in the current period
                          const count = studentsWithGrades.filter(s => {
                            const grade = s.grades[selectedSubject][selectedPeriod];
                            if (grade === null) return false;
                            const letterGrade = getGradeInfo(grade).letter;
                            return letterGrade === letter;
                          }).length;
                          
                          const percentage = (count / studentsWithGrades.length) * 100;
                          
                          return (
                            <div key={letter} className="space-y-1">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                  <div className={`w-4 h-4 rounded-sm mr-2 ${
                                    letter === 'A' ? 'bg-green-500' :
                                    letter === 'B' ? 'bg-blue-500' :
                                    letter === 'C' ? 'bg-yellow-500' :
                                    letter === 'D' ? 'bg-orange-500' :
                                    'bg-red-500'
                                  }`}></div>
                                  <span className="text-sm">{letter} Grade</span>
                                </div>
                                <span className="text-sm">{count} students</span>
                              </div>
                              <div className="w-full bg-muted rounded-full h-2">
                                <div 
                                  className={`h-2 rounded-full ${
                                    letter === 'A' ? 'bg-green-500' :
                                    letter === 'B' ? 'bg-blue-500' :
                                    letter === 'C' ? 'bg-yellow-500' :
                                    letter === 'D' ? 'bg-orange-500' :
                                    'bg-red-500'
                                  }`}
                                  style={{ width: `${percentage}%` }}
                                ></div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
              <CardFooter>
                <Button className="w-full">
                  Generate Complete Report
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default GradingManagement;
