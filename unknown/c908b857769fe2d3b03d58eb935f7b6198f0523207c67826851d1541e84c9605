import { useState } from "react";
import DashboardLayout from "../DashboardLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { 
  Heart, 
  Search, 
  AlertCircle, 
  Check, 
  X, 
  MessageSquare, 
  FileText, 
  Calendar
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";

// Sample health reports data
const healthReports = [
  {
    id: "HR001",
    student: {
      id: "STU001",
      name: "Emma Thompson",
      avatar: "ET",
      class: "Pre-K (4 years)",
      teacher: "Ms. Davis"
    },
    reportType: "illness",
    status: "pending",
    reportedBy: "teacher", // can be teacher or parent
    date: "2025-05-17",
    symptoms: ["Fever", "Cough"],
    notes: "Student complained of headache and had a temperature of 100.4°F. Parent has been contacted for pickup.",
    parentNotified: true,
    actionTaken: ""
  },
  {
    id: "HR002",
    student: {
      id: "STU002",
      name: "Liam Johnson",
      avatar: "LJ",
      class: "Pre-K (3 years)",
      teacher: "Ms. Rodriguez"
    },
    reportType: "injury",
    status: "processed",
    reportedBy: "teacher",
    date: "2025-05-16",
    symptoms: ["Minor scrape"],
    notes: "Student tripped during outdoor play and scraped knee. Wound was cleaned and bandaged.",
    parentNotified: true,
    actionTaken: "First aid applied. Student returned to class after treatment."
  },
  {
    id: "HR003",
    student: {
      id: "STU003",
      name: "Olivia Davis",
      avatar: "OD",
      class: "Pre-K (4 years)",
      teacher: "Ms. Davis"
    },
    reportType: "absence",
    status: "processed",
    reportedBy: "parent",
    date: "2025-05-15",
    symptoms: ["Stomach flu"],
    notes: "Parent reported child has stomach flu and will be absent for 2-3 days per doctor's recommendation.",
    parentNotified: false,
    actionTaken: "Marked as excused absence. Materials will be sent home."
  },
  {
    id: "HR004",
    student: {
      id: "STU004",
      name: "Noah Brown",
      avatar: "NB",
      class: "Pre-K (4 years)",
      teacher: "Ms. Davis"
    },
    reportType: "allergy",
    status: "pending",
    reportedBy: "teacher",
    date: "2025-05-17",
    symptoms: ["Rash", "Itching"],
    notes: "Student developed a rash after snack time. No breathing issues observed. No known allergies in records.",
    parentNotified: true,
    actionTaken: ""
  },
  {
    id: "HR005",
    student: {
      id: "STU005",
      name: "Ava Martinez",
      avatar: "AM",
      class: "Pre-K (3 years)",
      teacher: "Ms. Rodriguez"
    },
    reportType: "illness",
    status: "pending",
    reportedBy: "parent",
    date: "2025-05-17",
    symptoms: ["Cold symptoms"],
    notes: "Parent reports child has a runny nose and slight cough but no fever. Requesting to be informed if symptoms worsen.",
    parentNotified: false,
    actionTaken: ""
  },
];

// Sample health conditions data
const healthConditions = [
  {
    studentId: "STU001",
    allergies: ["None reported"],
    medications: ["None"],
    conditions: ["None"],
    emergencyContact: "Michael Thompson (Father) - 555-0123",
    notes: "No specific health concerns"
  },
  {
    studentId: "STU002",
    allergies: ["Peanuts - Severe"],
    medications: ["EpiPen - as needed for allergic reaction"],
    conditions: ["Food allergy"],
    emergencyContact: "David Johnson (Father) - 555-0124",
    notes: "Requires EpiPen for severe peanut allergy. School has one stored in nurse's office."
  },
  {
    studentId: "STU003",
    allergies: ["None reported"],
    medications: ["None"],
    conditions: ["Asthma - Mild"],
    emergencyContact: "Emily Williams (Mother) - 555-0125",
    notes: "Mild asthma, rarely requires inhaler but has one in backpack for emergencies."
  },
  {
    studentId: "STU004",
    allergies: ["None reported"],
    medications: ["None"],
    conditions: ["None"],
    emergencyContact: "Daniel Brown (Father) - 555-0126",
    notes: "No specific health concerns"
  },
  {
    studentId: "STU005",
    allergies: ["Seasonal - Mild"],
    medications: ["Antihistamine - as needed"],
    conditions: ["Seasonal allergies"],
    emergencyContact: "Isabella Davis (Mother) - 555-0127",
    notes: "May need antihistamine during spring pollen season."
  },
];

const HealthManagement = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedReport, setSelectedReport] = useState(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [actionNotes, setActionNotes] = useState("");
  const { toast } = useToast();

  // Filter health reports based on search term
  const filteredReports = healthReports.filter(report => 
    report.student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    report.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    report.reportType.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Group reports by status
  const pendingReports = filteredReports.filter(report => report.status === "pending");
  const processedReports = filteredReports.filter(report => report.status === "processed");

  const openReportDetails = (report) => {
    setSelectedReport(report);
    setIsDialogOpen(true);
  };

  const handleProcessReport = (e) => {
    e.preventDefault();
    toast({
      title: "Health Report Processed",
      description: `Action has been recorded for report ${selectedReport.id}.`,
    });
    setIsDialogOpen(false);
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const getStudentHealthInfo = (studentId) => {
    return healthConditions.find(info => info.studentId === studentId) || null;
  };

  return (
    <DashboardLayout role="admin" title="Health Management">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold tracking-tight">Health Reports</h2>
        </div>

        <Tabs defaultValue="pending">
          <TabsList>
            <TabsTrigger value="pending">
              Pending Actions
              <Badge variant="secondary" className="ml-2">{pendingReports.length}</Badge>
            </TabsTrigger>
            <TabsTrigger value="processed">Processed Reports</TabsTrigger>
            <TabsTrigger value="conditions">Health Conditions</TabsTrigger>
          </TabsList>
          
          <TabsContent value="pending" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Reports Requiring Action</CardTitle>
                <CardDescription>Health incidents that need review and response</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search reports..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={handleSearch}
                  />
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Report ID</TableHead>
                        <TableHead>Student</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Reported By</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {pendingReports.length > 0 ? (
                        pendingReports.map((report) => (
                          <TableRow key={report.id}>
                            <TableCell className="font-medium">{report.id}</TableCell>
                            <TableCell>
                              <div className="flex items-center space-x-2">
                                <Avatar className="h-8 w-8">
                                  <AvatarImage src={`https://ui-avatars.com/api/?name=${report.student.avatar}&background=random`} />
                                  <AvatarFallback>{report.student.avatar}</AvatarFallback>
                                </Avatar>
                                <span>{report.student.name}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant={
                                report.reportType === "illness" ? "default" :
                                report.reportType === "injury" ? "destructive" :
                                report.reportType === "allergy" ? "secondary" : "outline"
                              }>
                                {report.reportType.charAt(0).toUpperCase() + report.reportType.slice(1)}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center space-x-2">
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                                <span>{report.date}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              {report.reportedBy === "teacher" ? "Teacher" : "Parent"}
                            </TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="ghost"
                                onClick={() => openReportDetails(report)}
                              >
                                View Details
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
                            No pending health reports found
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="processed" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Processed Health Reports</CardTitle>
                <CardDescription>Previously handled health incidents</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search reports..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={handleSearch}
                  />
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Report ID</TableHead>
                        <TableHead>Student</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Action Taken</TableHead>
                        <TableHead className="text-right">Details</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {processedReports.length > 0 ? (
                        processedReports.map((report) => (
                          <TableRow key={report.id}>
                            <TableCell className="font-medium">{report.id}</TableCell>
                            <TableCell>
                              <div className="flex items-center space-x-2">
                                <Avatar className="h-8 w-8">
                                  <AvatarImage src={`https://ui-avatars.com/api/?name=${report.student.avatar}&background=random`} />
                                  <AvatarFallback>{report.student.avatar}</AvatarFallback>
                                </Avatar>
                                <span>{report.student.name}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant={
                                report.reportType === "illness" ? "default" :
                                report.reportType === "injury" ? "destructive" :
                                report.reportType === "allergy" ? "secondary" : "outline"
                              }>
                                {report.reportType.charAt(0).toUpperCase() + report.reportType.slice(1)}
                              </Badge>
                            </TableCell>
                            <TableCell>{report.date}</TableCell>
                            <TableCell>
                              <span className="line-clamp-1">{report.actionTaken}</span>
                            </TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="ghost"
                                onClick={() => openReportDetails(report)}
                              >
                                View
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
                            No processed health reports found
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="conditions" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Student Health Conditions</CardTitle>
                <CardDescription>Overview of student health information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search students..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={handleSearch}
                  />
                </div>

                {healthReports
                  .filter((report, index, self) => 
                    // Filter for unique students
                    index === self.findIndex((r) => r.student.id === report.student.id) &&
                    // Apply search term
                    report.student.name.toLowerCase().includes(searchTerm.toLowerCase())
                  )
                  .map(report => {
                    const healthInfo = getStudentHealthInfo(report.student.id);
                    return (
                      <Card key={report.student.id} className="overflow-hidden">
                        <CardHeader className="bg-muted/50">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <Avatar className="h-10 w-10">
                                <AvatarImage src={`https://ui-avatars.com/api/?name=${report.student.avatar}&background=random`} />
                                <AvatarFallback>{report.student.avatar}</AvatarFallback>
                              </Avatar>
                              <div>
                                <CardTitle>{report.student.name}</CardTitle>
                                <CardDescription>{report.student.class} | Teacher: {report.student.teacher}</CardDescription>
                              </div>
                            </div>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <FileText className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem>View Full Medical Record</DropdownMenuItem>
                                <DropdownMenuItem>Update Health Information</DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>Contact Parent</DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </CardHeader>
                        <CardContent className="pt-4">
                          {healthInfo ? (
                            <div className="space-y-4">
                              <div>
                                <h4 className="text-sm font-semibold mb-1">Allergies</h4>
                                <div className="flex flex-wrap gap-1">
                                  {healthInfo.allergies.map((allergy, idx) => (
                                    <Badge 
                                      key={idx}
                                      variant={allergy.toLowerCase().includes('severe') ? "destructive" : "outline"}
                                    >
                                      {allergy}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                              
                              <div>
                                <h4 className="text-sm font-semibold mb-1">Medical Conditions</h4>
                                <div className="flex flex-wrap gap-1">
                                  {healthInfo.conditions.map((condition, idx) => (
                                    <Badge key={idx} variant="outline">
                                      {condition}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                              
                              <div>
                                <h4 className="text-sm font-semibold mb-1">Medications</h4>
                                <p className="text-sm text-muted-foreground">{healthInfo.medications.join(", ")}</p>
                              </div>
                              
                              <div>
                                <h4 className="text-sm font-semibold mb-1">Emergency Contact</h4>
                                <p className="text-sm">{healthInfo.emergencyContact}</p>
                              </div>
                              
                              {healthInfo.notes && (
                                <div>
                                  <h4 className="text-sm font-semibold mb-1">Additional Notes</h4>
                                  <p className="text-sm text-muted-foreground">{healthInfo.notes}</p>
                                </div>
                              )}
                            </div>
                          ) : (
                            <p className="text-muted-foreground">No health information available</p>
                          )}
                        </CardContent>
                      </Card>
                    );
                  })}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
        
        {/* Health Report Detail Dialog */}
        {selectedReport && (
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Health Report Details</DialogTitle>
                <DialogDescription>
                  {selectedReport.id} - {selectedReport.date}
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={`https://ui-avatars.com/api/?name=${selectedReport.student.avatar}&background=random`} />
                      <AvatarFallback>{selectedReport.student.avatar}</AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-semibold">{selectedReport.student.name}</h3>
                      <p className="text-sm text-muted-foreground">{selectedReport.student.class} | {selectedReport.student.teacher}</p>
                    </div>
                  </div>
                  
                  <Badge variant={
                    selectedReport.reportType === "illness" ? "default" :
                    selectedReport.reportType === "injury" ? "destructive" :
                    selectedReport.reportType === "allergy" ? "secondary" : "outline"
                  }>
                    {selectedReport.reportType.charAt(0).toUpperCase() + selectedReport.reportType.slice(1)}
                  </Badge>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="text-sm font-semibold mb-1">Reported By</h4>
                    <p>{selectedReport.reportedBy === "teacher" ? "Teacher" : "Parent"}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-semibold mb-1">Parent Notified</h4>
                    <p className="flex items-center">
                      {selectedReport.parentNotified ? (
                        <>
                          <Check className="h-4 w-4 text-green-600 mr-1" /> Yes
                        </>
                      ) : (
                        <>
                          <X className="h-4 w-4 text-red-600 mr-1" /> No
                        </>
                      )}
                    </p>
                  </div>
                </div>
                
                <div>
                  <h4 className="text-sm font-semibold mb-1">Symptoms/Issues</h4>
                  <div className="flex flex-wrap gap-1 mb-2">
                    {selectedReport.symptoms.map((symptom, idx) => (
                      <Badge key={idx} variant="outline">
                        {symptom}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                <div className="border rounded-md p-3 bg-muted/50">
                  <h4 className="text-sm font-semibold mb-1 flex items-center">
                    <MessageSquare className="h-4 w-4 mr-1" /> Report Notes
                  </h4>
                  <p className="text-sm whitespace-pre-line">{selectedReport.notes}</p>
                </div>
                
                {selectedReport.actionTaken && (
                  <div className="border rounded-md p-3 bg-green-50">
                    <h4 className="text-sm font-semibold mb-1 flex items-center text-green-800">
                      <Check className="h-4 w-4 mr-1" /> Action Taken
                    </h4>
                    <p className="text-sm whitespace-pre-line">{selectedReport.actionTaken}</p>
                  </div>
                )}
                
                {selectedReport.status === "pending" && (
                  <form className="space-y-4" onSubmit={handleProcessReport}>
                    <div>
                      <Label htmlFor="action">Record Action</Label>
                      <Textarea 
                        id="action" 
                        placeholder="Describe the action taken for this health report..." 
                        value={actionNotes}
                        onChange={(e) => setActionNotes(e.target.value)}
                        className="min-h-[100px]"
                      />
                    </div>
                    
                    <DialogFooter>
                      <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                        Cancel
                      </Button>
                      <Button type="submit" disabled={!actionNotes.trim()}>
                        Process Report
                      </Button>
                    </DialogFooter>
                  </form>
                )}
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>
    </DashboardLayout>
  );
};

export default HealthManagement;
