
import { useState } from "react";
import DashboardLayout from "../DashboardLayout";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Book, Search, FileText, Download, Pencil, Filter } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";

// Sample data for classrooms
const classrooms = [
  { id: "CLASS001", name: "<PERSON>fant<PERSON> (6-18 months)", teacher: "<PERSON><PERSON> <PERSON>" },
  { id: "CLASS002", name: "Toddlers (18-36 months)", teacher: "Mr. <PERSON>" },
  { id: "CLASS003", name: "Pre-K (3 years)", teacher: "Ms. Rodriguez" },
  { id: "CLASS004", name: "Pre-K (4 years)", teacher: "Ms. Davis" },
  { id: "CLASS005", name: "Kindergarten (5 years)", teacher: "Mr. Martinez" },
  { id: "CLASS006", name: "Mixed Age (3-5 years)", teacher: "Ms. Wilson" },
];

// Sample subjects
const subjects = [
  { id: "sub1", name: "Language & Literacy" },
  { id: "sub2", name: "Mathematics" },
  { id: "sub3", name: "Science & Discovery" },
  { id: "sub4", name: "Social Development" },
  { id: "sub5", name: "Fine Motor Skills" },
];

// Sample grading periods
const gradingPeriods = [
  { id: "q1", name: "Q1 (Aug-Oct)" },
  { id: "q2", name: "Q2 (Nov-Jan)" },
  { id: "q3", name: "Q3 (Feb-Apr)" },
  { id: "q4", name: "Q4 (May-Jul)" },
];

// Sample classroom grades
const classroomGrades = {
  "CLASS001": {
    "sub1": { q1: 87, q2: 88, q3: 90, q4: null },
    "sub2": { q1: 85, q2: 86, q3: 87, q4: null },
    "sub3": { q1: 88, q2: 90, q3: 91, q4: null },
    "sub4": { q1: 92, q2: 91, q3: 93, q4: null },
    "sub5": { q1: 86, q2: 87, q3: 89, q4: null },
  },
  "CLASS002": {
    "sub1": { q1: 85, q2: 86, q3: 88, q4: null },
    "sub2": { q1: 87, q2: 89, q3: 90, q4: null },
    "sub3": { q1: 84, q2: 86, q3: 89, q4: null },
    "sub4": { q1: 90, q2: 91, q3: 93, q4: null },
    "sub5": { q1: 85, q2: 86, q3: 88, q4: null },
  },
  "CLASS003": {
    "sub1": { q1: 88, q2: 90, q3: 92, q4: null },
    "sub2": { q1: 86, q2: 88, q3: 91, q4: null },
    "sub3": { q1: 89, q2: 90, q3: 92, q4: null },
    "sub4": { q1: 91, q2: 93, q3: 94, q4: null },
    "sub5": { q1: 87, q2: 89, q3: 91, q4: null },
  },
  "CLASS004": {
    "sub1": { q1: 92, q2: 93, q3: 95, q4: null },
    "sub2": { q1: 89, q2: 91, q3: 93, q4: null },
    "sub3": { q1: 90, q2: 92, q3: 94, q4: null },
    "sub4": { q1: 93, q2: 94, q3: 96, q4: null },
    "sub5": { q1: 88, q2: 90, q3: 92, q4: null },
  },
  "CLASS005": {
    "sub1": { q1: 90, q2: 91, q3: 93, q4: null },
    "sub2": { q1: 92, q2: 93, q3: 95, q4: null },
    "sub3": { q1: 88, q2: 89, q3: 91, q4: null },
    "sub4": { q1: 94, q2: 95, q3: 96, q4: null },
    "sub5": { q1: 89, q2: 90, q3: 92, q4: null },
  },
  "CLASS006": {
    "sub1": { q1: 86, q2: 88, q3: 90, q4: null },
    "sub2": { q1: 84, q2: 86, q3: 89, q4: null },
    "sub3": { q1: 85, q2: 87, q3: 90, q4: null },
    "sub4": { q1: 89, q2: 90, q3: 92, q4: null },
    "sub5": { q1: 83, q2: 85, q3: 88, q4: null },
  },
};

// Sample student grade details for a specific class
const studentsInClass = [
  {
    id: "STU001",
    name: "Emma Thompson",
    avatar: "ET",
    grades: {
      "sub1": { q1: 92, q2: 93, q3: 95, q4: null },
      "sub2": { q1: 89, q2: 90, q3: 92, q4: null },
      "sub3": { q1: 90, q2: 91, q3: 94, q4: null },
      "sub4": { q1: 95, q2: 96, q3: 97, q4: null },
      "sub5": { q1: 88, q2: 89, q3: 91, q4: null },
    }
  },
  {
    id: "STU002",
    name: "Liam Johnson",
    avatar: "LJ",
    grades: {
      "sub1": { q1: 87, q2: 89, q3: 91, q4: null },
      "sub2": { q1: 90, q2: 92, q3: 94, q4: null },
      "sub3": { q1: 85, q2: 87, q3: 89, q4: null },
      "sub4": { q1: 92, q2: 93, q3: 94, q4: null },
      "sub5": { q1: 89, q2: 90, q3: 91, q4: null },
    }
  },
  {
    id: "STU003",
    name: "Olivia Davis",
    avatar: "OD",
    grades: {
      "sub1": { q1: 94, q2: 95, q3: 97, q4: null },
      "sub2": { q1: 91, q2: 93, q3: 95, q4: null },
      "sub3": { q1: 92, q2: 94, q3: 96, q4: null },
      "sub4": { q1: 96, q2: 97, q3: 98, q4: null },
      "sub5": { q1: 93, q2: 94, q3: 95, q4: null },
    }
  },
];

const GradingManagement = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedClassroom, setSelectedClassroom] = useState(null);
  const [selectedPeriod, setSelectedPeriod] = useState(gradingPeriods[2].id);
  const [selectedSubject, setSelectedSubject] = useState(subjects[0].id);
  const { toast } = useToast();
  
  // Filter classrooms based on search term
  const filteredClassrooms = classrooms.filter(classroom =>
    classroom.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    classroom.teacher.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const viewClassDetails = (classroom) => {
    setSelectedClassroom(classroom);
  };

  const getLetterGrade = (score) => {
    if (score === null) return "N/A";
    if (score >= 90) return "A";
    if (score >= 80) return "B";
    if (score >= 70) return "C";
    if (score >= 60) return "D";
    return "F";
  };

  const getGradeColor = (score) => {
    if (score === null) return "text-gray-500";
    if (score >= 90) return "text-green-600";
    if (score >= 80) return "text-blue-600";
    if (score >= 70) return "text-yellow-600";
    if (score >= 60) return "text-orange-600";
    return "text-red-600";
  };

  const handleDownloadGrades = () => {
    toast({
      title: "Grades Downloaded",
      description: "The grade report has been downloaded successfully.",
    });
  };

  return (
    <DashboardLayout role="admin" title="Grading Management">
      <div className="space-y-6">
        {!selectedClassroom ? (
          <>
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold tracking-tight">Grade Reports</h2>
              <Button onClick={handleDownloadGrades}>
                <Download className="mr-2 h-4 w-4" />
                Export All Grades
              </Button>
            </div>

            <div className="flex items-center mb-4 gap-4">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search classrooms..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <div className="flex gap-2">
                <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select period" />
                  </SelectTrigger>
                  <SelectContent>
                    {gradingPeriods.map(period => (
                      <SelectItem key={period.id} value={period.id}>
                        {period.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Classroom Grade Overview</CardTitle>
                <CardDescription>Summary of class performance in {gradingPeriods.find(p => p.id === selectedPeriod)?.name}</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Classroom</TableHead>
                      <TableHead>Teacher</TableHead>
                      {subjects.map(subject => (
                        <TableHead key={subject.id}>{subject.name}</TableHead>
                      ))}
                      <TableHead>Average</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredClassrooms.map((classroom) => {
                      const classGrades = classroomGrades[classroom.id];
                      const subjectGrades = subjects.map(subject => classGrades[subject.id][selectedPeriod]);
                      const classAverage = subjectGrades.filter(g => g !== null).reduce((sum, grade) => sum + grade, 0) / 
                                          subjectGrades.filter(g => g !== null).length;
                      
                      return (
                        <TableRow key={classroom.id}>
                          <TableCell className="font-medium">{classroom.name}</TableCell>
                          <TableCell>{classroom.teacher}</TableCell>
                          {subjects.map(subject => (
                            <TableCell key={subject.id} className={getGradeColor(classGrades[subject.id][selectedPeriod])}>
                              {classGrades[subject.id][selectedPeriod] ?? "N/A"}
                              <span className="text-xs ml-1">
                                ({getLetterGrade(classGrades[subject.id][selectedPeriod])})
                              </span>
                            </TableCell>
                          ))}
                          <TableCell className={getGradeColor(classAverage)}>
                            <div className="flex items-center space-x-2">
                              <span className="font-medium">{classAverage.toFixed(1)}</span>
                              <span className="text-xs">({getLetterGrade(classAverage)})</span>
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button variant="ghost" size="sm" onClick={() => viewClassDetails(classroom)}>
                              <Pencil className="mr-2 h-4 w-4" />
                              View Details
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>School-Wide Performance</CardTitle>
                <CardDescription>Average grades across all classrooms by subject</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-8">
                  {subjects.map(subject => {
                    const allGrades = Object.values(classroomGrades).map(c => c[subject.id][selectedPeriod]).filter(g => g !== null);
                    const avgGrade = allGrades.reduce((sum, g) => sum + g, 0) / allGrades.length;
                    
                    return (
                      <div key={subject.id} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="font-medium">{subject.name}</h4>
                            <p className="text-sm text-muted-foreground">School average: {avgGrade.toFixed(1)}%</p>
                          </div>
                          <Badge variant="outline" className={getGradeColor(avgGrade)}>
                            {getLetterGrade(avgGrade)}
                          </Badge>
                        </div>
                        <Progress value={avgGrade} className="h-2" />
                        <div className="flex justify-between text-xs text-muted-foreground pt-1">
                          {gradingPeriods.map(period => (
                            <div key={period.id} className={period.id === selectedPeriod ? "font-semibold" : ""}>
                              {period.name.split(" ")[0]}
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">
                  <FileText className="mr-2 h-4 w-4" />
                  Generate Full Report
                </Button>
              </CardFooter>
            </Card>
          </>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Button variant="ghost" onClick={() => setSelectedClassroom(null)}>
                &larr; Back to All Classes
              </Button>
              <Button onClick={handleDownloadGrades}>
                <Download className="mr-2 h-4 w-4" />
                Export Class Grades
              </Button>
            </div>
            
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>{selectedClassroom.name} - Grades</CardTitle>
                    <CardDescription>Teacher: {selectedClassroom.teacher}</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-4">
                  <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Select period" />
                    </SelectTrigger>
                    <SelectContent>
                      {gradingPeriods.map(period => (
                        <SelectItem key={period.id} value={period.id}>
                          {period.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  
                  <Select value={selectedSubject} onValueChange={setSelectedSubject}>
                    <SelectTrigger className="w-[200px]">
                      <SelectValue placeholder="Select subject" />
                    </SelectTrigger>
                    <SelectContent>
                      {subjects.map(subject => (
                        <SelectItem key={subject.id} value={subject.id}>
                          {subject.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  
                  <Button variant="outline" size="icon">
                    <Filter className="h-4 w-4" />
                  </Button>
                </div>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">
                      {subjects.find(s => s.id === selectedSubject)?.name} - {gradingPeriods.find(p => p.id === selectedPeriod)?.name}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Student</TableHead>
                          <TableHead>Grade</TableHead>
                          <TableHead>Previous Period</TableHead>
                          <TableHead>Change</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {studentsInClass.map(student => {
                          const currentPeriodIdx = gradingPeriods.findIndex(p => p.id === selectedPeriod);
                          const prevPeriodId = currentPeriodIdx > 0 ? gradingPeriods[currentPeriodIdx - 1].id : null;
                          
                          const currentGrade = student.grades[selectedSubject][selectedPeriod];
                          const prevGrade = prevPeriodId ? student.grades[selectedSubject][prevPeriodId] : null;
                          
                          const change = prevGrade !== null && currentGrade !== null
                            ? currentGrade - prevGrade
                            : null;
                            
                          return (
                            <TableRow key={student.id}>
                              <TableCell>
                                <div className="flex items-center space-x-2">
                                  <Avatar className="h-8 w-8">
                                    <AvatarImage src={`https://ui-avatars.com/api/?name=${student.avatar}&background=random`} />
                                    <AvatarFallback>{student.avatar}</AvatarFallback>
                                  </Avatar>
                                  <span className="font-medium">{student.name}</span>
                                </div>
                              </TableCell>
                              <TableCell className={getGradeColor(currentGrade)}>
                                <div className="flex items-center space-x-1">
                                  <span className="font-bold">{currentGrade ?? "N/A"}</span>
                                  <span className="text-xs">({getLetterGrade(currentGrade)})</span>
                                </div>
                              </TableCell>
                              <TableCell className={getGradeColor(prevGrade)}>
                                {prevGrade !== null ? (
                                  <div className="flex items-center space-x-1">
                                    <span>{prevGrade}</span>
                                    <span className="text-xs">({getLetterGrade(prevGrade)})</span>
                                  </div>
                                ) : "N/A"}
                              </TableCell>
                              <TableCell>
                                {change !== null && (
                                  <Badge className={change > 0 ? "bg-green-100 text-green-800" : change < 0 ? "bg-red-100 text-red-800" : "bg-gray-100 text-gray-800"}>
                                    {change > 0 ? `+${change}` : change}
                                  </Badge>
                                )}
                              </TableCell>
                              <TableCell className="text-right">
                                <Button variant="ghost" size="sm">
                                  View History
                                </Button>
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Class Performance</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {subjects.map(subject => {
                          const grades = studentsInClass.map(s => s.grades[subject.id][selectedPeriod]).filter(g => g !== null);
                          const avg = grades.length ? grades.reduce((sum, g) => sum + g, 0) / grades.length : 0;
                          
                          return (
                            <div key={subject.id} className="space-y-1">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium">{subject.name}</span>
                                <span className="text-sm font-medium">{avg.toFixed(1)}</span>
                              </div>
                              <Progress value={avg} className="h-2" />
                            </div>
                          );
                        })}
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Student Progress Over Time</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {studentsInClass.map(student => {
                          const allGrades = Object.values(student.grades).flatMap(
                            periodGrades => Object.values(periodGrades).filter(g => g !== null)
                          );
                          const avgGrade = allGrades.length ? allGrades.reduce((sum, g) => sum + g, 0) / allGrades.length : 0;
                          
                          return (
                            <div key={student.id} className="space-y-1">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                  <Avatar className="h-6 w-6">
                                    <AvatarFallback>{student.avatar}</AvatarFallback>
                                  </Avatar>
                                  <span className="text-sm">{student.name}</span>
                                </div>
                                <span className="text-sm font-medium">{avgGrade.toFixed(1)}</span>
                              </div>
                              <Progress value={avgGrade} className="h-2" />
                            </div>
                          );
                        })}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default GradingManagement;
